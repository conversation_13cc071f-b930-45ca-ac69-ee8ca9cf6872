import React from "react";
import { useDispatch, useSelector } from "react-redux";
import { setSettingsData } from "../../store/slices/storiesSlice";
import { Input } from "../../parts/FormComponents";

const AdvancedSettings = () => {
  const dispatch = useDispatch();
  const { storiesState } = useSelector((state) => state.stories);
  const handleDataChange = ({ name, value }) => {
    dispatch(setSettingsData({ name, value }));
  };

  return (
    <div className="flex flex-col gap-y-4 mt-4">
      <Input
        label="Movie/Series Title"
        name="articleTitle"
        id="articleTitle"
        value={storiesState.articleTitle}
        placeholder="Add a story title here"
        onDebouncedChange={(value) =>
          handleDataChange({ value, name: "articleTitle" })
        }
        type="text"
      />
      <Input
        label="Subheading"
        name="articleSubheading"
        id="articleSubheading"
        value={storiesState.articleSubheading}
        placeholder="Add a story subheading here"
        onDebouncedChange={(value) =>
          handleDataChange({ value, name: "articleSubheading" })
        }
      />
      <Input
        label="Platform"
        name="platform"
        value={storiesState.platform}
        id="platform"
        placeholder="e.g. A cat sleeping on a white blanket"
        onDebouncedChange={(value) =>
          handleDataChange({ value, name: "platform" })
        }
      />
      <Input
        label="Views"
        name="views"
        value={storiesState.views}
        id="views"
        placeholder="Add a views of the show"
        onDebouncedChange={(value) =>
          handleDataChange({ value, name: "views" })
        }
      />
      <Input
        label="Week"
        name="week"
        id="week"
        value={storiesState.week}
        placeholder="Add a week for the show"
        onDebouncedChange={(value) => handleDataChange({ value, name: "week" })}
        type="number"
      />
      <Input
        label="Weekend Box Office Collection"
        name="weekendBoxOffice"
        id="weekendBoxOffice"
        value={storiesState.weekendBoxOffice}
        placeholder="Add a Weekend Box Office Collection"
        onDebouncedChange={(value) =>
          handleDataChange({ value, name: "weekendBoxOffice" })
        }
      />
      <Input
        label="Cumulative Box Office Collection"
        name="cumulativeBoxOffice"
        value={storiesState.cumulativeBoxOffice}
        id="cumulativeBoxOffice"
        placeholder="Add a Cumulative Box Office Collection"
        onDebouncedChange={(value) =>
          handleDataChange({ value, name: "cumulativeBoxOffice" })
        }
      />
    </div>
  );
};

export default AdvancedSettings;
