import React, { useEffect, useMemo, useState } from "react";
import Container from "../parts/Container";
import BreadCrumb from "../parts/BreadCrumb";
import Button, { RoundedIconsButton } from "../parts/Button";
import { BiChevronRight, BiPlus } from "react-icons/bi";
import Table from "../parts/Table";
import { useDispatch, useSelector } from "react-redux";
import { DropdownButton } from "../parts/FormComponents";
import Filters from "../parts/Filters";
import { BsEye } from "react-icons/bs";
import { FiEdit } from "react-icons/fi";
import { FaRegTrashAlt } from "react-icons/fa";
import {
  Link,
  useNavigate,
  useParams,
  useSearchParams,
} from "react-router-dom";
import {
  filterStoryStatus,
  incrementOffset,
  resetFilters,
  resetState,
  setFetchedData,
} from "../store/slices/storiesSlice";
import useInfiniteScrollData from "../utils/useInfiniteScrollData";
import { toast } from "react-toastify";
import ConfirmationModal from "../parts/ConfirmationModal";
import useConfirmationModal from "../utils/useConfirmationModal";
import {
  useDeleteStoryMutation,
  useUpdateStatusMutation,
} from "../store/apis/storiesApi";
import {
  formatDateAndTime,
  formatDateTime,
  handleViewClickInNewTab,
} from "../utils/helperFunctions";
import { CiImageOn } from "react-icons/ci";
import ScheduleModal from "../components/stories/ScheduleModal";
import { shallowEqual } from "react-redux";

const TrashStories = () => {
  const {
    user: { clientLink: frontendUrl },
  } = useSelector((state) => state.user);
  const navigate = useNavigate();
  const [isPublish, setIsPublish] = useState(false);
  const [tableRowId, setTableRowId] = useState(null);
  const dispatch = useDispatch();
  const [selectedRows, setSelectedRows] = useState([]);
  const { isModalOpen, rowIdToDelete, openModal, closeModal } =
    useConfirmationModal();
  const [deleteStory, { isLoading: isDeleting, error: deleteError }] =
    useDeleteStoryMutation();
  const [updateStatus] = useUpdateStatusMutation();
  const [searchParams] = useSearchParams();
  const { addFilter, showFilters, tag, category, writer, publishedTime } =
    useSelector((state) => state.table);

  // config for the stories api
  const storiesApiConfig = {
    setDataAction: setFetchedData,
    resetDataAction: resetFilters,
    sliceName: "stories",
  };

  const {
    data,
    isLoading,
    offset,
    filter,
    isError,
    error,
    fetchData,
    isFetching,
  } = useInfiniteScrollData({
    config: storiesApiConfig,
      url: "/api/article/list",
    showOnlyTrashed: true,
  });

  // handling the error here
  if (isError) {
    if (error.status === 401) {
      toast.error("Session Expired", { toastId: "text_story_fetch_error" });
      navigate("/signin");
    }
  }

  // Load data when filters change
  useEffect(() => {
    fetchData();
  }, [
    offset,
    filter.status,
    filter.search,
    tag,
    writer,
    category,
    publishedTime,
  ]);

  //resetting the filters on the intital load
  useEffect(() => {
   return(()=>  dispatch(resetFilters()))
  }, []);

  useEffect(() => {
    dispatch(filterStoryStatus(searchParams.get("status")));
  }, [searchParams.get("status")]);

  // increase the offset when user has scrolled till the last row of the table which in turn fethes the data
  const fetchMoreData = () => {
    dispatch(incrementOffset());
  };

  const handleViewClick = (link) => {
    const url = `${frontendUrl}${link}`;
    window.open(url, "_blank", "noopener,noreferrer");
  };

  // used to delete the story
  const handleDelete = () => {
    deleteStory(rowIdToDelete)
      .then((res) => {
        if (res.data.status === "success") {
          toast.success("Story deleted successfully!");
          fetchData(true);
        } else {
          toast.error("Failed to delete story.");
        }
      })
      .catch((err) => {
        toast.error("Failed to delete story.");
        console.log(err);
      })
      .finally(() => {
        closeModal();
      });
  };

  const updateStatusFunction = (status, rowId, publishDate = null) => {
    updateStatus({ status: status, id: rowId, publishDate })
      .then((res) => {
        if (res.data.status === "success") {
          toast.success("Story status updated successfully!");
          fetchData(true);
        } else {
          toast.error("Failed to update story status.");
        }
      })
      .catch((err) => {
        toast.error("Failed to update story status.");
        console.log(err);
      });
  };
  const onScheduleModalClose = (publishDate) => {
    if (publishDate && tableRowId) {
      updateStatusFunction(4, tableRowId, publishDate);
    }
  };

  const handleChange = ({ value, id }) => {
    // console.log(value, id);
    if (value === 4) {
      setIsPublish(true);
      setTableRowId(id);
    }
    if (value === 4) {
      setIsPublish(true);
    } else {
      updateStatusFunction(value, id);
    }
  };
  const columns = [
    // {
    //   accessorKey: "_id",
    //   enableSorting: false,
    //   header: ({ table }) => (
    //     <input
    //       type="checkbox"
    //       checked={table.getIsAllRowsSelected()}
    //       indeterminate={table.getIsSomeRowsSelected()}
    //       onChange={table.getToggleAllRowsSelectedHandler()} //or getToggleAllPageRowsSelectedHandler
    //     />
    //   ),
    //   cell: ({ row }) => (
    //     <input
    //       type="checkbox"
    //       checked={row.getIsSelected()}
    //       disabled={!row.getCanSelect()}
    //       onChange={row.getToggleSelectedHandler()}
    //     />
    //   ),
    // },
    {
      accessorKey: "title",
      id: "title",
      size: 450,
      header: () => "Text Story Details",
      cell: ({ row }) => {
        return (
          <div className="flex items-center gap-x-2 font-semibold">
            {row.original.coverImg ? (
              <img
                src={row.original.coverImg}
                alt=""
                className="w-[80px] h-[60px] object-cover rounded"
              />
            ) : (
              <div className="w-[80px] h-[60px] object-cover rounded bg-[#daeffe] flex items-center justify-center">
                <div className="w-[40px] h-[40px] rounded-full bg-white flex items-center justify-center">
                  <CiImageOn className="text-[#3b82f6] text-2xl m-auto" />
                </div>
              </div>
            )}
            <div>
              <div className="line-clamp-2">{row.original.title}</div>
              <div className="text-[#969696] text-xs font-normal flex items-center space-x-2">
                <span>{formatDateAndTime(row.original?.timestamp)},</span>
                <span>{row.original.author.toString()}</span>
              </div>
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: "category",
      header: "Category",
      minSize: 150,
    },
    {
      accessorKey: "tag",
      header: () => "Tags",
      size: 150,
      minSize: 100,
      maxSize: 200,
      cell: ({ row }) => (
        <div className="line-clamp-3">{row.original.tag.join(", ")}</div>
      ),
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => (
        <>
          <DropdownButton
            selected={row.original.status}
            handleChange={(value) =>
              handleChange({ value, id: row.original._id })
            }
          />
          {row.original.status === 4 ? (
            <div className="text-xs mt-1 text-gray-600">
              {formatDateTime(row.original?.publishDate)}
            </div>
          ) : null}
        </>
      ),
    },
    {
      accessorKey: "timestamp",
      header: "Action",
      cell: ({ row }) => {
        return (
          <div className="flex items-center gap-x-2">
            <RoundedIconsButton
              onClick={() => handleViewClick(row.original?.viewLink)}
            >
              <BsEye className="h-[15px] w-[15px]" />
            </RoundedIconsButton>

            <RoundedIconsButton
              onClick={() => {
                // dispatch(resetState());
                handleViewClickInNewTab(
                  `/admin/stories/edit/${row.original._id}?location=stories`
                );
              }}
            >
              <FiEdit className="h-[15px] w-[15px]" />
            </RoundedIconsButton>
            <RoundedIconsButton
              onClick={() => {
                openModal(row.original._id);
              }}
            >
              <FaRegTrashAlt className="h-[15px] w-[15px]" />
            </RoundedIconsButton>
          </div>
        );
      },
    },
  ];

  // funtion to perfrom action on row selection in the table
  const handleRowSelectionChange = (rowIds) => {
    console.log(rowIds, " rows ids seelcted");
  };

  // used to perform actions when a row is selected
  const handleRowSelect = (rows) => {
    setSelectedRows(rows);
    // Make API call or perform other actions with the selected rows
  };

  // used to handle the filter change on the table for the status
  const handleFilterChange = (filterName, value) => {
    console.log(filterName, value, " filter names and value");
    // Update filter state and fetch new data
  };

  return (
    <Container>
      {isPublish ? (
        <ScheduleModal
          setIsPublish={setIsPublish}
          type={"stories"}
          onClose={onScheduleModalClose}
          setStatus={false}
        />
      ) : null}
      <div className="lg:flex items-center justify-between mb-5">
        <div>
          {/* <div className="flex items-center text-sm">
            <Link
              to={"/admin/text-stories?status=all"}
              onClick={() =>
                dispatch(filterStoryStatus(searchParams.get("status")))
              }
              className="hover:bg-white text-blackShade rounded-full px-3 py-1 bg-transparent "
            >
              Text Stories
            </Link>
            <BiChevronRight className="text-xl" />
            <p className="text-fadeGray pl-2 capitalize">
              {searchParams.get("status")}
            </p>
          </div> */}
          <BreadCrumb
            title={"Trash Stories"}
            description={"Manage your stories."}
          />
        </div>
        <Button
          rounded="full"
          size="sm"
          customClasses="mt-2 lg:mt-0 pb-1.5 pt-1.5"
          onClick={() => {
            handleViewClickInNewTab("/admin/stories/create?location=stories");
            //navigate("/admin/stories/create?location=stories");
            //dispatch(resetState());
          }}
        >
          <BiPlus /> <span>Create Stories</span>
        </Button>
      </div>
      <Table
        module="stories"
              data={data}
              showOnlyAll={true}
        isFetching={isFetching}
        actionColumn={5}
        isLoading={isLoading}
        columns={columns}
        enableRowSelection={true}
        enableMultiRowSelection={true}
        handleRowSelectionChange={handleRowSelectionChange}
        fetchMoreData={fetchMoreData}
        customClass={""}
      />
      <Filters />
      <ConfirmationModal
        isOpen={isModalOpen}
        isLoading={isDeleting}
        toggleModal={closeModal}
        message="Are you sure you want to delete this story?"
        onConfirm={handleDelete}
      />
    </Container>
  );
};

export default TrashStories;
