import React, { useRef, useState } from "react";

import { EditorContent } from "@tiptap/react";
import TextEditorToolbar from "./TextEditorToolbar";
import InlineToolbar from "../editor/InlineToolbar";
import ImageBlockMenu from "../../helpers/extension/Image/components/ImageBlockMenu";
import VideoBlockMenu from "../../helpers/extension/Video/components/VideoBlockMenu";
import AstroBlockMenu from "../../helpers/extension/Astro/components/AstroBlockMenu";
import LinkMenu from "../../helpers/extension/Link/components/LinkMenu";
import EmbedPopup from "../../helpers/extension/Embed/components/EmbedPopup";
import RelatedPostsPopup from "../../helpers/extension/RelatedPosts/components/RelatedPostsPopup";
import { useDispatch, useSelector } from "react-redux";
import EmbedMenu from "../../helpers/extension/Embed/components/EmbedBlockMenu";
import RelatedPostsMenu from "../../helpers/extension/RelatedPosts/components/RelatedPostsMenu";
import {
	setArticleTitle,
	setErrors,
	setMetaData,
	setOgContent,
	setXContent,
} from "../../store/slices/storiesSlice";
import { generateSlugStories } from "../../utils/helperFunctions";
import GalleryMenu from "../../helpers/extension/Gallery/components/GalleryMenu";

//need to set the title and the editor text area

const TextEditor = ({ method, editor }) => {
	const [showLink, setShowLink] = useState(false);
	const {
		showAstro,
		showEmbed,
		showRelatedPosts,
		storiesState: { title },
		errors,
		isMetaTitleActive,
	} = useSelector((state) => state.stories);

	const menuContainerRef = useRef(null);
	const dispatch = useDispatch();

	return (
		<div ref={menuContainerRef}>
			<div className="w-full h-16 fixed top-0 left-0 border-b z-10 bg-white ">
				<TextEditorToolbar
					method={method}
					editor={editor}
					linkOpen={showLink}
					setLinkOpen={setShowLink}
				/>
			</div>
			<div className="editor-js-cont tiptap-editor-container mt-16 relative">
				<div className="editor-js-title">
					<textarea
						onChange={(e) => {
							dispatch(setArticleTitle(e.target.value));
							if (method === "POST") {
								dispatch(
									setMetaData({
										name: "slug",
										value: generateSlugStories(e.target.value),
									})
								);
							}
							if (!isMetaTitleActive) {
								dispatch(
									setMetaData({
										name: "title",
										value: e.target.value,
									})
								);
							}
							dispatch(
								setOgContent({
									name: "title",
									value: e.target.value,
								})
							);
							dispatch(
								setXContent({
									name: "title",
									value: e.target.value,
								})
							);
							dispatch(setErrors({ ...errors, title: null }));
						}}
						className="editor-js-input"
						placeholder="Add a Catchy Title"
						name="title"
						value={title || ""}
					/>
				</div>
				<InlineToolbar editor={editor} />
				<EditorContent editor={editor} />
				<ImageBlockMenu editor={editor} appendTo={menuContainerRef} />
				<VideoBlockMenu editor={editor} appendTo={menuContainerRef} />
				<AstroBlockMenu editor={editor} showAstroDrawer={showAstro} appendTo={menuContainerRef} />
				<EmbedMenu editor={editor} appendTo={menuContainerRef} />
				<RelatedPostsMenu editor={editor} appendTo={menuContainerRef} />
				<GalleryMenu editor={editor} appendTo={menuContainerRef} />
				<LinkMenu
					editor={editor}
					appendTo={menuContainerRef}
					showLink={showLink}
					setShowLink={setShowLink}
				/>
				{showEmbed && <EmbedPopup editor={editor} appendTo={menuContainerRef} />}
				<div className="fixed bottom-10 right-10  bg-primaryLight text-white px-5 text-sm py-1 rounded z-10">
					Word Count: {editor?.storage?.characterCount?.words() || 0}
				</div>

				{showRelatedPosts && <RelatedPostsPopup editor={editor} appendTo={menuContainerRef} />}
			</div>
		</div>
	);
};

export default TextEditor;
