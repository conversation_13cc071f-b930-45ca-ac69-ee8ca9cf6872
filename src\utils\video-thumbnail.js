import { useCallback } from "react";

export const generateVideoThumbnail = (videoFile) => {
  return new Promise((resolve, reject) => {
    const video = document.createElement("video");
    const videoUrl = URL.createObjectURL(videoFile);

    // Set video attributes for better compatibility
    video.preload = "metadata";
    video.playsInline = true;
    video.muted = true;

    // Track loading state
    let isLoaded = false;

    const cleanup = () => {
      video.pause();
      video.removeAttribute("src");
      video.load();
      URL.revokeObjectURL(videoUrl);
    };

    const generateThumbnail = () => {
      try {
        const canvas = document.createElement("canvas");
        canvas.width = video.videoWidth;
        canvas.height = video.videoHeight;
        const ctx = canvas.getContext("2d");

        // Clear canvas before drawing
        ctx.fillStyle = "#ffffff";
        ctx.fillRect(0, 0, canvas.width, canvas.height);

        // Draw video frame
        ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

        // Check if the canvas is blank or black
        const imageData = ctx.getImageData(
          0,
          0,
          canvas.width,
          canvas.height
        ).data;
        const isBlank = imageData.every((pixel, i) => {
          // Check if pixel is black or transparent
          return i % 4 === 3 ? pixel === 0 : pixel < 5;
        });

        if (isBlank) {
          throw new Error("Generated thumbnail is blank");
        }

        canvas.toBlob(
          (blob) => {
            if (!blob || blob.size === 0) {
              cleanup();
              reject(new Error("Failed to generate thumbnail blob"));
              return;
            }

            const thumbnailFile = new File([blob], "thumbnail.jpg", {
              type: "image/jpeg",
            });
            cleanup();
            resolve(thumbnailFile);
          },
          "image/jpeg",
          0.95 // Higher quality
        );
      } catch (error) {
        cleanup();
        reject(error);
      }
    };

    // Multiple event listeners for better compatibility
    video.addEventListener("loadeddata", () => {
      isLoaded = true;
      // Wait a short moment before seeking to ensure video is properly loaded
      setTimeout(() => {
        // Try to seek to 25% of the video duration for potentially better thumbnail
        if (video.duration) {
          video.currentTime = Math.min(video.duration * 0.25, 3.0);
        } else {
          video.currentTime = 1.0;
        }
      }, 100);
    });

    video.addEventListener("seeked", () => {
      if (isLoaded) {
        // Add a small delay to ensure frame is fully rendered
        setTimeout(generateThumbnail, 50);
      }
    });

    // Error handling
    video.addEventListener("error", (e) => {
      cleanup();
      reject(
        new Error(
          `Video loading failed: ${video.error?.message || "Unknown error"}`
        )
      );
    });

    // Timeout for loading
    const timeoutId = setTimeout(() => {
      cleanup();
      reject(new Error("Thumbnail generation timed out"));
    }, 15000); // 15 second timeout

    // Set video source and start loading
    video.src = videoUrl;

    // Some browsers need play() to properly load the video
    video.play().catch((error) => {
      // Ignore play() errors as we only need it for loading
    });
  });
};

// Usage example with error handling
export const handleVideoThumbnail = async (videoFile) => {
  try {
    const maxAttempts = 3;
    let attempt = 0;
    let thumbnailFile = null;
    let lastError = null;

    while (attempt < maxAttempts && !thumbnailFile) {
      try {
        thumbnailFile = await generateVideoThumbnail(videoFile);
      } catch (error) {
        lastError = error;
        attempt++;
        // Wait before retrying
        if (attempt < maxAttempts) {
          await new Promise((resolve) => setTimeout(resolve, 1000));
        }
      }
    }

    if (!thumbnailFile) {
      throw (
        lastError ||
        new Error("Failed to generate thumbnail after multiple attempts")
      );
    }

    return thumbnailFile;
  } catch (error) {
    console.error("Thumbnail generation failed:", error);
    // You might want to dispatch an error action here
    throw error;
  }
};
