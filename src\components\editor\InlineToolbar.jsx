import React, { useEffect, useState } from "react";
import { FloatingMenu } from "@tiptap/react";
import { useDispatch } from "react-redux";
import {
  setShowSideStoryTab,
  setStoryTab,
} from "../../store/slices/storiesSlice";
import { HiPlus } from "react-icons/hi";

const InlineToolbar = ({ setOpen, editor }) => {
  const [toolbarStyle, setToolbarStyle] = useState({});
  const dispatch = useDispatch();

  useEffect(() => {
    if (editor && editor.isEditable) {
      editor.commands.focus();
    }
    const updateToolbarPosition = () => {
      const ebMidOpenElement = document.querySelector(".eb-mid-open");
      if (ebMidOpenElement) {
        const width = ebMidOpenElement.offsetWidth || 0;
        setToolbarStyle({
          right: `calc(100% - ${width}px)`,
          backgroundColor: "#e6f4ff",
        });
      } else {
        setToolbarStyle({
          right: "10px",
        });
      }
    };

    updateToolbarPosition();
    window.addEventListener("resize", updateToolbarPosition);
    return () => window.removeEventListener("resize", updateToolbarPosition);
  }, [editor]);

  const handleTabClick = (name) => {
    dispatch(setStoryTab(name));
    dispatch(setShowSideStoryTab(true));
    // setShowSideBar(true);
    // dispatch(showStoryTab(true));
  };

  return (
    <>
      {editor && (
        <FloatingMenu
          tippyOptions={{
            duration: 100,
            placement: "left-start",
          }}
          editor={editor}
        >
          <div className="floating-menu">
            <button onClick={() => handleTabClick("Add")}>
              <HiPlus />
            </button>
          </div>
        </FloatingMenu>
      )}
    </>
  );
};

export default InlineToolbar;
