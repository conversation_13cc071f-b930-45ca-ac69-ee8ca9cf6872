import React, { useEffect } from "react";
import { BiChevronRight } from "react-icons/bi";
import { Link, useNavigate, useParams } from "react-router-dom";
import Container from "../parts/Container";
import Button from "../parts/Button";
import { useDispatch, useSelector } from "react-redux";
import {
	useCreateAuthorMutation,
	useGetAuthorQuery,
	useUpdateAuthorMutation,
} from "../store/apis/authorsApi";
import AuthorSEO from "../components/authors/AuthorSEO";
import TopProfile from "../components/authors/TopProfile";
import { clearErrors, resetState, setAuthorsData, setErrors } from "../store/slices/authorsSlice";
import AdditionalInfo from "../components/authors/AdditionalInfo";
import AboutAuthors from "../components/authors/AboutAuthor";
import { toast } from "react-toastify";
import BreadCrumb from "../parts/BreadCrumb";

// need to check for the post and put state
// figure how to change and clear the caching upon post and put request
const AddEditAuthor = ({ method }) => {
	const dispatch = useDispatch();
	const navigate = useNavigate();
	const { id } = useParams();
	const {
		firstname,
		lastname,
		email,
		password,
		subheading,
		facebook,
		instagram,
		twitterX,
		linkedin,
		aboutus,
		storiesState: { meta, ogImg, imgsrc, twitterImg, og, twitter },
	} = useSelector((state) => state.authors);
	const { data, isLoading, isError, error } = useGetAuthorQuery(id, {
		skip: !id,
	});

	const [updateAuthor, { isLoading: authorUpdateLoading, isError: tagError }] =
		useUpdateAuthorMutation();
	const [createAuthor, { isLoading: authorCreateLoading, isError: tagCreateError }] =
		useCreateAuthorMutation();

	// displaying the error message here
	if (isError) {
		if (error.status === 401) {
			toast.error("Session Expired", { toastId: "video_edit_session_expired" });
			navigate("/signin");
		} else {
			toast.error(error.data.message, { toastId: "get_single_video_error" });
		}
	}

	const validateForm = () => {
		const newErrors = {};
		if (!firstname) {
			newErrors.firstname = "First name is required";
		}
		if (!lastname) {
			newErrors.lastname = "Last name is required";
		}
		if (!email) {
			newErrors.email = "Email is required";
		}
		if (!password) {
			newErrors.password = "Password is required";
		}
		if (!subheading) {
			newErrors.subheading = "Subheading is required";
		}
		if (!meta.title) {
			newErrors.title = "Title is required";
		}
		if (!meta.description) {
			newErrors.description = "Description is required";
		}
		if (!meta.slug) {
			newErrors.slug = "Slug is required";
		}
		return newErrors;
	};

	// function to call on the submit authors
	const handleSubmit = () => {
		const validationErrors = validateForm();
		const formData = new FormData();
		if (Object.keys(validationErrors).length > 0) {
			dispatch(setErrors(validationErrors));
		} else {
			dispatch(clearErrors());
			const finalMetaData = {
				title: meta.title,
				description: meta.description,
				keywords: meta.keywords.value,
				author: meta.author,
				canonical: meta.canonical,
				robots: meta.robots,
				_id: meta._id,
				og: {
					title: og.title,
					description: og.description,
				},
				twitter: {
					title: twitter.title,
					description: twitter.description,
					card: twitter.card === "large" ? "summary_large_image" : "summary",
				},
			};

			// Simple file type check
			const isFile = (value) => {
				return typeof value === "string";
			};

			// Apply checks
			if (isFile(imgsrc)) {
				finalMetaData.og.image = imgsrc;
			}

			if (isFile(ogImg)) {
				finalMetaData.og.image = ogImg;
			}

			if (isFile(twitterImg)) {
				finalMetaData.twitter.image = twitterImg;
			}

			if (!isFile(imgsrc)) {
				formData.append("imgsrc", imgsrc[0]);
			}
			if (!isFile(ogImg)) {
				formData.append("ogImg", ogImg[0]);
			}
			if (!isFile(twitterImg)) {
				formData.append("twitterImg", twitterImg[0]);
			}

			const payload = {
				firstname,
				lastname,
				email,
				password,
				subheading,
				social: {
					instagram,
					linkedin: linkedin,
					facebook,
					twitter: twitterX,
				},
				aboutus,
				slug: `/${meta.slug}`,
				meta: finalMetaData,
			};
			if (method == "PUT") {
				delete payload.password;
			}
			console.log(payload);

			formData.append("data", JSON.stringify(payload));

			if (method === "POST") {
				createAuthor(formData)
					.then((res) => {
						if (res.data.status === "success") {
							toast.success("Author Create successfully!");
							navigate("/admin/authors");
						} else {
							toast.error("Failed to create author.");
						}
					})
					.catch((err) => {
						toast.error("Failed to create author.");
						console.log(err);
					});
			} else if (method === "PUT") {
				updateAuthor({ data: formData, id: id })
					.then((res) => {
						if (res.data.status === "success") {
							toast.success("Author updated successfully!");
							navigate("/admin/authors");
						} else {
							toast.error("Failed to update Author.");
						}
					})
					.catch((err) => {
						toast.error("Failed to update Author.");
						console.log(err);
					});
			}
		}
	};

	// clear the edit data when component unmounts
	useEffect(() => {
		if (data) {
			dispatch(setAuthorsData(data));
		}

		return () => {
			if (data) {
				dispatch(resetState());
			}
		};
	}, [data]);

	if (isLoading) {
		return <p>Loading...</p>;
	}

	const handleCancel = () => navigate("/admin/authors");

	return (
		<Container>
			<div className="flex w-full items-center justify-between mb-0">
				<div>
					<div className="flex items-center  text-sm">
						<Link
							to={"/admin/authors"}
							className="hover:bg-white text-blackShade rounded-full px-3 py-1 bg-transparent "
						>
							Authors
						</Link>
						<BiChevronRight className="text-xl" />
						<p className="text-fadeGray pl-2">
							{`${firstname ? `${firstname} ${lastname}` : "Untitled Author"}` || "Untitled Story"}
						</p>
					</div>
					<BreadCrumb
						title={"Authors"}
						description={"Manage what info is shown on your authors' public profile."}
					/>
				</div>
				<div className="flex items-center gap-x-3">
					<Button
						rounded="full"
						variant="secondary"
						size="sm"
						customClasses="py-[7px]"
						onClick={handleCancel}
					>
						Cancel
					</Button>
					<Button
						disabled={authorCreateLoading || authorUpdateLoading}
						rounded="full"
						size="sm"
						customClasses="py-[7px]"
						onClick={handleSubmit}
					>
						{authorCreateLoading || authorUpdateLoading ? "Please wait..." : "Save"}
					</Button>
				</div>
			</div>

			<div className="mt-4 px-2 flex gap-x-5">
				<div className="w-[60%] flex flex-col gap-y-5">
					<TopProfile method={method} />
					<AdditionalInfo />
					<AboutAuthors />

					{/* <div className="">
            <div className="py-5 border-b w-full px-5 text-lg font-semibold ">
              Video Info
            </div>
          </div> */}
				</div>
				<div className="flex flex-col gap-y-5 w-[40%]">
					<AuthorSEO />
				</div>
			</div>
		</Container>
	);
};

export default AddEditAuthor;
