import React, { useEffect, useState, useRef, useCallback } from "react";
import Container from "../parts/Container";
import BreadCrumb from "../parts/BreadCrumb";
import Button from "../parts/Button";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { toast } from "react-toastify";
import { Input } from "../parts/FormComponents";
// UNCOMMENT WHEN API IS READY:
// import {
//   useGetEmailGroupsQuery,
//   useGetEmailTemplatesQuery,
//   useSendEmailCampaignMutation,
//   useScheduleEmailCampaignMutation,
// } from "../store/apis/emailMarketingApi";
import {
	setCampaignField,
	setRecipientGroup,
	setRecipientType,
	setRecipientIndividuals,
	setSelectedTemplate,
} from "../store/slices/emailMarketingSlice";
// ReactQuill removed as we no longer need the email content editor
import { <PERSON>i<PERSON><PERSON><PERSON><PERSON>, BiSearch } from "react-icons/bi";
import { FiCheck } from "react-icons/fi";

const EmailCampaign = () => {
	const navigate = useNavigate();
	const dispatch = useDispatch();
	const [showScheduleOptions, setShowScheduleOptions] = useState(false);
	const [scheduledDate, setScheduledDate] = useState("");
	const [scheduledTime, setScheduledTime] = useState("");

	// Get campaign data from Redux store
	const { campaign, templates, groups } = useSelector((state) => state.emailMarketing);
	const { data: contactsData } = useSelector((state) => state.emailMarketing.contacts);

	// Use dummy data instead of API calls
	const templatesData = templates.data;
	const groupsData = groups.data;
	const [isSending, setIsSending] = useState(false);
	const [isScheduling, setIsScheduling] = useState(false);

	// Contact selection state
	const [contactSearch, setContactSearch] = useState("");
	const [filteredContacts, setFilteredContacts] = useState([]);
	const [displayedContacts, setDisplayedContacts] = useState([]);
	const [contactsPage, setContactsPage] = useState(1);
	const contactsPerPage = 20;
	const contactsContainerRef = useRef(null);

	// Filter contacts based on search
	useEffect(() => {
		if (contactsData) {
			const filtered = contactsData.filter(
				(contact) =>
					contact.email.toLowerCase().includes(contactSearch.toLowerCase()) ||
					(contact.firstName &&
						contact.firstName.toLowerCase().includes(contactSearch.toLowerCase())) ||
					(contact.lastName && contact.lastName.toLowerCase().includes(contactSearch.toLowerCase()))
			);
			setFilteredContacts(filtered);
			setContactsPage(1);
			setDisplayedContacts(filtered.slice(0, contactsPerPage));
		}
	}, [contactsData, contactSearch]);

	// Handle scroll for infinite loading
	const handleContactsScroll = useCallback(() => {
		if (contactsContainerRef.current) {
			const { scrollTop, scrollHeight, clientHeight } = contactsContainerRef.current;

			// If scrolled to bottom and there are more contacts to load
			if (
				scrollTop + clientHeight >= scrollHeight - 20 &&
				displayedContacts.length < filteredContacts.length
			) {
				const nextPage = contactsPage + 1;
				const newDisplayedContacts = [
					...displayedContacts,
					...filteredContacts.slice((nextPage - 1) * contactsPerPage, nextPage * contactsPerPage),
				];

				setContactsPage(nextPage);
				setDisplayedContacts(newDisplayedContacts);
			}
		}
	}, [contactsPage, displayedContacts, filteredContacts, contactsPerPage]);

	// UNCOMMENT WHEN API IS READY:
	// // Fetch templates and groups
	// const { data: templatesData, isLoading: isLoadingTemplates } = useGetEmailTemplatesQuery();
	// const { data: groupsData, isLoading: isLoadingGroups } = useGetEmailGroupsQuery();
	//
	// // Mutations for sending emails
	// const [sendEmailCampaign, { isLoading: isSending }] = useSendEmailCampaignMutation();
	// const [scheduleEmailCampaign, { isLoading: isScheduling }] = useScheduleEmailCampaignMutation();

	// Set first template as selected by default if not already set
	useEffect(() => {
		if (templatesData && templatesData.length > 0 && !campaign.templateId) {
			dispatch(setSelectedTemplate(templatesData[0]));
		}
	}, [templatesData, campaign.templateId, dispatch]);

	// Initialize filtered contacts on component mount
	useEffect(() => {
		if (contactsData) {
			setFilteredContacts(contactsData);
			setDisplayedContacts(contactsData.slice(0, contactsPerPage));
		}
	}, [contactsData, contactsPerPage]);

	// Handle form field changes
	const handleInputChange = (field, value) => {
		dispatch(setCampaignField({ field, value }));
	};

	// Handle recipient type change
	const handleRecipientTypeChange = (e) => {
		dispatch(setRecipientType(e.target.value));
	};

	// Handle group selection
	const handleGroupChange = (e) => {
		dispatch(setRecipientGroup(e.target.value));
	};

	// Handle individual contact selection
	const handleIndividualChange = (contactId, isChecked) => {
		const currentSelected = [...campaign.recipients.individuals];

		if (isChecked) {
			// Add to selected if not already included
			if (!currentSelected.includes(contactId)) {
				dispatch(setRecipientIndividuals([...currentSelected, contactId]));
			}
		} else {
			// Remove from selected
			dispatch(setRecipientIndividuals(currentSelected.filter((id) => id !== contactId)));
		}
	};

	// Toggle select all contacts
	const handleSelectAllContacts = (isChecked) => {
		if (isChecked) {
			const allContactIds = filteredContacts.map((contact) => contact._id);
			dispatch(setRecipientIndividuals(allContactIds));
		} else {
			dispatch(setRecipientIndividuals([]));
		}
	};

	// Handle template selection
	const handleTemplateChange = (templateId) => {
		const selectedTemplate = templatesData.find((template) => template.id === templateId);
		dispatch(setSelectedTemplate(selectedTemplate));
	};

	// Handle send email
	const handleSendEmail = () => {
		// Validate form
		if (!campaign.subject) {
			toast.error("Please enter a subject for your email");
			return;
		}

		if (campaign.recipients.type === "group" && !campaign.recipients.groupId) {
			toast.error("Please select a group to send to");
			return;
		}

		if (campaign.recipients.type === "individual" && campaign.recipients.individuals.length === 0) {
			toast.error("Please select at least one contact");
			return;
		}

		// Prepare campaign data
		const campaignData = {
			subject: campaign.subject,
			from: campaign.from,
			sendAs: campaign.sendAs,
			recipientType: campaign.recipients.type,
			groupId: campaign.recipients.groupId,
			individuals: campaign.recipients.individuals,
			templateId: campaign.templateId,
		};

		// Simulate sending campaign
		setIsSending(true);

		// Log what would be sent to API
		console.log("Would send email campaign with data:", campaignData);

		// Simulate API delay
		setTimeout(() => {
			toast.success("Email campaign sent successfully!");
			setIsSending(false);
			navigate("/admin/email/analytics");
		}, 1500);

		// UNCOMMENT WHEN API IS READY:
		// sendEmailCampaign(campaignData)
		//   .then((res) => {
		//     if (res.data?.status === "success") {
		//       toast.success("Email campaign sent successfully!");
		//       navigate("/admin/email/analytics");
		//     } else {
		//       toast.error("Failed to send email campaign.");
		//     }
		//   })
		//   .catch((err) => {
		//     toast.error("Failed to send email campaign.");
		//     console.error(err);
		//   });
	};

	// Handle schedule email
	const handleScheduleEmail = () => {
		// Validate form
		if (!campaign.subject) {
			toast.error("Please enter a subject for your email");
			return;
		}

		if (campaign.recipients.type === "group" && !campaign.recipients.groupId) {
			toast.error("Please select a group to send to");
			return;
		}

		if (campaign.recipients.type === "individual" && campaign.recipients.individuals.length === 0) {
			toast.error("Please select at least one contact");
			return;
		}

		if (!scheduledDate || !scheduledTime) {
			toast.error("Please select a date and time for scheduling");
			return;
		}

		// Prepare campaign data with schedule
		const scheduledDateTime = new Date(`${scheduledDate}T${scheduledTime}`);

		const campaignData = {
			subject: campaign.subject,
			from: campaign.from,
			sendAs: campaign.sendAs,
			recipientType: campaign.recipients.type,
			groupId: campaign.recipients.groupId,
			individuals: campaign.recipients.individuals,
			templateId: campaign.templateId,
			scheduledAt: scheduledDateTime.toISOString(),
		};

		// Simulate scheduling campaign
		setIsScheduling(true);

		// Log what would be sent to API
		console.log("Would schedule email campaign with data:", campaignData);

		// Simulate API delay
		setTimeout(() => {
			toast.success("Email campaign scheduled successfully!");
			setIsScheduling(false);
			navigate("/admin/email/analytics");
		}, 1500);

		// UNCOMMENT WHEN API IS READY:
		// scheduleEmailCampaign(campaignData)
		//   .then((res) => {
		//     if (res.data?.status === "success") {
		//       toast.success("Email campaign scheduled successfully!");
		//       navigate("/admin/email/analytics");
		//     } else {
		//       toast.error("Failed to schedule email campaign.");
		//     }
		//   })
		//   .catch((err) => {
		//     toast.error("Failed to schedule email campaign.");
		//     console.error(err);
		//   });
	};

	// Render template selection
	const renderTemplateSelection = () => {
		if (!templatesData || templatesData.length === 0) {
			return <div className="text-center py-4">No templates available</div>;
		}

		return (
			<div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
				{templatesData.map((template) => (
					<div
						key={template.id}
						className={`border rounded-lg overflow-hidden cursor-pointer transition-all ${
							campaign.templateId === template.id
								? "border-blue-500 ring-2 ring-blue-200"
								: "border-gray-200 hover:border-blue-300"
						}`}
						onClick={() => handleTemplateChange(template.id)}
					>
						<div className="h-40 bg-gray-100 flex items-center justify-center">
							{template.thumbnail ? (
								<img
									src={template.thumbnail}
									alt={template.name}
									className="w-full h-full object-cover"
								/>
							) : (
								<div className="text-gray-400">No Preview</div>
							)}
						</div>
						<div className="p-3">
							<h3 className="font-medium">{template.name}</h3>
							<p className="text-sm text-gray-500">{template.description}</p>
						</div>
					</div>
				))}
			</div>
		);
	};

	return (
		<Container>
			<div className="lg:flex items-center justify-between mb-5">
				<div>
					<BreadCrumb
						title={"Create Email Campaign"}
						description={"Create and send bulk emails to your contacts."}
					/>
				</div>
			</div>

			<div className="bg-white rounded-lg border border-gray-200 p-6 mb-6">
				<h2 className="text-lg font-semibold mb-4">Email Template</h2>
				{renderTemplateSelection()}
			</div>

			<div className="bg-white rounded-lg border border-gray-200 p-6 mb-6">
				<h2 className="text-lg font-semibold mb-4">Email Details</h2>

				<div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
					<div>
						<Input
							label="From (Email Address)"
							value={campaign.from}
							disabled={true}
							placeholder="<EMAIL>"
						/>
						<p className="text-xs text-gray-500 mt-1">This email address cannot be changed</p>
					</div>

					<div>
						<Input
							label="Send As (Name)"
							value={campaign.sendAs}
							disabled={true}
							placeholder="Your Company Name"
						/>
						<p className="text-xs text-gray-500 mt-1">This name cannot be changed</p>
					</div>

					<div className="md:col-span-2">
						<Input
							label="Email Subject"
							value={campaign.subject}
							onDebouncedChange={(value) => handleInputChange("subject", value)}
							placeholder="Enter email subject"
							required={true}
						/>
					</div>
				</div>
			</div>

			<div className="bg-white rounded-lg border border-gray-200 p-6 mb-6">
				<h2 className="text-lg font-semibold mb-4">Recipients</h2>

				<div className="mb-4">
					<label className="block mb-2 font-medium">Send To</label>
					<div className="flex flex-col md:flex-row gap-4">
						<label className="inline-flex items-center">
							<input
								type="radio"
								name="recipientType"
								value="all"
								checked={campaign.recipients.type === "all"}
								onChange={handleRecipientTypeChange}
								className="mr-2"
							/>
							All Contacts
						</label>

						<label className="inline-flex items-center">
							<input
								type="radio"
								name="recipientType"
								value="group"
								checked={campaign.recipients.type === "group"}
								onChange={handleRecipientTypeChange}
								className="mr-2"
							/>
							Specific Group
						</label>

						<label className="inline-flex items-center">
							<input
								type="radio"
								name="recipientType"
								value="individual"
								checked={campaign.recipients.type === "individual"}
								onChange={handleRecipientTypeChange}
								className="mr-2"
							/>
							Specific Contacts
						</label>
					</div>
				</div>

				{campaign.recipients.type === "group" && (
					<div className="mb-4">
						<label className="block mb-2 font-medium">Select Group</label>
						<select
							value={campaign.recipients.groupId || ""}
							onChange={handleGroupChange}
							className="w-full p-2 border border-gray-300 rounded-md"
						>
							<option value="">Select a group</option>
							{groupsData?.map((group) => (
								<option key={group._id} value={group._id}>
									{group.name} ({group.contactCount || 0} contacts)
								</option>
							))}
						</select>
					</div>
				)}

				{campaign.recipients.type === "individual" && (
					<div className="mb-4">
						<label className="block mb-2 font-medium">Select Contacts</label>

						{/* Search and select all */}
						<div className="flex flex-col md:flex-row gap-2 mb-3">
							<div className="relative flex-grow">
								<input
									type="text"
									placeholder="Search contacts..."
									value={contactSearch}
									onChange={(e) => setContactSearch(e.target.value)}
									className="w-full p-2 pl-8 border border-gray-300 rounded-md"
								/>
								<BiSearch className="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-400" />
							</div>

							<div className="flex items-center">
								<label className="inline-flex items-center cursor-pointer">
									<input
										type="checkbox"
										checked={
											filteredContacts.length > 0 &&
											campaign.recipients.individuals.length === filteredContacts.length
										}
										onChange={(e) => handleSelectAllContacts(e.target.checked)}
										className="mr-2"
									/>
									Select All ({filteredContacts.length})
								</label>
							</div>
						</div>

						{/* Contacts list with infinite scroll */}
						<div
							ref={contactsContainerRef}
							onScroll={handleContactsScroll}
							className="border border-gray-300 rounded-md p-2 max-h-60 overflow-y-auto"
						>
							{displayedContacts.length > 0 ? (
								displayedContacts.map((contact) => (
									<div
										key={contact._id}
										className={`mb-2 p-2 rounded-md hover:bg-gray-50 cursor-pointer ${
											campaign.recipients.individuals.includes(contact._id) ? "bg-blue-50" : ""
										}`}
										onClick={() =>
											handleIndividualChange(
												contact._id,
												!campaign.recipients.individuals.includes(contact._id)
											)
										}
									>
										<div className="flex items-center">
											<div
												className={`w-5 h-5 mr-2 flex items-center justify-center border rounded ${
													campaign.recipients.individuals.includes(contact._id)
														? "bg-blue-500 border-blue-500 text-white"
														: "border-gray-300"
												}`}
											>
												{campaign.recipients.individuals.includes(contact._id) && (
													<FiCheck size={12} />
												)}
											</div>
											<div>
												<div className="font-medium">{contact.email}</div>
												{contact.firstName && contact.lastName && (
													<div className="text-sm text-gray-500">
														{contact.firstName} {contact.lastName}
													</div>
												)}
											</div>
										</div>
									</div>
								))
							) : (
								<div className="text-center py-4 text-gray-500">
									{contactSearch
										? "No contacts found matching your search"
										: "No contacts available"}
								</div>
							)}

							{displayedContacts.length < filteredContacts.length && (
								<div className="text-center py-2 text-sm text-gray-500">
									Scroll for more contacts...
								</div>
							)}
						</div>

						<div className="flex justify-between mt-2">
							<p className="text-xs text-gray-500">
								Showing {displayedContacts.length} of {filteredContacts.length} contacts
							</p>
							<p className="text-xs text-gray-500 font-medium">
								Selected: {campaign.recipients.individuals.length} contacts
							</p>
						</div>
					</div>
				)}
			</div>

			<div className="bg-white rounded-lg border border-gray-200 p-6 mb-6">
				<h2 className="text-lg font-semibold mb-4">Send Options</h2>

				<div className="flex items-center mb-4">
					<label className="inline-flex items-center mr-6">
						<input
							type="checkbox"
							checked={showScheduleOptions}
							onChange={() => setShowScheduleOptions(!showScheduleOptions)}
							className="mr-2"
						/>
						Schedule for later
					</label>
				</div>

				{showScheduleOptions && (
					<div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
						<div>
							<label className="block mb-2 font-medium">Date</label>
							<div className="relative">
								<input
									type="date"
									value={scheduledDate}
									onChange={(e) => setScheduledDate(e.target.value)}
									min={new Date().toISOString().split("T")[0]}
									className="w-full p-2 border border-gray-300 rounded-md"
								/>
								<BiCalendar className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
							</div>
						</div>

						<div>
							<label className="block mb-2 font-medium">Time</label>
							<input
								type="time"
								value={scheduledTime}
								onChange={(e) => setScheduledTime(e.target.value)}
								className="w-full p-2 border border-gray-300 rounded-md"
							/>
						</div>
					</div>
				)}

				<div className="flex justify-end gap-4 mt-6">
					<Button
						variant="secondary"
						rounded="full"
						onClick={() => navigate("/admin/email/contacts")}
					>
						Cancel
					</Button>

					{showScheduleOptions ? (
						<Button
							onClick={handleScheduleEmail}
							rounded="full"
							isLoading={isScheduling}
							disabled={isScheduling}
						>
							Schedule Email
						</Button>
					) : (
						<Button
							onClick={handleSendEmail}
							rounded="full"
							isLoading={isSending}
							disabled={isSending}
						>
							Send Now
						</Button>
					)}
				</div>
			</div>
		</Container>
	);
};

export default EmailCampaign;
