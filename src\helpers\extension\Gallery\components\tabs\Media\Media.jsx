import React, { useState, useCallback } from "react";
import Image from "./Image";

const Media = ({ editor, data = [], setIsEdit }) => {
  const [selectedIndex, setSelectedIndex] = useState([]);

  const onSelectFiles = (value) => {
    setSelectedIndex((prev) => {
      if (selectedIndex.includes(value)) {
        return prev.filter((v) => v !== value);
      } else {
        return [...prev, value];
      }
    });
  };

  const onUpload = useCallback(
    (urls) => {
      if (!urls || urls.length === 0) return;
      const newData = urls.map((item) => ({
        src: item || "",
        alt: "",
        caption: "",
        courtesy: "",
        link: {
          url: "",
          target: "_blank",
          noreferrer: "noreferrer",
          nofollow: "",
          sponsored: "",
        },
      }));

      const updatedData = [...data, ...newData];
      editor.chain().setGalleryBlock({ data: updatedData }).run();
    },
    [data, editor]
  );

  const onReorder = useCallback(
    (data) => {
      if (!data || data.length === 0) return;
      editor.chain().setGalleryBlock({ data }).run();
    },
    [data, editor]
  );

  const handleSelect = () => {
    if (selectedIndex.length > 0) {
      setSelectedIndex([]);
      return;
    }
    const allIndexes = data?.map((_, index) => index);
    setSelectedIndex(allIndexes);
  };

  const handleEdit = () => {
    setIsEdit(selectedIndex[0]);
  };

  const handleDelete = () => {
    const updatedData = data?.filter(
      (_, index) => !selectedIndex.includes(index)
    );
    editor.chain().setGalleryBlock({ data: updatedData }).focus().run();
    setSelectedIndex([]);
  };

  return (
    <div className="px-4 py-6 overflow-y-auto overflow-x-hidden">
        <div className="flex flex-col">
        <div className="flex justify-between mb-[10px]">
            <button
            className="transition-all duration-300 text-primary underline"
            onClick={handleSelect}
            >
            <span className="text-sm">
                {selectedIndex.length > 0
                ? `Unselect All (${selectedIndex?.length || 0})`
                : `Select All (${data?.length || 0})`}
            </span>
            </button>
            {selectedIndex.length > 0 && (
            <div className="flex gap-5">
                <button
                className="transition-all duration-300 text-primary underline"
                onClick={handleDelete}
                >
                <span className="text-sm">Delete</span>
                </button>
                {selectedIndex.length === 1 && (
                <button
                    className="transition-all duration-300 text-primary underline"
                    onClick={handleEdit}
                >
                    <span className="text-sm">Edit</span>
                </button>
                )}
            </div>
            )}
        </div>
        <Image
            data={data}
            selectedIndex={selectedIndex}
            onSelectFiles={onSelectFiles}
            onUpload={onUpload}
            onReorder={onReorder}
        />
        </div>      
    </div>
  );
};

export default Media;
