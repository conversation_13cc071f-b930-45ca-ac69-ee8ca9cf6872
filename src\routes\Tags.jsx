import React, { useMemo, useState } from "react";
import Container from "../parts/Container";
import BreadCrumb from "../parts/BreadCrumb";
import Button, { RoundedIconsButton } from "../parts/Button";
import { BiChevronRight, BiPlus } from "react-icons/bi";
import Table from "../parts/Table";
import { useDispatch, useSelector } from "react-redux";
import Filters from "../parts/Filters";
import { FiEdit } from "react-icons/fi";
import { FaRegTrashAlt } from "react-icons/fa";
import { Link, useNavigate } from "react-router-dom";
import { toast } from "react-toastify";
import {
  useDeleteTagMutation,
  useGetTagsListQuery,
} from "../store/apis/tagsApi";
import { RxCross1 } from "react-icons/rx";
import { GiCheckMark } from "react-icons/gi";
import { incrementOffset } from "../store/slices/tagsSlice";
import ConfirmationModal from "../parts/ConfirmationModal";
import useConfirmationModal from "../utils/useConfirmationModal";
import { handleViewClickInNewTab } from "../utils/helperFunctions";

const Tags = () => {
  const navigate = useNavigate();
  const [selectedRows, setSelectedRows] = useState([]);
  const {
    filter: { search },
    limit,
    offset,
  } = useSelector((state) => state.tags);
  const dispatch = useDispatch();

  // fething the tags
  const { data, isLoading, isError, error, isFetching } = useGetTagsListQuery({
    search,
    limit,
    offset,
  });

  const [deleteTag, { isLoading: isDeleting, error: deleteError }] =
    useDeleteTagMutation();
  // confirmation modal hook
  const { isModalOpen, rowIdToDelete, openModal, closeModal } =
    useConfirmationModal();

  // handling the error here
  if (isError) {
    if (error.status === 401) {
      toast.error("Session Expired", { toastId: "tags_fetch_error" });
      navigate("/signin");
    }
  }

  // used to delete the tags
  const handleDelete = () => {
    deleteTag(rowIdToDelete)
      .then((res) => {
        if (res.data.status === "success") {
          toast.success("Tag deleted successfully!");
        } else {
          toast.error("Failed to delete tag.");
        }
      })
      .catch((err) => {
        toast.error("Failed to delete tag.");
        console.log(err);
      })
      .finally(() => {
        closeModal();
      });
  };

  // columns for the tags table
  const columns = useMemo(
    () => [
      // {
      //   accessorKey: "_id",
      //   enableSorting: false,
      //   header: ({ table }) => (
      //     <input
      //       type="checkbox"
      //       checked={table.getIsAllRowsSelected()}
      //       indeterminate={table.getIsSomeRowsSelected()}
      //       onChange={table.getToggleAllRowsSelectedHandler()} //or getToggleAllPageRowsSelectedHandler
      //     />
      //   ),
      //   cell: ({ row }) => (
      //     <input
      //       type="checkbox"
      //       checked={row.getIsSelected()}
      //       disabled={!row.getCanSelect()}
      //       onChange={row.getToggleSelectedHandler()}
      //     />
      //   ),
      // },
      { accessorKey: "name", id: "name", header: "Tag Name" },
      {
        accessorKey: "slug",
        id: "slug",
        header: () => "URL Slug",
      },

      {
        accessorKey: "postCount",
        header: () => "Stories",
        cell: ({ row }) => <div>{row.original.postCount} Stories</div>,
      },
      {
        accessorKey: "SEO",
        header: () => "SEO",
        cell: ({ row }) => (
          <div>
            {row.original.SEO === 0 ? (
              <RxCross1 className="text-red-600" />
            ) : (
              <GiCheckMark className="text-green-600" />
            )}
          </div>
        ),
      },
      {
        accessorKey: "status",
        header: "Action",
        cell: ({ row }) => {
          return (
            <div className="flex items-center gap-x-2">
              <RoundedIconsButton
                onClick={() =>
                  handleViewClickInNewTab(
                    `/admin/tags/edit/${row.original._id}`
                  )
                }
              >
                <FiEdit className="h-[15px] w-[15px]" />
              </RoundedIconsButton>
              <RoundedIconsButton
                onClick={() => {
                  openModal(row.original._id);
                }}
              >
                <FaRegTrashAlt className="h-[15px] w-[15px]" />
              </RoundedIconsButton>
            </div>
          );
        },
      },
    ],
    []
  );

  // funtion to perfrom action on row selection in the table
  const handleRowSelectionChange = (rowIds) => {
    console.log(rowIds, " rows ids");
  };

  // used to perform actions when a row is selected
  const handleRowSelect = (rows) => {
    console.log(rows, " rows");
    setSelectedRows(rows);
    // Make API call or perform other actions with the selected rows
  };

  // used to handle the filter change on the table for the status
  const handleFilterChange = (filterName, value) => {
    console.log(filterName, value, " filter names and value");
    // Update filter state and fetch new data
  };

  // Make API call to fetch more data
  const fetchMoreData = () => {
    if (
      data?.data?.length > 0 && // Check if `data.data` has items
      !isFetching && // Ensure no ongoing fetch
      data?.data?.length < data?.count // Check if there are more items to fetch
    ) {
      dispatch(incrementOffset());
    }
  };

  return (
    <Container>
      <div className="lg:flex items-center justify-between mb-5">
        <div>
          {/* <div className="flex items-center text-sm">
            <Link
              to={"/admin/tags"}
              className="hover:bg-white text-blackShade rounded-full px-3 py-1 bg-transparent "
            >
              Tags
            </Link>
          </div> */}
          <BreadCrumb
            title={"Tags"}
            description={
              "Create and manage tags to help readers discover related content in your stories."
            }
          />
        </div>
        <Button
          rounded="full"
          size="sm"
          customClasses="mt-2 lg:mt-0 pb-1.5 pt-1.5"
          onClick={() => navigate("/admin/tags/create")}
        >
          <BiPlus /> Create Tag
        </Button>
      </div>
      <Table
        module="tags"
        data={data ? data.data : []}
        isLoading={isFetching}
        columns={columns}
        offset={offset}
        handleRowSelectionChange={handleRowSelectionChange}
        fetchMoreData={fetchMoreData}
        customClass={"categories"}
        isFetching={isFetching}
      />
      <Filters />
      <ConfirmationModal
        isOpen={isModalOpen}
        isLoading={isDeleting}
        toggleModal={closeModal}
        message="Are you sure you want to delete this tag?"
        onConfirm={handleDelete}
      />
    </Container>
  );
};

export default Tags;
