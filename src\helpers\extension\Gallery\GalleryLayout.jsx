import React from "react";
import {
  Grid,
  Masonry,
  Collage,
  Thumbnails,
  Slideshow,
  Panorama,
  Columns,
  Slider,
} from "./templates";

const GalleryLayout = ({ layout, properties, data }) => {
  const templates = {
    grid: <Grid data={data} properties={properties[layout || "grid"]} />,
    masonry: (
      <Masonry data={data} properties={properties[layout || "masonry"]} />
    ),
    collage: (
      <Collage data={data} properties={properties[layout || "collage"]} />
    ),
    thumbnails: (
      <Thumbnails data={data} properties={properties[layout || "thumbnails"]} />
    ),
    slideshow: (
      <Slideshow data={data} properties={properties[layout || "slideshow"]} />
    ),
    panorama: (
      <Panorama data={data} properties={properties[layout || "panorama"]} />
    ),
    columns: (
      <Columns data={data} properties={properties[layout || "columns"]} />
    ),
    slider: <Slider data={data} properties={properties[layout || "slider"]} />,
  };
  if (!data || data.length === 0) return;
  return templates[layout || "grid"];
};

export default GalleryLayout;
