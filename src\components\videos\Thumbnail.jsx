import React, { useRef, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  setVideoImage,
  updateThumbnail,
} from "../../store/slices/videoStorySlice";
import Button from "../../parts/Button";

const Thumbnail = () => {
  const { coverImg, isYoutube } = useSelector((state) => state.videoStory);
  const [error, setError] = useState("");
  const fileInputRef = useRef(null);
  const dispatch = useDispatch();

  // Valid image types
  const VALID_TYPES = ["image/jpeg", "image/png", "image/jpg", "image/webp"];
  // Maximum file size (5MB in bytes)
  const MAX_FILE_SIZE = 5 * 1024 * 1024;

  const validateFile = (file) => {
    // Reset error state
    setError("");

    // Check if file exists
    if (!file) {
      setError("Please select a file");
      return false;
    }

    // Validate file type
    if (!VALID_TYPES.includes(file.type)) {
      setError(
        "Invalid file type. Please upload JPEG, PNG, or WebP images only"
      );
      return false;
    }

    // Validate file size
    if (file.size > MAX_FILE_SIZE) {
      setError("File size exceeds 5MB limit");
      return false;
    }
    return true;
  };

  // Handle custom thumbnail upload
  const handleCustomThumbnail = (e) => {
    const file = e.target.files[0];

    if (validateFile(file)) {
      dispatch(updateThumbnail(file));
      // dispatch(setVideoImage({ name: "ogImg", value: e.target.files }));
      // dispatch(setVideoImage({ name: "twitterImg", value: e.target.files }));
    }

    // Clear the input value to allow uploading the same file again if needed
    e.target.value = "";
  };

  const handleButtonClick = () => {
    if (fileInputRef.current) {
      // Programmatically trigger the file input click
      fileInputRef.current.click();
    } else {
      console.error("File input ref is not attached correctly!");
    }
  };

  return (
    <div className="bg-white border border-[#c1e4fe] rounded-md">
      <div className=" border-b w-full  text-lg font-semibold px-5 py-5">
        Thumbnail
      </div>
      <div className="py-5 px-5 flex flex-col gap-y-2 items-center">
        {coverImg && (
          <div className="overflow-hidden">
            <img
              src={
                typeof coverImg === "string"
                  ? coverImg
                  : URL.createObjectURL(coverImg)
              }
              alt="Video thumbnail"
              className=" w-full"
            />
          </div>
        )}
        <label className="cursor-pointer">
          <input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            onChange={handleCustomThumbnail}
            className="hidden"
          />
          <Button
            type="button"
            rounded="full"
            className="mt-2"
            onClick={handleButtonClick}
          >
            Replace
          </Button>
        </label>
        {error ? (
          <p className="text-red-500 text-sm text-center">{error}</p>
        ) : null}
      </div>
    </div>
  );
};

export default Thumbnail;
