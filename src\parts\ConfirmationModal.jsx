import { useState } from "react";
import { AiOutlineClose, AiOutlineDelete } from "react-icons/ai";
import Button from "./Button";
import { FaRegTrashAlt } from "react-icons/fa";
import OutsideClickHandler from "react-outside-click-handler";

export default function ConfirmationModal({
  isOpen,
  toggleModal,
  message = "Are you sure you want to proceed?",
  onConfirm,
  isLoading = false,
  confirmLogout = false,
}) {
  return (
    isOpen && (
      <div
        id="confirmationModal"
        tabIndex="-1"
        aria-hidden={!isOpen}
        className="fixed top-0 right-0 left-0 z-50 flex justify-center items-center w-full h-full bg-gray-800 bg-opacity-50"
      >
        <OutsideClickHandler onOutsideClick={() => toggleModal()}>
          <div className="relative p-4 w-full max-w-md h-auto">
            {/* Modal content */}
            <div className="relative p-4 text-center bg-white rounded-lg shadow sm:p-5">
              <button
                type="button"
                onClick={toggleModal}
                className="text-fadeGray absolute top-2.5 right-2.5 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm p-1.5 ml-auto inline-flex items-center"
              >
                <AiOutlineClose className="text-2xl" />
                <span className="sr-only">Close modal</span>
              </button>
              <FaRegTrashAlt className="text-fadeGray text-3xl mb-3.5 mx-auto" />
              <p className="mb-4 text-fadeGray">{message}</p>
              <div className="flex justify-center items-center space-x-4">
                <Button onClick={toggleModal} type="button" variant="outline">
                  No, cancel
                </Button>
                <Button
                  onClick={() => {
                    onConfirm();
                    toggleModal();
                  }}
                  type="button"
                  variant="danger"
                >
                  {isLoading
                    ? "Please wait..."
                    : confirmLogout
                    ? "Yes Logout"
                    : "Yes, delete"}
                </Button>
              </div>
            </div>
          </div>
        </OutsideClickHandler>
      </div>
    )
  );
}
