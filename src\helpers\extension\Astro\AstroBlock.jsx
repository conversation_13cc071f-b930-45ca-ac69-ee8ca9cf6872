import { Node } from "@tiptap/core";
import { ReactNodeViewRenderer } from "@tiptap/react";
import AstroBlockView from "./components/AstroBlockView";

export const AstroBlock = Node.create({
  name: "astroBlock",

  group: "block",
  content: "block+",
  draggable: true,
  isolating: true,
  defining: true,

  addAttributes() {
    return {
      image: {
        default: "",
        parseHTML: (element) => element.getAttribute("data-image"),
        renderHTML: (attributes) => ({
          "data-image": attributes.image,
        }),
      },
      caption: {
        default: "",
        parseHTML: (element) => element.getAttribute("data-caption"),
        renderHTML: (attributes) => ({
          "data-caption": attributes.caption,
        }),
      },
      courtesy: {
        default: "",
        parseHTML: (element) => element.getAttribute("data-courtesy"),
        renderHTML: (attributes) => ({
          "data-courtesy": attributes.courtesy,
        }),
      },
      alt: {
        default: "",
        parseHTML: (element) => element.getAttribute("data-alt"),
        renderHTML: (attributes) => ({
          "data-alt": attributes.alt,
        }),
      },
    };
  },

  parseHTML() {
    return [
      {
        tag: 'div[data-type="astroBlock"]',
      },
    ];
  },

  renderHTML({ HTMLAttributes }) {
    return ["div", { "data-type": "astroBlock", ...HTMLAttributes }];
  },

  addCommands() {
    return {
      setAstroBlock:
        () =>
        ({ commands }) =>
          commands.insertContent({
            type: this.name,
            content: [{ type: "paragraph", content: [] }],
          }),
      setAstroImage:
        (image) =>
        ({ commands }) =>
          commands.updateAttributes(this.name, { image }),
      setAstroCaption:
        (caption) =>
        ({ commands }) =>
          commands.updateAttributes(this.name, { caption }),
      setAstroCourtesy:
        (courtesy) =>
        ({ commands }) =>
          commands.updateAttributes(this.name, { courtesy }),
      setAstroAlt:
        (alt) =>
        ({ commands }) =>
          commands.updateAttributes(this.name, { alt }),
    };
  },

  addNodeView() {
    return ReactNodeViewRenderer(AstroBlockView);
  },
});

export default AstroBlock;
