const Masonry = ({
  width = 20,
  height = 20,
  fontSize,
  strokeWidth = 1,
  fill = "currentColor",
  stroke,
  ...rest
}) => {
  const style = fontSize ? { fontSize } : undefined;
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 50 50"
      fill={fill}
      stroke={stroke}
      strokeWidth={strokeWidth}
      style={style}
      xmlns="http://www.w3.org/2000/svg"
      {...rest}
    >
      <path
        fill={fill}
        d="M3 2h27a1 1 0 0 1 1 1v20a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V3a1 1 0 0 1 1-1zm0 1v20h27V3H3zm31-1h13a1 1 0 0 1 1 1v20a1 1 0 0 1-1 1H34a1 1 0 0 1-1-1V3a1 1 0 0 1 1-1zm0 1v20h13V3H34zM3 26h13a1 1 0 0 1 1 1v20a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V27a1 1 0 0 1 1-1zm0 1v20h13V27H3zm17-1h27a1 1 0 0 1 1 1v20a1 1 0 0 1-1 1H20a1 1 0 0 1-1-1V27a1 1 0 0 1 1-1zm0 1v20h27V27H20z"
      />
    </svg>
  );
};

export default Masonry;
