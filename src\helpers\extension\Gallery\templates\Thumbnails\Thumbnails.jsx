import React, { useState } from "react";

const Thumbnails = ({
  data = [],
  properties = {
    direction: "bottom", // bottom | top | left | right
    spacing: 5,
  },
}) => {
  const [activeIndex, setActiveIndex] = useState(0);
  const { direction, spacing } = properties;

  const isHorizontal = direction === "top" || direction === "bottom";
  const isReversed = direction === "top" || direction === "left";

  const thumbnailSize = 130;
  const containerStyle = {
    display: "flex",
    flexDirection: isHorizontal
      ? isReversed
        ? "column-reverse"
        : "column"
      : isReversed
      ? "row-reverse"
      : "row",
    width: "100%",
    gap: `${spacing}px`,
    height: isHorizontal ? "auto" : "100%",
  };

  const thumbnailsStyle = {
    display: "flex",
    flexDirection: isHorizontal ? "row" : "column",
    gap: `${spacing}px`,
    overflowX: isHorizontal ? "auto" : "hidden",
    overflowY: isHorizontal ? "hidden" : "auto",
    flexWrap: "nowrap",
    ...(!isHorizontal && { aspectRatio: "1 / 1" }),
  };

  // Style for main preview image
  const previewStyle = {
    flexShrink: 0,
    width: "100%",
    height: isHorizontal ? "auto" : "100%",
    aspectRatio: isHorizontal ? "16 / 9" : "1 / 1",
    objectFit: "cover",
  };

  // Thumbnail image style (square)
  const thumbImageStyle = {
    width: `${thumbnailSize}px`,
    height: `${thumbnailSize}px`,
    cursor: "pointer",
    boxSizing: "border-box",
    position: "relative",
  };

  const navButtonStyle = {
    position: "absolute",
    top: "50%",
    transform: "translateY(-50%)",
    backgroundColor: "rgba(0,0,0,0.5)",
    border: "none",
    color: "white",
    padding: "8px",
    cursor: "pointer",
    borderRadius: "50%",
    zIndex: 10,
    transition: "all 0.3s ease-in-out",
  };

  const handlePrev = () => {
    setActiveIndex((prev) => (prev > 0 ? prev - 1 : prev));
  };

  const handleNext = () => {
    setActiveIndex((prev) => (prev < data.length - 1 ? prev + 1 : prev));
  };

  return (
    <div style={containerStyle}>
      {/* Main Image */}
      <div
        style={{
          position: "relative",
          flexShrink: 0,
          width: isHorizontal ? "100%" : "85%",
        }}
      >
        <img
          src={data[activeIndex]?.src}
          alt={data[activeIndex]?.alt || ""}
          style={previewStyle}
        />
        {/* Prev Button */}
        <button
          onClick={handlePrev}
          style={{
            ...navButtonStyle,
            left: 10,
            opacity: activeIndex === 0 ? 0 : 1,
          }}
          aria-label="Previous image"
          disabled={activeIndex === 0}
        >
          {/* Left Arrow SVG */}
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            viewBox="0 0 24 24"
            width="16"
            height="16"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M15 19l-7-7 7-7"
            />
          </svg>
        </button>

        {/* Next Button */}
        <button
          onClick={handleNext}
          style={{
            ...navButtonStyle,
            right: 10,
            opacity: activeIndex === data.length - 1 ? 0 : 1,
          }}
          aria-label="Next image"
          disabled={activeIndex === data.length - 1}
        >
          {/* Right Arrow SVG */}
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            viewBox="0 0 24 24"
            width="16"
            height="16"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M9 5l7 7-7 7"
            />
          </svg>
        </button>
      </div>

      {/* Thumbnails */}
      <div className="scrollbar" style={thumbnailsStyle}>
        {data.map((item, index) => (
          <div
            key={index}
            style={{ position: "relative", flexShrink: 0 }}
            className="relative"
          >
            <img
              className="absolute inset-0 object-cover"
              src={item.src}
              alt={item.alt || `Thumbnail ${index}`}
              onClick={() => setActiveIndex(index)}
              style={thumbImageStyle}
            />
          </div>
        ))}
      </div>
    </div>
  );
};

export default Thumbnails;
