import React, { useEffect, useState } from "react";
import ImageDrop from "../stories/ImageDrop";
import { Input } from "../../parts/FormComponents";
import { useDispatch } from "react-redux";
import { useSelector } from "react-redux";
import {
  replaceTagMetaData,
  setTagMetaData,
  setTagProfile,
  setTagsMetaTemplateData,
} from "../../store/slices/tagsSlice";
import { useLocation } from "react-router-dom";
import {
  formatTagTitle,
  generateSlugStories,
} from "../../utils/helperFunctions";
import { useGetMetaDataTagQuery } from "../../store/apis/tagsApi";
import { shallowEqual } from "react-redux";
const TopSection = ({ method }) => {
  const dispatch = useDispatch();
  const { pathname } = useLocation();
  const { data } = useGetMetaDataTagQuery();

  // const [errors, setErrors] = useState({});

  const {
    name,
    description,
    errors,
    storiesState: { meta },
  } = useSelector((state) => state.tags, shallowEqual);
  const handleDataChange = ({ value, name }) => {
    dispatch(setTagProfile({ value, name }));
  };

  useEffect(() => {
    if (data && method === "POST") {
      dispatch(setTagsMetaTemplateData(data));
    }
  }, [data]);

  return (
    <div className="border border-[#c1e4fe] rounded-md bg-white">
      <div className="border-b w-full px-5 text-lg font-semibold  py-5">
        <div>{pathname === "/admin/tags/create" ? "Add" : "Edit"} Tag</div>
      </div>
      <div className="px-6 py-4 flex flex-col gap-y-4">
        <div>
          <Input
            label="Tag Name"
            name="name"
            maxLength={25}
            required={true}
            value={name}
            extraArg="25 characters"
            id="name"
            placeholder="E.g., Bruch-spots, How-tos, Technology"
            onDebouncedChange={(value) => {
              handleDataChange({ value, name: "name" });
              dispatch(
                setTagMetaData({
                  name: "slug",
                  value: generateSlugStories(value),
                })
              );
              dispatch(
                replaceTagMetaData({
                  name: "title",
                  value: formatTagTitle(value, data?.title),
                })
              );
              dispatch(
                replaceTagMetaData({
                  name: "description",
                  value: formatTagTitle(value, data?.title),
                })
              );
            }}
          />
          {errors.name && <p className="text-red-500 text-sm">{errors.name}</p>}
        </div>
        <Input
          label="Description"
          name="description"
          isTextarea={true}
          required={true}
          rows={4}
          value={description}
          id="description"
          placeholder="Enter the tag description here"
          onDebouncedChange={(value) =>
            handleDataChange({ value, name: "description" })
          }
        />
      </div>
    </div>
  );
};

export default TopSection;
