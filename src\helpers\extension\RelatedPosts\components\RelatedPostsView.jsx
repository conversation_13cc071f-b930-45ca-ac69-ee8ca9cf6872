import React, { useEffect, useCallback, useRef, useState } from "react";
import { NodeViewWrapper } from "@tiptap/react";
import styles from "./RelatedPosts.module.css";
import axios from "axios";

export const RelatedPostsView = (props) => {
  const { editor, getPos, node } = props;
  const relatedPostRef = useRef(null);
  const { postIds, title, layout, width, align } = node.attrs;
  const [postData, setPostData] = useState([]);

  const backendUrl = import.meta.env.VITE_BACKENT_URL;
  const token = localStorage.getItem("token");
  const user = JSON.parse(localStorage.getItem("user"));

  const fetchData = async () => {
    const data = await axios.post(
      `${backendUrl}/api/article/list`,
      {
        filter: { status: 1, _id: postIds },
        offset: 0,
        limit: 20,
      },
      {
        headers: {
          authtoken: `Bear<PERSON> ${token}`,
          dbtoken: `Bearer ${user?.dbToken}`,
        },
      }
    );
    setPostData(data.data.data.data);
  };

  // Load data when filters change
  useEffect(() => {
    fetchData();
  }, [postIds]);

  let wrapperClassName = "";
  if (align === "left") {
    wrapperClassName = "ml-0";
  } else if (align === "right") {
    wrapperClassName = "mr-0 ml-auto";
  } else if (align === "center") {
    wrapperClassName = "mx-auto";
  } else {
    wrapperClassName = "ml-auto mr-auto";
  }

  const onClick = useCallback(
    (e) => {
      e.stopPropagation();
      editor.commands.setNodeSelection(getPos());
    },
    [getPos, editor.commands]
  );

  return (
    <NodeViewWrapper>
      <div className={wrapperClassName} style={{ width: width }}>
        <div
          className="relatedposts-wrapper"
          contentEditable={false}
          ref={relatedPostRef}
          onClick={(e) => onClick(e)}
        >
          <h2>{title}</h2>
          <div className={styles[layout]}>
            {postData?.map((item, index) => {
              return (
                <div className={styles.cardItem} key={`relatedPost-${index}`}>
                  <div className={styles.cardImg_Wrapper}>
                    <img
                      src={item?.coverImg || ""}
                      alt={item?.altName || `Related Post ${index}`}
                    />
                  </div>
                  <div className={styles.cardContent_Wrapper}>
                    <span>{item?.category || ""}</span>
                    <h3>{item?.title || ""}</h3>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </NodeViewWrapper>
  );
};

export default RelatedPostsView;
