import React, { useEffect, useState } from "react";
import Container from "../parts/Container";
import BreadCrumb from "../parts/BreadCrumb";
import Button from "../parts/Button";
import { BiPlus } from "react-icons/bi";
import {
  useGetTextStoriesQuery,
  usePostStoriesMutation,
} from "../store/apis/storiesApi";
import { useSelector } from "react-redux";
import SectionHead from "../parts/SectionHead";
import { useNavigate } from "react-router-dom";
import StoryCard from "../components/home/<USER>";
import { status } from "../utils/constants";
import { toast } from "react-toastify";
import {
  formatDateAndTime,
  handleViewClickInNewTab,
} from "../utils/helperFunctions";

const Home = () => {
  const navigate = useNavigate();
  const { data: stories, isLoading, isError, error } = useGetTextStoriesQuery();
  const { user } = useSelector((state) => state.user);

  if (isError) {
    if (error.status === 401) {
      toast.error("Session Expired", { toastId: "author_fetch_error" });
      navigate("/signin");
    }
  }
  // function to redirect the users to the respective page
  const redirectLink = (source) => {
    navigate(source);
  };
  return (
    <>
      <div className="homeGradient p-5">
        <h1 className="text-2xl text-white font-semibold capitalize">
          Welcome to {user.clientName} Dashboard
        </h1>
        <p className="text-white">
          Your Central Hub for Comprehensive Management and Real-Time Insights,
          Developed by {user.clientName}!
        </p>
      </div>
      <div className="p-5 flex items-center w-full justify-between">
        <SectionHead title="Text Stories" />
        <Button
          rounded="full"
          onClick={() => redirectLink("/admin/stories/create?location=stories")}
        >
          <BiPlus /> <span>Create Text Story</span>
        </Button>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-5 px-3 md:px-5">
        {stories
          ? stories.data.articles.map((story) => {
              return (
                <StoryCard
                  key={story._id}
                  title={story.title}
                  author={story.author}
                  timestamp={formatDateAndTime(story.timestamp)}
                  status={status[story.status]}
                  imageUrl={story.coverImg}
                  onEdit={() =>
                    handleViewClickInNewTab(
                      `/admin/stories/edit/${story._id}?location=stories`
                    )
                  }
                  previewUrl={story.viewLink}
                />
              );
            })
          : null}
      </div>
      {stories && stories.data.videos.length > 0 ? (
        <div className="p-5 flex items-center w-full justify-between mt-5">
          <SectionHead title="Video Stories" />
          <Button
            rounded="full"
            onClick={() => redirectLink("/admin/add-video-story")}
          >
            <BiPlus /> <span>Create Video Story</span>
          </Button>
        </div>
      ) : null}

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-5 px-3 md:px-5">
        {stories
          ? stories.data.videos.map((story) => {
              return (
                <StoryCard
                  key={story._id}
                  title={story.title}
                  author={story.author}
                  timestamp={formatDateAndTime(story.timestamp)}
                  status={status[story.status]}
                  imageUrl={story.coverImg}
                  onEdit={() =>
                    navigate("/admin/edit-video-story/" + story._id)
                  }
                  previewUrl={story.viewLink}
                />
              );
            })
          : null}
      </div>
      {stories && stories.data.webStories.length > 0 ? (
        <div className="p-5 flex items-center w-full justify-between mt-5">
          <SectionHead title="Web Stories" />
          <Button
            rounded="full"
            onClick={() => redirectLink("/admin/web-stories/create")}
          >
            <BiPlus /> <span>Create Web Story</span>
          </Button>
        </div>
      ) : null}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-5 px-3 md:px-5">
        {stories
          ? stories.data.webStories.map((story) => {
              return (
                <StoryCard
                  key={story._id}
                  title={story.title}
                  author={story.author}
                  timestamp={formatDateAndTime(story.timestamp)}
                  status={status[story.status]}
                  imageUrl={story.coverImg}
                  onEdit={() => navigate("/admin/edit-web-story/" + story._id)}
                  previewUrl={story.viewLink}
                />
              );
            })
          : null}
      </div>
    </>
  );
};

export default Home;
