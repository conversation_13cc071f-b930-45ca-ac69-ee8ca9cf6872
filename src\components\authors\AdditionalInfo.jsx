import React from "react";
import { Input } from "../../parts/FormComponents";
import { useDispatch } from "react-redux";
import { setAuthorProfile, setErrors } from "../../store/slices/authorsSlice";
import { useSelector } from "react-redux";

const AdditionalInfo = () => {
	const dispatch = useDispatch();

	const { twitterX, linkedin, facebook, instagram } = useSelector((state) => state.authors);

	const { errors } = useSelector((state) => state.authors);

	const handleDataChange = ({ value, name }) => {
		if (name === "twitterX" || name === "instagram" || name === "facebook" || name === "linkedin") {
			const urlRegex = /^https:\/\/(?:www\.)?[a-zA-Z0-9-]+\.[a-zA-Z]{2,}(\/[^\s]*)?$/;
			const isValidUrl = urlRegex.test(value);
			if (value) {
				if (!isValidUrl) {
					dispatch(setErrors({ ...errors, [name]: "Please enter a valid https:// URL" }));
				} else {
					dispatch(setErrors({ ...errors, [name]: null }));
				}
			} else {
				dispatch(setErrors({ ...errors, [name]: null }));
			}
		}
		dispatch(setAuthorProfile({ value, name }));
	};
	return (
		<div className="border border-[#c1e4fe] rounded-md bg-white">
			<div className="border-b w-full px-5 text-lg font-semibold  py-5">
				<div>Additional Info</div>
			</div>
			<div className="px-5 py-4 grid grid-cols-2 gap-4 items-center">
				<div>
					<Input
						label="Facebook Profile URL (include the full https://… link)"
						name="facebook"
						value={facebook}
						id="facebook"
						placeholder="https://facebook.com/username"
						onDebouncedChange={(value) => handleDataChange({ value, name: "facebook" })}
					/>
					{errors.facebook && <p className="text-red-500 text-sm ">{errors.facebook}</p>}
				</div>
				<div>
					<Input
						label="Instagram Profile URL (include the full https://… link)"
						name="instagram"
						value={instagram}
						id="instagram"
						placeholder="https://instagram.com/username"
						onDebouncedChange={(value) => handleDataChange({ value, name: "instagram" })}
					/>
					{errors.instagram && <p className="text-red-500 text-sm ">{errors.instagram}</p>}
				</div>
				<div>
					<Input
						label="Linkedin Profile URL (include the full https://… link)"
						name="linkedin"
						value={linkedin}
						id="linkedin"
						placeholder="https://www.linkedin.com/in/username"
						onDebouncedChange={(value) => handleDataChange({ value, name: "linkedin" })}
					/>
					{errors.linkedin && <p className="text-red-500 text-sm ">{errors.linkedin}</p>}
				</div>
				<div>
					<Input
						label="X Profile URL (include the full https://… link)"
						name="twitterX"
						value={twitterX}
						id="twitterX"
						placeholder="https://x.com/username"
						onDebouncedChange={(value) => handleDataChange({ value, name: "twitterX" })}
					/>
					{errors.twitterX && <p className="text-red-500 text-sm ">{errors.twitterX}</p>}
				</div>
			</div>
		</div>
	);
};

export default AdditionalInfo;
