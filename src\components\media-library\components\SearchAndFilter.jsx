import React, { useState, useEffect } from "react";
import { Md<PERSON><PERSON><PERSON>, Md<PERSON><PERSON><PERSON><PERSON><PERSON>, Md<PERSON>lear } from "react-icons/md";
import { useMediaLibrary } from "../hooks/useMediaLibrary";

/**
 * Component for search and filter functionality
 */
const SearchAndFilter = ({ 
	onSearchChange,
	onFilterChange,
	className = "" 
}) => {
	const { 
		filters, 
		updateSearchFilter, 
		updateTypeFilter, 
		clearFilters 
	} = useMediaLibrary();
	
	const [localSearch, setLocalSearch] = useState(filters.search || "");
	const [showFilters, setShowFilters] = useState(false);
	
	// Debounce search input
	useEffect(() => {
		const timer = setTimeout(() => {
			if (localSearch !== filters.search) {
				updateSearchFilter(localSearch);
				if (onSearchChange) {
					onSearchChange(localSearch);
				}
			}
		}, 300);
		
		return () => clearTimeout(timer);
	}, [localSearch, filters.search, updateSearchFilter, onSearchChange]);
	
	const handleTypeFilterChange = (type) => {
		const newType = type === filters.type ? null : type;
		updateTypeFilter(newType);
		if (onFilterChange) {
			onFilterChange({ type: newType });
		}
	};
	
	const handleClearFilters = () => {
		setLocalSearch("");
		clearFilters();
		if (onSearchChange) {
			onSearchChange("");
		}
		if (onFilterChange) {
			onFilterChange({ type: null, search: "" });
		}
	};
	
	const hasActiveFilters = filters.search || filters.type || (filters.tags && filters.tags.length > 0);
	
	const fileTypes = [
		{ value: "image", label: "Images", icon: "🖼️" },
		{ value: "video", label: "Videos", icon: "🎥" },
		{ value: "document", label: "Documents", icon: "📄" },
		{ value: "audio", label: "Audio", icon: "🎵" },
	];
	
	return (
		<div className={`space-y-3 ${className}`}>
			{/* Search Bar */}
			<div className="relative">
				<div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
					<MdSearch className="h-5 w-5 text-gray-400" />
				</div>
				<input
					type="text"
					placeholder="Search media..."
					value={localSearch}
					onChange={(e) => setLocalSearch(e.target.value)}
					className="block w-full pl-10 pr-10 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
				/>
				{localSearch && (
					<button
						onClick={() => setLocalSearch("")}
						className="absolute inset-y-0 right-0 pr-3 flex items-center"
					>
						<MdClear className="h-5 w-5 text-gray-400 hover:text-gray-600" />
					</button>
				)}
			</div>
			
			{/* Filter Controls */}
			<div className="flex items-center justify-between">
				<button
					onClick={() => setShowFilters(!showFilters)}
					className={`flex items-center space-x-2 px-3 py-2 text-sm rounded-md transition-colors ${
						showFilters || hasActiveFilters
							? "bg-blue-100 text-blue-700"
							: "bg-gray-100 text-gray-700 hover:bg-gray-200"
					}`}
				>
					<MdFilterList className="w-4 h-4" />
					<span>Filters</span>
					{hasActiveFilters && (
						<span className="bg-blue-500 text-white text-xs px-1.5 py-0.5 rounded-full">
							{[filters.search, filters.type, ...(filters.tags || [])].filter(Boolean).length}
						</span>
					)}
				</button>
				
				{hasActiveFilters && (
					<button
						onClick={handleClearFilters}
						className="text-sm text-gray-500 hover:text-gray-700 transition-colors"
					>
						Clear all filters
					</button>
				)}
			</div>
			
			{/* Filter Options */}
			{showFilters && (
				<div className="bg-gray-50 rounded-lg p-4 space-y-4">
					{/* File Type Filter */}
					<div>
						<h4 className="text-sm font-medium text-gray-700 mb-2">File Type</h4>
						<div className="flex flex-wrap gap-2">
							{fileTypes.map((type) => (
								<button
									key={type.value}
									onClick={() => handleTypeFilterChange(type.value)}
									className={`flex items-center space-x-2 px-3 py-2 text-sm rounded-md transition-colors ${
										filters.type === type.value
											? "bg-blue-500 text-white"
											: "bg-white text-gray-700 hover:bg-gray-100 border border-gray-300"
									}`}
								>
									<span>{type.icon}</span>
									<span>{type.label}</span>
								</button>
							))}
						</div>
					</div>
					
					{/* Date Range Filter (placeholder for future implementation) */}
					<div>
						<h4 className="text-sm font-medium text-gray-700 mb-2">Date Range</h4>
						<div className="flex space-x-2">
							<select className="flex-1 px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
								<option value="">Any time</option>
								<option value="today">Today</option>
								<option value="week">This week</option>
								<option value="month">This month</option>
								<option value="year">This year</option>
							</select>
						</div>
					</div>
					
					{/* Size Filter (placeholder for future implementation) */}
					<div>
						<h4 className="text-sm font-medium text-gray-700 mb-2">File Size</h4>
						<div className="flex flex-wrap gap-2">
							{[
								{ value: "small", label: "< 1MB" },
								{ value: "medium", label: "1-10MB" },
								{ value: "large", label: "> 10MB" },
							].map((size) => (
								<button
									key={size.value}
									className="px-3 py-2 text-sm bg-white text-gray-700 hover:bg-gray-100 border border-gray-300 rounded-md transition-colors"
								>
									{size.label}
								</button>
							))}
						</div>
					</div>
				</div>
			)}
			
			{/* Active Filters Display */}
			{hasActiveFilters && (
				<div className="flex flex-wrap gap-2">
					{filters.search && (
						<div className="flex items-center space-x-1 px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-md">
							<span>Search: "{filters.search}"</span>
							<button
								onClick={() => {
									setLocalSearch("");
									updateSearchFilter("");
								}}
								className="text-blue-500 hover:text-blue-700"
							>
								×
							</button>
						</div>
					)}
					
					{filters.type && (
						<div className="flex items-center space-x-1 px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-md">
							<span>Type: {filters.type}</span>
							<button
								onClick={() => updateTypeFilter(null)}
								className="text-blue-500 hover:text-blue-700"
							>
								×
							</button>
						</div>
					)}
					
					{filters.tags && filters.tags.map((tag) => (
						<div
							key={tag}
							className="flex items-center space-x-1 px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-md"
						>
							<span>Tag: {tag}</span>
							<button
								onClick={() => {
									const newTags = filters.tags.filter(t => t !== tag);
									// updateTagsFilter(newTags); // Implement when needed
								}}
								className="text-blue-500 hover:text-blue-700"
							>
								×
							</button>
						</div>
					))}
				</div>
			)}
		</div>
	);
};

export default SearchAndFilter;
