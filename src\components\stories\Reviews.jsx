import { useSelector } from "react-redux";
import { Input } from "../../parts/FormComponents";
import { useDispatch } from "react-redux";
import { setErrors, setReviewsData } from "../../store/slices/storiesSlice";

const Reviews = () => {
	const dispatch = useDispatch();
	const {
		storiesState: { reviews },
		errors,
	} = useSelector((state) => state.stories);

	const handleDataChange = ({ name, value }) => {
		// Add validation for mandatory fields
		if (["title", "cast", "director", "releasePlatform", "publishDate"].includes(name) && !value) {
			dispatch(setErrors({ ...errors, [name]: `${name} is required` }));
		} else if (name === "runtime") {
			if (value && parseInt(value) < 0) {
				dispatch(setErrors({ ...errors, [name]: `${name} cannot be negative` }));
			} else {
				dispatch(setErrors({ ...errors, [name]: null }));
			}
		} else {
			dispatch(setErrors({ ...errors, [name]: null }));
		}

		dispatch(setReviewsData({ name, value }));
	};
	console.log(errors, " errors");
	return (
		<div className="py-5">
			<h3>Assign story reviews details for the readers to understand more.</h3>
			<div className="text-sm flex flex-col gap-y-4 mt-4 ">
				<div>
					<Input
						label="Title of the movie/series"
						name="title"
						id="title"
						required={true}
						value={reviews.title}
						placeholder="Add the title here"
						onDebouncedChange={(value) => handleDataChange({ value, name: "title" })}
						type="text"
					/>
					{errors.title ? (
						<small className="text-red-500 text-sm capitalize">{errors.title}</small>
					) : null}
				</div>
				<div>
					<label htmlFor="releasePlatform" className="text-sm relative font-medium text-gray-700">
						Select Release Platform <sup className="text-red-500 text-base absolute -right">*</sup>
					</label>
					<select
						value={reviews.releasePlatform}
						onChange={(e) => handleDataChange({ value: e.target.value, name: "releasePlatform" })}
						className="w-full px-4 py-2 mt-3 border rounded-md pr-10"
					>
						<option key={"select"} value={""}>
							Select release platform
						</option>
						<option key={"THEATRE"} value={"THEATRE"}>
							THEATRE
						</option>
						<option key={"OTT"} value={"OTT"}>
							OTT
						</option>
					</select>
					{errors.releasePlatform ? (
						<small className="text-red-500 text-sm capitalize">{errors.releasePlatform}</small>
					) : null}
				</div>
				<div>
					<Input
						label="Cast"
						name="cast"
						id="cast"
						required={true}
						value={reviews.cast}
						placeholder="Add the cast names here"
						onDebouncedChange={(value) => handleDataChange({ value, name: "cast" })}
						type="text"
					/>
					{errors.cast ? (
						<small className="text-red-500 text-sm capitalize">{errors.cast}</small>
					) : null}
				</div>
				<div>
					{" "}
					<Input
						label="Director"
						name="director"
						id="director"
						required={true}
						value={reviews.director}
						placeholder="Add the director names here"
						onDebouncedChange={(value) => handleDataChange({ value, name: "director" })}
						type="text"
					/>
					{errors.director ? (
						<small className="text-red-500 text-sm capitalize">{errors.director}</small>
					) : null}
				</div>
				<div className="flex flex-col gap-y-2">
					<label className="text-sm font-medium text-gray-700 relative">
						Release Date <sup className="text-red-500 text-base absolute -right">*</sup>
					</label>
					<div className="relative">
						<input
							type="date"
							value={reviews.publishDate}
							onChange={(e) =>
								dispatch(setReviewsData({ value: e.target.value, name: "publishDate" }))
							}
							className="w-full px-4 py-2 border rounded-md pr-10"
							//  min={new Date().toISOString().split("T")[0]}
						/>
					</div>
				</div>
				{/* <Input
					label="Reviewer Name"
					name="reviewerName"
					id="reviewerName"
					value={reviews.reviewerName}
					placeholder="Add the reviewer name here"
					onDebouncedChange={(value) => handleDataChange({ value, name: "reviewerName" })}
					type="text"
				/>

				<Input
					label="Rating"
					name="rating"
					id="rating"
					value={reviews.rating}
					placeholder="Enter the rating here"
					onDebouncedChange={(value) => handleDataChange({ value, name: "rating" })}
					type="text"
				/> */}

				<Input
					label="Review Summary"
					name="summary"
					id="summary"
					isTextarea={true}
					value={reviews.summary}
					placeholder="Add the review summary here"
					onDebouncedChange={(value) => handleDataChange({ value, name: "summary" })}
					type="text"
				/>

				<Input
					label="Writer"
					name="screenWriter"
					id="screenWriter"
					value={reviews.screenWriter}
					placeholder="Add the screen writer details here"
					onDebouncedChange={(value) => handleDataChange({ value, name: "screenWriter" })}
					type="text"
				/>

				<Input
					label="Language"
					name="language"
					id="language"
					value={reviews.language}
					placeholder="Add the movie/series languages here"
					onDebouncedChange={(value) => handleDataChange({ value, name: "language" })}
					type="text"
				/>
				<div>
					<Input
						label="Total Runtime of the movie/series (in minutes)"
						name="runtime"
						id="runtime"
						value={reviews.runtime}
						placeholder="Enter the runtime"
						onDebouncedChange={(value) => handleDataChange({ value, name: "runtime" })}
						type="number"
					/>
					{errors?.runtime ? (
						<small className="text-red-500 text-sm capitalize">{errors.runtime}</small>
					) : null}
				</div>
			</div>
		</div>
	);
};

export default Reviews;
