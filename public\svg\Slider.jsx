import React from "react";

const Slider = ({
  width = 20,
  height = 20,
  fontSize,
  strokeWidth = 1,
  fill = "currentColor",
  stroke,
  ...rest
}) => {
  const style = fontSize ? { fontSize } : undefined;
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 50 50"
      fill={fill}
      stroke={stroke}
      strokeWidth={strokeWidth}
      style={style}
      xmlns="http://www.w3.org/2000/svg"
      {...rest}
    >
      <path
        fill={fill}
        d="M3 2h5a1 1 0 0 1 1 1v44a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V3a1 1 0 0 1 1-1zm0 1v44h5V3H3zm9-1h26a1 1 0 0 1 1 1v44a1 1 0 0 1-1 1H12a1 1 0 0 1-1-1V3a1 1 0 0 1 1-1zm0 1v44h26V3H12zm30-1h5a1 1 0 0 1 1 1v44a1 1 0 0 1-1 1h-5a1 1 0 0 1-1-1V3a1 1 0 0 1 1-1zm0 1v44h5V3h-5z"
      ></path>
    </svg>
  );
};

export default Slider;
