import React, { useEffect, useState } from "react";
import ReactQuill from "react-quill";
import "react-quill/dist/quill.snow.css";
import { useDispatch, useSelector } from "react-redux";
import {
  DropdownButton,
  DropdownMenu,
  Input,
} from "../../parts/FormComponents";
import {
  setErrors,
  setInputData,
  setStatus,
  setVideoMetaData,
  setVideoOgContent,
  setVideoXContent,
} from "../../store/slices/videoStorySlice";
import { RxCross2 } from "react-icons/rx";
import { setWriter } from "../../store/slices/storiesSlice";
import Select from "react-select";
import { useGetAuthorsListQuery } from "../../store/apis/authorsApi";
import { CustomOption } from "../../utils/TransformData";
import {
  formatDateTime,
  generateSlugStories,
} from "../../utils/helperFunctions";
import ScheduleModal from "../stories/ScheduleModal";

const VideoInfo = () => {
  const dispatch = useDispatch();
  const [isPublish, setIsPublish] = useState(false);
  // fething the authors
  const { data, isLoading, isError, error, isFetching } =
    useGetAuthorsListQuery({ search: "", limit: 100 });
  const {
    errors,
    status,
    storiesState: { excerpt, title, writer, contributors: contri, publishDate },
  } = useSelector((state) => state.videoStory);
  const handleDataChange = ({ value, name }) => {
    dispatch(setInputData({ name, value }));
    dispatch(setErrors({ ...errors, [name]: null }));
  };

  const [contributors, setContributorsState] = useState(contri);
  const [newContributor, setNewContributor] = useState(""); // Track current contributor input
  const [authorsState, setAuthorsState] = useState([]);
  // Handling the author change

  // Handle contributor input change
  const handleContributorInputChange = (value) => {
    setNewContributor(value); // Update the new contributor input field
  };

  useEffect(() => {
    if (contri.length > 0) {
      setContributorsState(contri);
    }
  }, [contri]);

  // Handling contributor addition (only when Enter is pressed)
  const handleAddContributor = () => {
    const value = newContributor.trim();
    if (value && !contributors.includes(value)) {
      const updatedContributors = [...contributors, value];
      setContributorsState(updatedContributors);
      dispatch(
        setInputData({ value: updatedContributors, name: "contributors" })
      );
      setNewContributor(""); // Reset input after adding
    }
  };

  // Handling contributor removal
  const handleRemoveContributor = (value) => {
    const updatedContributors = contributors.filter((chip) => chip !== value);
    setContributorsState(updatedContributors);
    dispatch(
      setInputData({ value: updatedContributors, name: "contributors" })
    ); // Update Redux state
  };

  // handling the author change here
  const handleAuthorSelectChange = (selected) => {
    const payloadData = selected.map((author) => author.value);
    dispatch(setInputData({ value: payloadData, name: "writer" }));
  };

  const handleChange = (e) => {
    dispatch(setStatus(e.target.value));
    if (parseInt(e.target.value, 10) === 4) {
      setIsPublish(true);
    }
  };

  useEffect(() => {
    if (data) {
      const transformedData = data.data.map((data) => ({
        value: data._id,
        label: data.name,
      }));

      const initialAuthors = writer.map((author) =>
        transformedData.find((a) => a.value === author)
      );
      setAuthorsState(initialAuthors);
    }
  }, [data, writer]);

  return (
    <>
      {isPublish ? (
        <ScheduleModal setIsPublish={setIsPublish} type={"videoStory"} />
      ) : null}
      <div className="border border-[#c1e4fe] rounded-md bg-white">
        <div className="border-b w-full px-5 text-lg font-semibold  py-5">
          Video Info
        </div>
        <div className="px-5 py-4 flex flex-col gap-y-4">
          <div>
            <Input
              label="Story Title"
              name="title"
              id="title"
              placeholder="Add a story title"
              type="text"
              value={title}
              required={true}
              onDebouncedChange={(value) => {
                handleDataChange({ value, name: "title" });
                dispatch(
                  setVideoMetaData({
                    value: generateSlugStories(value),
                    name: "slug",
                  })
                );
                dispatch(setVideoMetaData({ value, name: "title" }));
                dispatch(setVideoOgContent({ value, name: "title" }));
                dispatch(setVideoXContent({ value, name: "title" }));
              }}
            />
            {errors.title && (
              <p className="text-red-500 text-sm">{errors.title}</p>
            )}
          </div>
          <div>
            <div className="text-sm text-fadeGray mb-4">Description</div>
            <ReactQuill
              theme="snow"
              value={excerpt}
              className=" !rounded-md border border-[#d1d5db] outline-none"
              onChange={(value) => {
                handleDataChange({ value, name: "excerpt" });
              }}
              name="excerpt"
            />
          </div>

          <div>
            <div className="text-sm text-fadeGray mb-4">Status</div>
            <select
              value={status}
              onChange={handleChange}
              id="status"
              className="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary focus:border-primary focus:outline-none block w-full p-2.5"
            >
              <option selected value={""}>
                Select
              </option>
              <option value={1}>Published</option>
              <option value={0}>Unpublished</option>
              <option value={3}>Draft</option>
              <option value={4}>Scheduled</option>
            </select>
            {status === parseInt(status, 10) && publishDate ? (
              <p className="mt-1 text-sm text-[#ea7d00]">
                {formatDateTime(publishDate)}
              </p>
            ) : null}
          </div>
          <div>
            <div className="text-sm text-fadeGray mb-4">Authors</div>
            <Select
              isMulti
              value={authorsState}
              name="authors"
              options={
                data
                  ? data.data.map((author) => ({
                      value: author._id,
                      label: author.name,
                      image: author.imgsrc,
                    }))
                  : []
              }
              className="basic-multi-select focus:outline-none active:outline-none focus:border-primary focus:!border"
              classNamePrefix="select"
              onChange={handleAuthorSelectChange}
              components={{
                Option: CustomOption,
              }}
            />
          </div>

          <div className="flex flex-col gap-y-4">
            {/* Input for adding contributors */}
            <Input
              label="Contributors"
              name="contributors"
              id="contributors"
              placeholder="Add contributors"
              type="text"
              value={newContributor}
              onDebouncedChange={(value) => handleContributorInputChange(value)} // Use debounced change for contributor input
              onEnter={handleAddContributor} // Add contributor when Enter is pressed
            />

            {/* Chips for contributors */}
            <div className="flex flex-wrap gap-2">
              {contributors.map((contributor, index) => (
                <span
                  key={index}
                  className="flex items-center gap-2 px-3 py-1 rounded-full hover:bg-blue-700 bg-primary text-white font-medium text-sm border cursor-pointer"
                  onClick={() => handleRemoveContributor(contributor)} // Remove chip on click
                >
                  {contributor}
                  <RxCross2 className="text-[16px]" />
                </span>
              ))}
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default VideoInfo;
