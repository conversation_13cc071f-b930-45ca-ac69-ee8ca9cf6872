import React, { useRef } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, GoPlus } from "react-icons/go";
import {
  DndContext,
  closestCenter,
  PointerSensor,
  useSensor,
  useSensors,
} from "@dnd-kit/core";
import {
  arrayMove,
  SortableContext,
  useSortable,
  rectSortingStrategy,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import styles from "../../Gallery.module.css";
import { setMediaLibraryCallback } from "../../../../../../utils/MediaLibraryManager";
import { useDispatch } from "react-redux";
import {
  openMediaLibrary,
  setFolderPath,
  setMultiple,
} from "../../../../../../store/slices/mediaLibrarySlice";
import { folderPath } from "../../../../../../utils/constants";

const SortableImage = ({ id, index, item, selectedIndex, onSelectFiles }) => {
  const { attributes, listeners, setNodeRef, transform } = useSortable({ id });

  const style = {
    transform: CSS.Transform.toString(transform),
  };

  const startPos = useRef(null);

  const handleMouseDown = (e) => {
    startPos.current = { x: e.clientX, y: e.clientY };
  };

  const handleMouseUp = (e) => {
    const endPos = { x: e.clientX, y: e.clientY };
    const dx = Math.abs(endPos.x - startPos.current.x);
    const dy = Math.abs(endPos.y - startPos.current.y);

    if (dx < 5 && dy < 5) {
      onSelectFiles(index);
    }
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      {...attributes}
      {...listeners}
      onMouseDown={handleMouseDown}
      onMouseUp={handleMouseUp}
      className="relative flex justify-center group cursor-move"
    >
      {selectedIndex.includes(index) && (
        <div className="absolute right-[-5px] top-[-5px] flex justify-center items-center w-[20px] h-[20px] z-50 rounded-full bg-[#3b82f6]">
          <GoCheck className="text-[white] text-sm stroke-1" />
        </div>
      )}
      <div
        className={`w-full h-fit mx-auto aspect-square overflow-hidden rounded-[5px] bg-gray-100 relative transition-all duration-100 ease-in-out hover:opacity-70 hover:border-2 hover:border-[#3b82f6]
                    ${
                      selectedIndex.includes(index)
                        ? "border-2 border-[#3b82f6]"
                        : ""
                    }`}
      >
        <img
          className="absolute inset-0 w-full h-full transition-all duration-100 object-cover"
          src={item?.src || ""}
          alt={item?.alt || `Gallery Image ${index}`}
        />
      </div>
    </div>
  );
};

const Image = ({ data, selectedIndex, onSelectFiles, onReorder, onUpload }) => {
  const dispatch = useDispatch();
  const baseStyle = {
    flex: 1,
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    justifyContent: "center",
    padding: "10px",
    width: "100%",
    aspectRatio: 1,
    borderWidth: 2,
    borderRadius: 5,
    borderColor: "#3b82f6",
    backgroundColor: "#f4f7ff",
    color: "#bdbdbd",
    outline: "none",
    cursor: "pointer",
    transition: "border .24s ease-in-out",
  };

  const sensors = useSensors(useSensor(PointerSensor));

  const handleDragEnd = (event) => {
    const { active, over } = event;
    if (active.id !== over?.id) {
      const oldIndex = data.findIndex((i, idx) => idx.toString() === active.id);
      const newIndex = data.findIndex((i, idx) => idx.toString() === over.id);
      const newItems = arrayMove(data, oldIndex, newIndex);
      onReorder && onReorder(newItems);
    }
  };

  const handleClick = () => {
    setMediaLibraryCallback((files) => {
      onUpload(files);
    });
    dispatch(setMultiple(true));
    dispatch(setFolderPath(folderPath.galleryBlock));
    dispatch(openMediaLibrary());
  };

  return (
    <>
      <div className={styles.mediaBody}>
        <div className={styles.mediaWrapper}>
          <div>
            <div style={baseStyle} onClick={handleClick}>
              <div className="border border-primary border-dashed w-full h-full flex items-center justify-center">
                <GoPlus className="text-4xl" />
              </div>
            </div>
          </div>
          <DndContext
            sensors={sensors}
            collisionDetection={closestCenter}
            onDragEnd={handleDragEnd}
          >
            <SortableContext
              items={data.map((_, index) => index.toString())}
              strategy={rectSortingStrategy}
            >
              {data.map((item, index) => (
                <SortableImage
                  key={index}
                  id={index.toString()}
                  index={index}
                  item={item}
                  selectedIndex={selectedIndex}
                  onSelectFiles={onSelectFiles}
                />
              ))}
            </SortableContext>
          </DndContext>
        </div>
      </div>
    </>
  );
};

export default Image;
