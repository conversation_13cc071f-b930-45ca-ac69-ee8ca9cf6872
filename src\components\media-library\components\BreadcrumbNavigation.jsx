import React from "react";
import { MdH<PERSON>, MdChevronRight } from "react-icons/md";

/**
 * Component for breadcrumb navigation in media library
 */
const BreadcrumbNavigation = ({ 
	folderHierarchy = [], 
	onNavigate,
	className = "" 
}) => {
	
	const handleBreadcrumbClick = (index) => {
		if (onNavigate) {
			onNavigate(index);
		}
	};
	
	// Create breadcrumb items with root
	const breadcrumbItems = [
		{ name: "Root", _id: null, isRoot: true },
		...folderHierarchy
	];
	
	return (
		<nav className={`flex items-center space-x-1 text-sm ${className}`} aria-label="Breadcrumb">
			<ol className="flex items-center space-x-1">
				{breadcrumbItems.map((item, index) => {
					const isLast = index === breadcrumbItems.length - 1;
					const isRoot = item.isRoot || item._id === null;
					
					return (
						<li key={item._id || 'root'} className="flex items-center">
							{/* Separator (except for first item) */}
							{index > 0 && (
								<MdChevronRight className="w-4 h-4 text-gray-400 mx-1" />
							)}
							
							{/* Breadcrumb Item */}
							<button
								onClick={() => handleBreadcrumbClick(index)}
								className={`flex items-center px-2 py-1 rounded-md transition-colors ${
									isLast
										? "text-blue-600 bg-blue-50 font-medium cursor-default"
										: "text-gray-600 hover:text-blue-600 hover:bg-gray-50"
								}`}
								disabled={isLast}
								title={isRoot ? "Go to root folder" : `Go to ${item.name}`}
							>
								{/* Home icon for root */}
								{isRoot && (
									<MdHome className="w-4 h-4 mr-1" />
								)}
								
								{/* Folder icon for non-root items */}
								{!isRoot && (
									<svg 
										className="w-4 h-4 mr-1" 
										fill="currentColor" 
										viewBox="0 0 20 20"
									>
										<path d="M2 6a2 2 0 012-2h5l2 2h5a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6z" />
									</svg>
								)}
								
								{/* Item name */}
								<span className="truncate max-w-32">
									{item.name}
								</span>
							</button>
						</li>
					);
				})}
			</ol>
		</nav>
	);
};

export default BreadcrumbNavigation;
