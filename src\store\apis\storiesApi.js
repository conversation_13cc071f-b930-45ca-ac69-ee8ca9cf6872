import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import { baseQuery } from "./baseConfig";
import { apiEndpoints } from "../../utils/constants";
//https://backend.hollywoodreporterindia.com/api/article/list
// api endpoints related to the categories
export const storiesApi = createApi({
  reducerPath: "storiesApi",
  baseQuery: baseQuery,
  tagTypes: ["TagsData", "SingleStoryData"],
  endpoints: (builder) => ({
    getTextStories: builder.query({
      query: () => apiEndpoints.getHomeList,
    }),

    getFlags: builder.query({
      query: () => apiEndpoints.getFlags,
    }),

    getTags: builder.query({
      query: ({ search, limit, offset }) => ({
        url: `${apiEndpoints.getTags}`,
        params: { search, limit, offset },
      }),
      providesTags: ["TagsData"],
      transformResponse: (response) => {
        return {
          count: response.totalCounts,
          data: response.data,
        };
      },
      // Only have one cache entry because the arg always maps to one string
      serializeQueryArgs: ({ endpointName, queryArgs }) => {
        // Include search in the cache key so different searches have different caches
        return `${endpointName}-${queryArgs.search}`;
      },
      merge: (currentCache, newItems, { arg: { offset } }) => {
        // If offset is 0, it means either:
        // 1. This is the first request
        // 2. Search term has changed (since you'll reset offset to 0)
        // In either case, we want to replace the cache
        if (offset === 0) {
          return newItems;
        }
        // Otherwise, merge the new items with existing cache
        currentCache.data.push(...newItems.data);
        return currentCache;
      },
      forceRefetch({ currentArg, previousArg }) {
        // Refetch if any of the arguments change
        return (
          currentArg.search !== previousArg?.search ||
          currentArg.offset !== previousArg?.offset ||
          currentArg.limit !== previousArg?.limit
        );
      },
    }),

    getVideoStories: builder.query({
      query: ({ search = "", limit = 20, offset = 0 }) => ({
        url: apiEndpoints.getVideosList,
        params: { search, limit, offset },
      }),
      transformResponse: (response) => response.data,
    }),

    postStories: builder.mutation({
      query: ({ data, url }) => ({
        url: url,
        body: data,
        method: "POST",
      }),
      invalidatesTags: ["SingleStoryData"],
    }),
    postVideoStoris: builder.mutation({
      query: (data) => ({
        url: apiEndpoints.postVideoStoriesList,
        body: data,
        method: "POST",
      }),
    }),

    postCategories: builder.mutation({
      query: (data) => ({
        url: apiEndpoints.postCategories,
        body: data,
        method: "POST",
      }),
    }),

    postStoryData: builder.mutation({
      query: ({ data, url }) => ({
        url: url,
        body: data,
        method: "POST",
      }),
    }),

    postTag: builder.mutation({
      query: (data) => ({
        url: apiEndpoints.postTag,
        method: "POST",
        body: data,
      }),
      invalidatesTags: ["TagsData"],
    }),

    updateStatus: builder.mutation({
      query: ({ id, status, publishDate }) => ({
        url: `${apiEndpoints.updateStoryStatus}/${id}`,
        method: "PUT",
        body: { status, publishDate },
      }),
      invalidatesTags: ["SingleStoryData"],
    }),

    deleteStory: builder.mutation({
      query: (id) => ({
        url: `${apiEndpoints.deleteStory}/${id}`,
        method: "PUT",
        body: { status: 2 },
      }),
    }),
    uploadFile: builder.mutation({
      query: (file) => {
        const formData = new FormData();
        formData.append("file", file);
        return {
          url: apiEndpoints.postImage,
          method: "POST",
          body: formData,
        };
      },
    }),
    uploadVideo: builder.mutation({
      query: (file) => {
        const formData = new FormData();
        formData.append("file", file);
        return {
          url: apiEndpoints.postVideo,
          method: "POST",
          body: formData,
        };
      },
    }),
    uploadGallery: builder.mutation({
      query: (files) => {
        const formData = new FormData();
        files.forEach((file) => {
          formData.append("files", file);
        });
        return {
          url: apiEndpoints.postGallery,
          method: "POST",
          body: formData,
        };
      },
    }),

    updateStory: builder.mutation({
      query: ({ data, url }) => ({
        url,
        method: "PATCH",
        body: data,
      }),
      invalidatesTags: ["SingleStoryData"],
    }),
    getStory: builder.query({
      query: (id) => ({
        url: `${apiEndpoints.getSingleStory}/${id}`,
      }),
      providesTags: ["SingleStoryData"],
    }),
  }),
});

// Export hooks for usage in functional components, which are
// auto-generated based on the defined endpoints
export const {
  useGetFlagsQuery,
  useGetTextStoriesQuery,
  useGetTagsQuery,
  useGetVideoStoriesQuery,
  usePostStoriesMutation,
  usePostCategoriesMutation,
  usePostStoryDataMutation,
  usePostTagMutation,
  usePostVideoStorisMutation,
  useDeleteStoryMutation,
  useUpdateStatusMutation,
  useUploadFileMutation,
  useUploadVideoMutation,
  useUploadGalleryMutation,
  useUpdateStoryMutation,
  useGetStoryQuery,
} = storiesApi;
