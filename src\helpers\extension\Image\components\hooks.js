import { useCallback, useEffect, useRef, useState } from "react";
import { toast } from "react-toastify";
import { useUploadFileMutation } from "../../../../store/apis/storiesApi";

export const useUploader = ({ onUpload }) => {
  const [uploadFile, { isLoading }] = useUploadFileMutation();

  const handleFileUpload = useCallback(
    async (file) => {
      try {
        const data = await uploadFile(file).unwrap(); // Unwrap the mutation response
        if (data.status === "success") {
          onUpload(data.data.file.url);
        } else {
          toast.error(data.error || "Error uploading image");
        }
      } catch (error) {
        const errorMessage =
          error?.data?.error || error.message || "Something went wrong";
        console.error(errorMessage);
        toast.error(errorMessage);
      }
    },
    [uploadFile, onUpload]
  );

  return { loading: isLoading, uploadFile: handleFileUpload };
};

export const useFileUpload = () => {
  const fileInput = useRef(null);

  const handleUploadClick = useCallback(() => {
    fileInput.current?.click();
  }, []);

  return { ref: fileInput, handleUploadClick };
};

export const useDropZone = ({ uploader }) => {
  const [isDragging, setIsDragging] = useState(false);
  const [draggedInside, setDraggedInside] = useState(false);

  useEffect(() => {
    const dragStartHandler = () => {
      setIsDragging(true);
    };

    const dragEndHandler = () => {
      setIsDragging(false);
    };

    document.body.addEventListener("dragstart", dragStartHandler);
    document.body.addEventListener("dragend", dragEndHandler);

    return () => {
      document.body.removeEventListener("dragstart", dragStartHandler);
      document.body.removeEventListener("dragend", dragEndHandler);
    };
  }, []);

  const onDrop = useCallback(
    (e) => {
      setDraggedInside(false);
      if (e.dataTransfer.files.length === 0) {
        return;
      }

      const fileList = e.dataTransfer.files;

      const files = [];

      for (let i = 0; i < fileList.length; i += 1) {
        const item = fileList.item(i);
        if (item) {
          files.push(item);
        }
      }

      if (files.some((file) => file.type.indexOf("image") === -1)) {
        return;
      }

      e.preventDefault();

      const filteredFiles = files.filter((f) => f.type.indexOf("image") !== -1);

      const file = filteredFiles.length > 0 ? filteredFiles[0] : undefined;

      if (file) {
        uploader(file);
      }
    },
    [uploader]
  );

  const onDragEnter = () => {
    setDraggedInside(true);
  };

  const onDragLeave = () => {
    setDraggedInside(false);
  };

  return { isDragging, draggedInside, onDragEnter, onDragLeave, onDrop };
};
