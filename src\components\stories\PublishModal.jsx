import React, { useState } from "react";
// import { FiArrowLeft, FiCalendar, FiClock } from "react-icons/fi";
import Button from "../../parts/Button";
import { useDispatch } from "react-redux";
// import { setPublishedData } from "../../store/slices/storiesSlice";
import Outside<PERSON>lickHandler from "react-outside-click-handler";

const PublishModal = ({
  frontendUrl,
  handlePreviewUrl,
  onPublish,
  setIsPublish,
}) => {
  const [screen, setScreen] = useState("initial");
  const [selectedDate, setSelectedDate] = useState("");
     const [selectedHour, setSelectedHour] = useState("");
     const dispatch = useDispatch();

     function convertToISO(dateString) {
       try {
         // Parse the date string into a Date object
         const date = new Date(dateString);

         // Check if the date is valid
         if (isNaN(date.getTime())) {
           throw new Error("Invalid date format");
         }

         // Convert the date to ISO 8601 format
         return date.toISOString();
       } catch (error) {
         console.error(error.message);
         return null;
       }
     }
     // const handleSchedulePublish = async () => {
     //   const scheduledDateTime = new Date(`${selectedDate}T${selectedTime}`);
     //   const isoDate = convertToISO(scheduledDateTime);
     //   onPublish({ status: 4, publishDate: isoDate });
     // };

     const handleSchedulePublish = () => {
       // Convert 12-hour format to 24-hour format for the backend
       const [hour, period] = selectedHour.split(" ");
       let hour24 = parseInt(hour);

       if (period === "PM" && hour24 !== 12) {
         hour24 += 12;
       } else if (period === "AM" && hour24 === 12) {
         hour24 = 0;
       }

       const hour24String = hour24.toString().padStart(2, "0");
       const scheduledDateTime = new Date(`${selectedDate}T${hour24String}:00`);
       const isoDate = convertToISO(scheduledDateTime);
       onPublish({ status: 4, publishDate: isoDate });
     };

     // Generate hours in 12-hour format with AM/PM
     const hours = Array.from({ length: 24 }, (_, i) => {
       const hour = i % 12 || 12; // Convert to 12-hour format
       const period = i < 12 ? "AM" : "PM";
       return `${hour} ${period}`;
     });

     if (screen === "schedule") {
       return (
         <div className="fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 z-50  mt-3 bg-white border border-gray-200 shadow-md rounded-md">
           <OutsideClickHandler
             onOutsideClick={() => {
               setIsPublish(false);
             }}
           >
             <div className="flex flex-col gap-y-5 p-10 md:min-w-96">
               <div className="flex flex-col gap-y-4">
                 <div className="flex flex-col gap-y-2">
                   <label className="text-sm font-medium text-gray-700">
                     Select Date
                   </label>
                   <div className="relative">
                     <input
                       type="date"
                       value={selectedDate}
                       onChange={(e) => setSelectedDate(e.target.value)}
                       className="w-full px-4 py-2 border rounded-md pr-10"
                       min={new Date().toISOString().split("T")[0]}
                     />
                   </div>
                 </div>

                 <div className="flex flex-col gap-y-2">
                   <label className="text-sm font-medium text-gray-700">
                     Select Time
                   </label>
                   <div className="relative">
                     <select
                       value={selectedHour}
                       onChange={(e) => setSelectedHour(e.target.value)}
                       className="w-full px-4 py-2 border rounded-md pr-10"
                     >
                       <option value="">Select hour</option>
                       {hours.map((hour) => (
                         <option key={hour} value={hour}>
                           {hour}
                         </option>
                       ))}
                     </select>
                   </div>
                 </div>
               </div>

               <div className="flex items-center justify-end gap-x-4 mt-4">
                 <Button
                   rounded="full"
                   variant="secondary"
                   onClick={() => setScreen("initial")}
                   className="px-6 py-2 border border-gray-300 rounded-full hover:bg-gray-50"
                 >
                   Back
                 </Button>
                 <Button
                   rounded="full"
                   onClick={handleSchedulePublish}
                   //onClick={handleSchedulePublish}   // for now just update the status of the article

                   disabled={!selectedDate || !selectedHour}
                   className="px-6 py-2 bg-blue-600 text-white rounded-full hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                 >
                   Publish Now
                 </Button>
               </div>
             </div>
           </OutsideClickHandler>
         </div>
       );
     }

  return (
    <div className="fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 z-50 mt-3 bg-white border border-gray-200 shadow-md rounded-md ">
      <OutsideClickHandler
        onOutsideClick={() => {
          setIsPublish(false);
        }}
      >
        <div className="flex flex-col gap-y-5 p-10">
          <h3 className="text-base md:text-lg font-semibold">
            Ready to Publish
          </h3>
          <p>Choose to publish now or schedule for later.</p>
          <div className="flex items-center gap-x-6">
            <div className="py-2 pl-5 pr-2 bg-[#dcdcdc] rounded-md">
              <div className="bg-white rounded px-2 py-1">{frontendUrl}</div>
            </div>
            <Button
              variant="outline"
              rounded="full"
              onClick={handlePreviewUrl}
              className="px-6 py-2 border border-gray-300 rounded-full hover:bg-gray-50"
            >
              Preview
            </Button>
          </div>
          <div className="flex items-center justify-end gap-x-6">
            <Button
              rounded="full"
              variant="secondary"
              onClick={() => setScreen("schedule")}
              className="px-6 py-2 border border-gray-300 rounded-full hover:bg-gray-50"
            >
              Schedule for later
            </Button>
            <Button
              rounded="full"
              onClick={() => onPublish({ status: 1 })}
              className="px-6 py-2 bg-blue-600 text-white rounded-full hover:bg-blue-700"
            >
              Publish Now
            </Button>
          </div>
        </div>
      </OutsideClickHandler>
    </div>
  );
};

export default PublishModal;
