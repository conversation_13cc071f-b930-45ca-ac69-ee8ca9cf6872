import React from "react";
import Filter from "./Filter";
import Gallery from "./Gallery";
import styles from "../MediaLibrary.module.css";

const Left = ({
  isLoading,
  hasMore,
  isMultiple,
  data,
  selectedFiles,
  loadMedia,
  setSelectedFiles,
}) => {
  return (
    <>
      <div className={styles.leftContainer}>
        <Filter />
        <Gallery
          isLoading={isLoading}
          hasMore={hasMore}
          isMultiple={isMultiple}
          data={data}
          selectedFiles={selectedFiles}
          loadMedia={loadMedia}
          setSelectedFiles={setSelectedFiles}
        />
      </div>
    </>
  );
};

export default Left;
