import { useDispatch, useSelector } from "react-redux";
import { usePostStoriesMutation } from "../store/apis/storiesApi";
import { useCallback, useEffect, useRef } from "react";
import { toast } from "react-toastify";
import { cleanFilters, preparePayload } from "./helperFunctions";

const useInfiniteScrollData = ({
  config,
  url,
  ids,
  fetch = true,
  sideFilters = true,
  isShorts = false,
  showOnlyTrashed = false,
  defaultStatus = null,
}) => {
  const dispatch = useDispatch();

  const {
    setDataAction = () => {},
    resetDataAction = () => {},
    setSearchFilterAction = () => {},
    sliceName = "stories",
  } = config;

  const { data, filter, offset, hasMore, status } = useSelector(
    (state) => state[sliceName]
  );

  const { tag, writer, publishedTime, category, addFilter } = useSelector(
    (state) => state.table
  );

  const [postStories, { isLoading, isError, error, isFetching }] =
    usePostStoriesMutation();

  const processFilter = useCallback(() => {
    const { category, section, ...restFilter } = filter;
    if (category) {
      return {
        ...restFilter,
        category: ids,
      };
    } else if (section) {
      return {
        ...restFilter,
        section: ids,
      };
    }
    return restFilter;
  }, [filter, ids]);

  const fetchData = useCallback(
    async (forceReset = false) => {
      if (!hasMore && !forceReset) return;

      const processedFilter = ids ? processFilter() : filter;
      let cleanedFilters = cleanFilters(processedFilter);
      if (defaultStatus) {
        cleanedFilters.status = defaultStatus;
      }
      // Only apply side filters if addFilter is true (i.e., submit button was clicked)
      if (addFilter) {
        const preparedSideFilters = preparePayload({
          tag,
          writer,
          publishedTime,
          category,
        });

        cleanedFilters = { ...cleanedFilters, ...preparedSideFilters };
      }

      if (isShorts) {
        cleanedFilters.type = 2;
        cleanedFilters.isShorts = true;
      }

      try {
        const response = await postStories({
          data: {
            filter: showOnlyTrashed
              ? { ...cleanedFilters, status: 2 }
              : cleanedFilters,
            offset: forceReset ? 0 : offset,
            limit: 20,
          },
          url,
        }).unwrap();

        const responseData = response.data?.data ?? response.data;
        dispatch(
          setDataAction({
            data: responseData,
            replace: forceReset || offset === 0,
          })
        );
      } catch (fetchError) {
        toast.error("Failed to fetch items", { toastId: "fetch_failed" });
        console.error("Failed to fetch items", fetchError);
      }
    },
    [
      dispatch,
      filter,
      offset,
      hasMore,
      postStories,
      setDataAction,
      ids,
      addFilter,
      tag,
      writer,
      publishedTime,
      category,
    ]
  );

  // Only trigger fetch when addFilter changes
  useEffect(() => {
    if (addFilter) {
      fetchData(true);
    }
  }, [addFilter]);

  const resetData = useCallback(async () => {
    dispatch(resetDataAction());
    await fetchData(true);
  }, [dispatch, resetDataAction, fetchData]);

  const handleSearch = useCallback(
    (searchTerm) => {
      dispatch(setSearchFilterAction(searchTerm));
      fetchData(true);
    },
    [dispatch, setSearchFilterAction, fetchData]
  );

  return {
    data,
    filter,
    isLoading,
    isError,
    error,
    offset,
    isFetching,
    fetchData,
    resetData,
    handleSearch,
  };
};

export default useInfiniteScrollData;
