import React, { useState, useEffect } from "react";
import ImageDrop from "../stories/ImageDrop";
import { Input } from "../../parts/FormComponents";
import { useDispatch } from "react-redux";
import {
	clearErrors,
	setAuthorImage,
	setAuthorMetaData,
	setAuthorOgContent,
	setAuthorProfile,
	setErrors,
	setAuthorsMetaTemplateData,
	replaceAuthorMetaData,
} from "../../store/slices/authorsSlice";
import { useSelector } from "react-redux";
import { generateSlugStories, formatAuthorTitle } from "../../utils/helperFunctions";
import { useGetMetaDataAuthorQuery } from "../../store/apis/authorsApi";
const TopProfile = ({ method }) => {
	const dispatch = useDispatch();
	const {
		storiesState: { ogImg, imgsrc },
	} = useSelector((state) => state.authors);

	const { data } = useGetMetaDataAuthorQuery();
	// const [errors, setErrors] = useState({});

	const { firstname, lastname, email, password, subheading, errors, metaDescription } = useSelector(
		(state) => state.authors
	);

	const handleFileChange = (value, name) => {
		dispatch(setAuthorImage({ value, name }));
		dispatch(setAuthorImage({ value, name: "ogImg" }));
	};

	useEffect(() => {
		if (data && method === "POST") {
			dispatch(setAuthorsMetaTemplateData(data));
		}
	}, [data]);

	const handleDataChange = ({ value, name }) => {
		dispatch(setAuthorProfile({ value, name }));
		// Combining firstname and lastname to set the title
		if (name === "firstname" || name === "lastname") {
			// formatting the firstname and the last name here to set the values of the title
			const formattedFirstName = name === "firstname" ? value : firstname;
			const formattedLastname = name === "lastname" ? value : lastname;
			const title = [formattedFirstName, formattedLastname].filter(Boolean).join(" ").trim();

			const slug = [formattedFirstName, formattedLastname].filter(Boolean).join("-").trim();

			dispatch(
				setAuthorOgContent({
					value: title,
					name: "title",
				})
			);
			dispatch(setAuthorMetaData({ value: generateSlugStories(slug), name: "slug" }));
			dispatch(
				replaceAuthorMetaData({
					name: "title",
					value: formatAuthorTitle(title, data?.title),
				})
			);
			dispatch(
				replaceAuthorMetaData({
					name: "description",
					value: formatAuthorTitle(title, data?.description),
				})
			);
		}
		// Clear errors for the current field
		dispatch(setErrors({ ...errors, [name]: null }));
	};

	return (
		<div className="border border-[#c1e4fe] rounded-md bg-white">
			<div className="border-b w-full px-5 text-lg font-semibold  py-5">
				<div>Profile Info</div>
				<p className="text-sm font-normal">Give your author a name and a profile picture</p>
			</div>
			<div className="px-6 py-4 grid grid-cols-2 gap-4 gap-x-5">
				<div>
					<div className="mb-2">Profile Picture</div>
					<ImageDrop
						label={false}
						selectedFiles={imgsrc}
						setSelectedFiles={(file) => handleFileChange(file, "imgsrc")}
					/>
				</div>
				<div className="flex flex-col gap-3 ">
					<div className="grid grid-cols-2 gap-3">
						<div>
							<Input
								label="First Name"
								name="firstname"
								required={true}
								value={firstname}
								id="firstname"
								placeholder=""
								onDebouncedChange={(value) => handleDataChange({ value, name: "firstname" })}
							/>
							{errors.firstname && <p className="text-red-500 text-sm">{errors.firstname}</p>}
						</div>
						<div>
							<Input
								label="Last Name"
								name="lastname"
								required={true}
								value={lastname}
								id="lastname"
								placeholder=""
								onDebouncedChange={(value) => handleDataChange({ value, name: "lastname" })}
							/>{" "}
							{errors.lastname && <p className="text-red-500 text-sm">{errors.lastname}</p>}
						</div>
					</div>
					<div className="mt-2">
						<Input
							label="Email"
							name="email"
							required={true}
							value={email}
							readOnly={method == "PUT"}
							id="email"
							placeholder=""
							onDebouncedChange={(value) => handleDataChange({ value, name: "email" })}
						/>
						{errors.email && <p className="text-red-500 text-sm">{errors.email}</p>}
					</div>
					{method == "POST" && (
						<div className="mt-2">
							<Input
								label="Password"
								name="password"
								type="password"
								required={true}
								value={password}
								readOnly={method == "PUT"}
								id="password"
								placeholder=""
								onDebouncedChange={(value) => handleDataChange({ value, name: "password" })}
							/>
							{errors.password && <p className="text-red-500 text-sm">{errors.password}</p>}
						</div>
					)}
					<div className="mt-2">
						<Input
							label="Subheading"
							name="subheading"
							required={true}
							value={subheading}
							id="subheading"
							placeholder=""
							onDebouncedChange={(value) => handleDataChange({ value, name: "subheading" })}
						/>
						{errors.subheading && <p className="text-red-500 text-sm">{errors.subheading}</p>}
					</div>
				</div>
			</div>
		</div>
	);
};

export default TopProfile;
