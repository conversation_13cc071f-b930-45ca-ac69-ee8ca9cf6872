import React, { useEffect, useState } from "react";
import { RxCross2 } from "react-icons/rx";
import { FiPlus } from "react-icons/fi";
import MultipleFile from "../../../../public/svg/MultipleFile";
import Accordion, { AccordionProvider } from "../../../parts/Accordion";
import { Input } from "../../../parts/FormComponents";
import { IoIosCheckmarkCircle } from "react-icons/io";
import {
	useGetImageTagsListMutation,
	useCreateImageTagMutation,
	useAddMediaTagMutation,
	useDeleteMediaTagMutation,
	useUpdateMediaMutation,
} from "../../../store/apis/mediaLibraryApi";
import { toast } from "react-toastify";
import { BiLoaderAlt } from "react-icons/bi";

const FileInfo = ({ selectedMedia = [], isMultiple = false, onRefresh }) => {
	const [fileInfo, setFileInfo] = useState({
		name: "",
		type: "",
		size: null,
		date: "",
		resolution: {},
	});
	const [copied, setCopied] = useState(false);
	const [newTag, setNewTag] = useState("");
	const [mediaTags, setMediaTags] = useState([]);
	const [searchResults, setSearchResults] = useState([]);
	const [isSearching, setIsSearching] = useState(false);
	const [seoDetails, setSeoDetails] = useState({
		caption: "",
		courtesy: "",
		alt: "",
	});

	const [getImageTagsList, { isLoading: isLoadingTags }] = useGetImageTagsListMutation();
	const [createImageTag, { isLoading: isCreatingTag }] = useCreateImageTagMutation();
	const [addMediaTag] = useAddMediaTagMutation();
	const [deleteMediaTag] = useDeleteMediaTagMutation();
	const [updateMedia, { isLoading: isUpdatingMedia }] = useUpdateMediaMutation();

	useEffect(() => {
		if (!selectedMedia.length || isMultiple) {
			// Reset SEO details when no media is selected
			setSeoDetails({ caption: "", courtesy: "", alt: "" });
			return;
		}

		const mediaItem = selectedMedia[0];
		if (!mediaItem) {
			setSeoDetails({ caption: "", courtesy: "", alt: "" });
			return;
		}

		// Force update SEO details from media item
		setSeoDetails({
			caption: mediaItem.caption || "",
			courtesy: mediaItem.courtesy || "",
			alt: mediaItem.alt || "",
		});

		// Fetch tags for the selected media item
		const fetchMediaTags = async () => {
			if (mediaItem.imageTags && mediaItem.imageTags.length > 0) {
				try {
					const imageTagIds = mediaItem.imageTags.map((tag) => tag.imageTagId);
					const response = await getImageTagsList({
						limit: 20,
						offset: 0,
						search: "",
						filters: {
							ids: imageTagIds,
						},
					}).unwrap();

					// Set the fetched tags
					setMediaTags(response.data || []);
				} catch (error) {
					console.error("Failed to fetch media tags:", error);
					// Fallback to imageTags from media item
					setMediaTags(mediaItem.imageTags || []);
				}
			} else {
				setMediaTags([]);
			}
		};

		fetchMediaTags();

		const fetchFileInfo = async () => {
			try {
				const response = await fetch(mediaItem.url, { method: "HEAD" });
				const contentType = response.headers.get("Content-Type");
				const contentLength = response.headers.get("Content-Length");
				const lastModified = response.headers.get("Last-Modified");

				const dimensions = await getImageDimensions(mediaItem.url);

				setFileInfo({
					name: mediaItem.title || "Unknown",
					type: contentType || "Unknown",
					size: contentLength ? formatBytes(+contentLength) : null,
					date: mediaItem.createdAt
						? new Date(mediaItem.createdAt).toLocaleDateString()
						: lastModified
						? new Date(lastModified).toLocaleDateString()
						: "",
					resolution: dimensions,
				});
			} catch (error) {
				console.error("Failed to get file info:", error);
				// Fallback to basic info from media item
				setFileInfo({
					name: mediaItem.title || "Unknown",
					type: "Unknown",
					size: null,
					date: mediaItem.createdAt ? new Date(mediaItem.createdAt).toLocaleDateString() : "",
					resolution: {},
				});
			}
		};

		fetchFileInfo();
	}, [selectedMedia, isMultiple, getImageTagsList, selectedMedia[0]?._id, selectedMedia[0]?.caption, selectedMedia[0]?.courtesy, selectedMedia[0]?.alt]);

	const formatBytes = (bytes, decimals = 2) => {
		if (!bytes) return "0 Bytes";
		const k = 1024;
		const dm = decimals < 0 ? 0 : decimals;
		const sizes = ["Bytes", "KB", "MB", "GB", "TB"];
		const i = Math.floor(Math.log(bytes) / Math.log(k));
		return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + " " + sizes[i];
	};

	const getImageDimensions = (url) =>
		new Promise((resolve, reject) => {
			const img = new Image();
			img.src = url;
			img.onload = () => resolve({ width: img.width, height: img.height });
			img.onerror = () => reject(new Error("Failed to load image"));
		});

	const copyTextToClipboard = async () => {
		try {
			await navigator.clipboard.writeText(selectedMedia[0]?.url || "");
			setCopied(true);
			setTimeout(() => setCopied(false), 2000);
		} catch (err) {
			console.error("Failed to copy text: ", err);
		}
	};

	const handleRemoveTag = async (tagId) => {
		const updatedTags = mediaTags.filter((tag) => tag._id !== tagId);
		setMediaTags(updatedTags);

		// Remove media tag via API
		try {
			await deleteMediaTag({
				mediaIds: [selectedMedia[0]._id],
				tagIds: [tagId],
			}).unwrap();
			onRefresh?.();
		} catch (error) {
			console.error("Failed to remove media tag:", error);
			// Revert on error
			setMediaTags(mediaTags);
		}
	};

	const handleTagSearch = async (searchTerm) => {
		if (!searchTerm.trim()) {
			setSearchResults([]);
			return;
		}

		setIsSearching(true);
		try {
			const response = await getImageTagsList({
				limit: 20,
				offset: 0,
				search: searchTerm.trim(),
			}).unwrap();

			setSearchResults(response.data || []);
		} catch (error) {
			console.error("Failed to search tags:", error);
		} finally {
			setIsSearching(false);
		}
	};

	const handleSelectTag = async (tag) => {
		if (mediaTags.some((t) => t._id === tag._id)) return;

		const updatedTags = [...mediaTags, tag];
		setMediaTags(updatedTags);

		try {
			const allTagIds = updatedTags.map((t) => t._id);
			await addMediaTag({
				mediaIds: [selectedMedia[0]._id],
				tagIds: allTagIds,
			}).then((res) => {
				if (res.data.status === "success") {
					onRefresh?.();
				}
				if (res.data.status === "failure") {
					toast.error("Tag Already Exists.");
				}
			});
		} catch (error) {
			console.error("Failed to add tag:", error);
			setMediaTags(mediaTags);
		}
	};

	const handleCreateNewTag = async () => {
		if (!newTag.trim()) return;

		try {
			const result = await createImageTag({ name: newTag.trim() }).unwrap();
			const newTagObj = { _id: result._id, name: newTag.trim() };
			const updatedTags = [...mediaTags, newTagObj];
			setMediaTags(updatedTags);
			setNewTag("");
			setSearchResults([]);

			const allTagIds = [...mediaTags.map((tag) => tag._id), result.data._id];

			await addMediaTag({
				mediaIds: [selectedMedia[0]._id],
				tagIds: allTagIds,
			}).then((res) => {
				if (res.data.status === "success") {
					onRefresh?.();
				}
				if (res.data.status === "failure") {
					toast.error("Tag Already Exists.");
				}
			});
		} catch (error) {
			console.error("Failed to create tag:", error);
			if (error.data?.message === "Image tag with this name already exists") {
				toast.error("Tag already exists");
			}
		}
	};

	const handleSeoDetailsChange = (field, value) => {
		setSeoDetails((prev) => ({ ...prev, [field]: value }));
	};

	const handleSaveSeoDetails = async () => {
		if (!selectedMedia[0]?._id) return;

		try {
			await updateMedia({
				mediaId: selectedMedia[0]._id,
				caption: seoDetails.caption,
				courtesy: seoDetails.courtesy,
				alt: seoDetails.alt,
			}).unwrap();
			toast.success("SEO details updated successfully");
			onRefresh?.();
		} catch (error) {
			console.error("Failed to update SEO details:", error);
			toast.error("Failed to update SEO details");
		}
	};

	if (!selectedMedia.length) {
		return (
			<div>
				<img
					src="/svg/placeholder-file.svg"
					alt="Placeholder"
					className="h-32 w-full object-cover mt-[30px] mx-auto"
				/>
				<div className="text-sm mt-10">No file selected</div>
			</div>
		);
	}

	if (isMultiple) {
		return (
			<div>
				{/* Multiple Files Preview */}
				<div className="relative flex flex-col justify-center group cursor-move mb-4">
					<div className="w-full mx-auto flex items-center justify-center aspect-square overflow-hidden bg-gray-100 relative transition-all duration-100 ease-in-out">
						<MultipleFile />
					</div>
					<div className="text-sm mt-4">{`${selectedMedia.length} items selected`}</div>
				</div>

				{/* File URLs for multiple selection */}
				<div className="text-sm">
					<div className="flex justify-between items-center mb-2">
						<span className="font-medium">File URLs:</span>
						<button
							onClick={copyTextToClipboard}
							className="text-blue-500 hover:text-blue-700 text-xs"
						>
							{copied ? "Copied!" : "Copy"}
						</button>
					</div>
					<div className="bg-gray-100 p-2 rounded text-xs break-all">
						{[
							...new Set(
								selectedMedia.map((media) => media.url).filter((url) => url && url.trim() !== "")
							),
						].join(", ")}
					</div>
				</div>
			</div>
		);
	}

	return (
		<div>
			{/* Image Preview */}
			<div className="relative flex flex-col justify-center group cursor-move mb-6">
				<div className="w-full mx-auto aspect-square overflow-hidden bg-gray-100 relative transition-all duration-100 ease-in-out">
					<img
						className="absolute inset-0 w-full h-full object-contain transition-all duration-100"
						src={selectedMedia[0]?.url || ""}
						alt={selectedMedia[0]?.alt || selectedMedia[0]?.title || "Selected"}
					/>
				</div>
				<div className="text-sm mt-4" title={fileInfo.name}>
					{fileInfo.name.length > 20 ? fileInfo.name.substring(0, 20) + "..." : fileInfo.name}
				</div>
			</div>

			{/* Accordions */}
			<AccordionProvider>
				<div className="border-t fileInfoAccordio" id="seoDetail">
					{/* Tags Accordion */}
					<Accordion customClass={"border-b"} title="SEO Details" index={0}>
						<div className="space-y-4">
							<Input
								name="caption"
								label="Caption"
								placeholder="Enter caption"
								value={seoDetails.caption}
								onDebouncedChange={(value) => handleSeoDetailsChange("caption", value)}
							/>
							<Input
								name="courtesy"
								label="Courtesy"
								placeholder="Enter courtesy"
								value={seoDetails.courtesy}
								onDebouncedChange={(value) => handleSeoDetailsChange("courtesy", value)}
							/>
							<Input
								name="altname"
								label="Alt Name"
								placeholder="Enter alt name"
								value={seoDetails.alt}
								onDebouncedChange={(value) => handleSeoDetailsChange("alt", value)}
							/>
							<button
								onClick={handleSaveSeoDetails}
								disabled={isUpdatingMedia}
								className="px-4 py-1 bg-blue-500 text-white rounded-full text-sm hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed"
							>
								{isUpdatingMedia ? "Saving..." : "Save"}
							</button>
						</div>
					</Accordion>

					<Accordion customClass={"border-b"} title="Tags" index={1}>
						<div className="space-y-3">
							<div className="flex items-center gap-2">
								<div className="relative flex-1">
									<input
										type="text"
										placeholder="Search or create tag"
										value={newTag}
										onChange={(e) => {
											setNewTag(e.target.value);
											handleTagSearch(e.target.value);
										}}
										onKeyDown={(e) => e.key === "Enter" && handleCreateNewTag()}
										className="border text-xs border-gray-300 rounded-md px-3 py-2 w-full focus:outline-none focus:border-blue-500"
									/>
									{isSearching && (
										<div className="absolute right-3 top-1/2 transform -translate-y-1/2">
											<div className="animate-spin rounded-full h-3 w-3 border-2 border-gray-300 border-t-blue-500"></div>
										</div>
									)}
								</div>
								{newTag && isCreatingTag ? (
									<BiLoaderAlt className="text-2xl text-primary animate-spin" />
								) : (
									newTag &&
									!searchResults.some((tag) => tag.name.toLowerCase() === newTag.toLowerCase()) && (
										<IoIosCheckmarkCircle
											className="text-2xl hover:cursor-pointer text-primary"
											onClick={handleCreateNewTag}
										/>
									)
								)}
							</div>

							{/* Search Results as Chips */}
							{searchResults.length > 0 && (
								<div className="flex flex-wrap gap-2">
									{searchResults
										.filter((tag) => !mediaTags.some((mt) => mt._id === tag._id))
										.map((tag) => (
											<button
												key={tag._id}
												onClick={() => handleSelectTag(tag)}
												className="px-3 py-1 border rounded-full bg-gray-100 text-black text-xs hover:bg-gray-200"
											>
												{tag.name}
											</button>
										))}
								</div>
							)}
							{/* Current Media Tags */}
							<div className="flex flex-wrap gap-2">
								{isLoadingTags
									? null
									: mediaTags.map((tag) => (
											<span
												key={tag._id}
												className="flex items-center gap-1 px-2 py-1 rounded-full bg-primary text-white text-xs font-medium"
											>
												{tag.name}
												<RxCross2
													className="text-[12px] cursor-pointer hover:bg-blue-600 rounded-full p-0.5"
													onClick={() => handleRemoveTag(tag._id)}
												/>
											</span>
									  ))}
							</div>
						</div>
					</Accordion>

					{/* File Information Accordion */}
					<Accordion customClass={"border-b"} title="File Information" index={2}>
						<div className="space-y-4">
							{/* File URL */}
							<div className="text-sm">
								{/* <div className="flex justify-between items-center mb-2">
									<span className="font-medium">File URL:</span>
									<button
										onClick={copyTextToClipboard}
										className="text-blue-500 hover:text-blue-700 text-xs"
									>
										{copied ? "Copied!" : "Copy"}
									</button>
								</div>
								<div className="bg-gray-100 p-2 rounded text-xs break-all">
									{selectedMedia[0]?.url || ""}
								</div> */}
							</div>

							{/* File Details */}
							<div className="text-sm space-y-2">
								{selectedMedia[0]?.title && (
									<div className="flex justify-between">
										<span className="font-medium">Title:</span>
										<span className="text-right">{selectedMedia[0].title}</span>
									</div>
								)}
								{selectedMedia[0]?.alt && (
									<div className="flex justify-between">
										<span className="font-medium">Alt Text:</span>
										<span className="text-right">{selectedMedia[0].alt}</span>
									</div>
								)}
								{selectedMedia[0]?.caption && (
									<div className="flex justify-between">
										<span className="font-medium">Caption:</span>
										<span className="text-right">{selectedMedia[0].caption}</span>
									</div>
								)}
								<div className="flex justify-between">
									<span className="font-medium">Type:</span>
									<span>{fileInfo.type}</span>
								</div>
								{fileInfo.size && (
									<div className="flex justify-between">
										<span className="font-medium">Size:</span>
										<span>{fileInfo.size}</span>
									</div>
								)}
								{fileInfo.resolution.width && fileInfo.resolution.height && (
									<div className="flex justify-between">
										<span className="font-medium">Dimensions:</span>
										<span>
											{fileInfo.resolution.width} × {fileInfo.resolution.height}
										</span>
									</div>
								)}
								{/* {fileInfo.date && (
									<div className="flex justify-between">
										<span className="font-medium">Date:</span>
										<span>{fileInfo.date}</span>
									</div>
								)}
								{selectedMedia[0]?.keywords && (
									<div className="flex justify-between">
										<span className="font-medium">Keywords:</span>
										<span className="text-right">{selectedMedia[0].keywords}</span>
									</div>
								)} */}
								{selectedMedia[0]?.courtesy && (
									<div className="flex justify-between">
										<span className="font-medium">Courtesy:</span>
										<span className="text-right">{selectedMedia[0].courtesy}</span>
									</div>
								)}
								{selectedMedia[0]?.folderInfo && (
									<div className="flex justify-between">
										<span className="font-medium">Folder:</span>
										<span className="text-right">{selectedMedia[0].folderInfo.name}</span>
									</div>
								)}
							</div>
						</div>
					</Accordion>
				</div>
			</AccordionProvider>
		</div>
	);
};

export default FileInfo;
