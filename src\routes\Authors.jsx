import React, { useEffect, useMemo, useState } from "react";
import Container from "../parts/Container";
import BreadCrumb from "../parts/BreadCrumb";
import Button, { RoundedIconsButton } from "../parts/Button";
import { BiChevronRight, BiPlus } from "react-icons/bi";
import Table from "../parts/Table";
import { useDispatch, useSelector } from "react-redux";
import Filters from "../parts/Filters";
import { FiEdit } from "react-icons/fi";
import { FaRegTrashAlt } from "react-icons/fa";
import { Link, useNavigate } from "react-router-dom";
import { increaseFilterLimitCount } from "../store/slices/storiesSlice";
import {
  useDeleteAuthorMutation,
  useGetAuthorsListQuery,
} from "../store/apis/authorsApi";
import { toast } from "react-toastify";
import { incrementOffset } from "../store/slices/authorsSlice";
import useConfirmationModal from "../utils/useConfirmationModal";
import ConfirmationModal from "../parts/ConfirmationModal";
import { handleViewClickInNewTab } from "../utils/helperFunctions";
// import { incrementOffset } from "../store/slices/authorsSlice";

const Authors = () => {
  const navigate = useNavigate();
  const [selectedRows, setSelectedRows] = useState([]);
  const {
    filter: { search },
    limit,
    offset,
  } = useSelector((state) => state.authors);
  const dispatch = useDispatch();

  const [deleteAuthor, { isLoading: isDeleting, error: deleteError }] =
    useDeleteAuthorMutation();
  // confirmation modal hook
  const { isModalOpen, rowIdToDelete, openModal, closeModal } =
    useConfirmationModal();

  // fething the authors
  const { data, isLoading, isError, error, isFetching } =
    useGetAuthorsListQuery({
      search,
      limit,
      offset,
    });

  // handling the error here
  if (isError) {
    console.log(error);
    if (error.status === 401) {
      toast.error("Session Expired", { toastId: "author_fetch_error" });
      navigate("/signin");
    }
  }

  // Make API call to fetch more data
  const fetchMoreData = () => {
    if (
      data?.data?.length > 0 && // Check if `data.data` has items
      !isFetching && // Ensure no ongoing fetch
      data?.data?.length < data?.count // Check if there are more items to fetch
    ) {
      dispatch(incrementOffset());
    }
  };

  // used to delete the authors
  const handleDelete = () => {
    deleteAuthor(rowIdToDelete)
      .then((res) => {
        if (res.data.status === "success") {
          toast.success("Author deleted successfully!");
        } else {
          toast.error("Failed to delete author.");
        }
      })
      .catch((err) => {
        toast.error("Failed to delete author.");
        console.log(err);
      })
      .finally(() => {
        closeModal();
      });
  };

  // columns for the authors table
  const columns = useMemo(
    () => [
      // {
      //   accessorKey: "_id",
      //   enableSorting: false,
      //   header: ({ table }) => (
      //     <input
      //       type="checkbox"
      //       checked={table.getIsAllRowsSelected()}
      //       indeterminate={table.getIsSomeRowsSelected()}
      //       onChange={table.getToggleAllRowsSelectedHandler()} //or getToggleAllPageRowsSelectedHandler
      //     />
      //   ),
      //   cell: ({ row }) => (
      //     <input
      //       type="checkbox"
      //       checked={row.getIsSelected()}
      //       disabled={!row.getCanSelect()}
      //       onChange={row.getToggleSelectedHandler()}
      //     />
      //   ),
      // },
      {
        accessorKey: "name",
        id: "name",
        width: 200,
        header: () => "Author Name",
        cell: ({ row }) => {
          return (
            <div className="flex items-center gap-x-2 font-semibold">
              {row.original.imgsrc ? (
                <img
                  src={row.original.imgsrc}
                  alt=""
                  className="w-10 h-10 object-cover rounded-full"
                />
              ) : (
                <div class="relative w-10 h-10 overflow-hidden bg-gray-100 rounded-full ">
                  <svg
                    class="absolute w-12 h-12 text-gray-400 -left-1"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"
                      clip-rule="evenodd"
                    ></path>
                  </svg>
                </div>
              )}
              <div>
                <div className="line-clamp-2">{row.original.name}</div>
                <div className="text-[#969696] text-xs font-normal flex items-center space-x-2">
                  <span>Site Member</span>
                </div>
              </div>
            </div>
          );
        },
      },

      {
        accessorKey: "postCount",
        header: () => "Story Count",
        cell: ({ row }) => (
          <div className="line-clamp-3">{row.original.postCount} Stories</div>
        ),
      },
      {
        accessorKey: "status",
        header: "Action",
        cell: ({ row }) => {
          return (
            <div className="flex items-center gap-x-2">
              <RoundedIconsButton
                onClick={() =>
                  handleViewClickInNewTab(
                    `/admin/authors/edit/${row.original._id}`
                  )
                }
              >
                <FiEdit className="h-[15px] w-[15px]" />
              </RoundedIconsButton>
              <RoundedIconsButton
                onClick={() => {
                  openModal(row.original._id);
                }}
              >
                <FaRegTrashAlt className="h-[15px] w-[15px]" />
              </RoundedIconsButton>
            </div>
          );
        },
      },
    ],
    []
  );

  // funtion to perfrom action on row selection in the table
  const handleRowSelectionChange = (rowIds) => {
    console.log(rowIds, " rows ids");
  };

  // used to perform actions when a row is selected
  const handleRowSelect = (rows) => {
    console.log(rows, " rows");
    setSelectedRows(rows);
    // Make API call or perform other actions with the selected rows
  };

  // used to handle the filter change on the table for the status
  const handleFilterChange = (filterName, value) => {
    console.log(filterName, value, " filter names and value");
    // Update filter state and fetch new data
  };

  return (
    <Container>
      <div className="lg:flex items-center justify-between mb-5">
        <div>
          {/* <div className="flex items-center  text-sm">
            <Link
              to={"/admin/authors"}
              className="hover:bg-white text-blackShade rounded-full px-3 py-1 bg-transparent "
            >
              Authors
            </Link>
          </div> */}
          <BreadCrumb
            title={"Authors"}
            description={
              "Manage authors for your story, create and customize their public profiles."
            }
          />
        </div>

        <Button
          rounded="full"
          size="sm"
          customClasses="mt-2 lg:mt-0 pb-1.5 pt-1.5"
          onClick={() => navigate("/admin/authors/create")}
        >
          <BiPlus /> Create Author
        </Button>
      </div>
      <Table
        module="authors"
        data={data ? data.data : []}
        isLoading={isLoading}
        actionColumn={3}
        columns={columns}
        offset={offset}
        handleRowSelectionChange={handleRowSelectionChange}
        fetchMoreData={fetchMoreData}
        customClass={"categories"}
        isFetching={isFetching}
      />
      <Filters />
      <ConfirmationModal
        isOpen={isModalOpen}
        isLoading={isDeleting}
        toggleModal={closeModal}
        message="Are you sure you want to delete this author?"
        onConfirm={handleDelete}
      />
    </Container>
  );
};

export default Authors;
