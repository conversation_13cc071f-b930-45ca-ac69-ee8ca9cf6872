import React, { useState } from "react";
import { IoIosStar, IoIosStarOutline } from "react-icons/io";
import { SlStar } from "react-icons/sl";
import { usePostStoryDataMutation } from "../../store/apis/storiesApi";
import { useDispatch, useSelector } from "react-redux";
import { setHighlightedSubCategory } from "../../store/slices/storiesSlice";
import { toast } from "react-toastify";
import { apiEndpoints } from "../../utils/constants";
import { setHighlightedVideoSubCategory } from "../../store/slices/videoStorySlice";

const StarButton = ({
  subCategoryId,
  isHighlighted,
  highligthedSubCategory,
  type,
}) => {
  const dispatch = useDispatch();
  const [postStoryData, { isLoading, data, isError, error }] =
    usePostStoryDataMutation();

  // displaying the error message here
  if (error) {
    toast.error(error.data.message, { toastId: "highlighted_error" });
  }

  // making the api call here to send the highlighted data
  const handleStarClick = () => {
    if (type === "stories") {
      dispatch(setHighlightedSubCategory(subCategoryId));
      postStoryData({
        data: { subcategoryId: subCategoryId },
        url: apiEndpoints.postHighlightSubCategory,
      });
    }
    if (type === "videoStory") {
      dispatch(setHighlightedVideoSubCategory(subCategoryId));
      // postStoryData({
      //   data: { subCategoryId },
      //   url: apiEndpoints.postVideoHighlightSubCategory,
      // });
    }
  };
  return (
    <>
      {isHighlighted && highligthedSubCategory === subCategoryId ? (
        <IoIosStar
          className="text-xl text-fadeGray"
          onClick={handleStarClick}
        />
      ) : (
        <IoIosStarOutline
          className="text-xl text-fadeGray"
          onClick={handleStarClick}
        />
      )}
    </>
  );
};

export default StarButton;
