import React from "react";
import { useGetFlagsQuery } from "../../store/apis/storiesApi";
import { useDispatch, useSelector } from "react-redux";
import { toggleFlag } from "../../store/slices/storiesSlice";
import { toast } from "react-toastify";
import { storyType, storyTypeNumbers } from "../../utils/constants";
import { toggleVideoFlag } from "../../store/slices/videoStorySlice";
import { toggleWebStoryFlag } from "../../store/slices/webstorySlice";

const Flags = ({ type = "stories", isShorts = false }) => {
  const dispatch = useDispatch();
  const {
    storiesState: { section },
  } = useSelector((state) => state[storyType[type]]);
  const { data, isLoading, isError, error } = useGetFlagsQuery();

  // displaying the error message here
  if (isError) {
    toast.error(error.data.message, { toastId: "getFlag_error" });
  }

  // handling the flag toggling here
  const handleFlagClick = (flagId) => {
    if (type === "stories") {
      dispatch(toggleFlag(flagId));
    }
    if (type === "videoStory") {
      dispatch(toggleVideoFlag(flagId));
    }
    if (type === "webStory") {
      dispatch(toggleWebStoryFlag(flagId));
    }
  };

  return (
    <div className="py-5">
      <h3>Assign story to one or more flags for readers to find them.</h3>
      <div className="text-sm flex flex-col gap-y-4 mt-4 ">
        {data
          ? data.data.map((flag) => {
              return (
                <div key={flag._id}>
                  <div className="text-[#00000080]">{flag.name}</div>
                  <div className="pl-2 mt-2 flex flex-col gap-y-2 w-full">
                    {flag.section
                      .filter((subFlag) => {
                        // Filter logic based on the story type
                        if (storyTypeNumbers[type] === 0) {
                          return subFlag.storiesType === 0; // For storyType 0
                        }

                        if (storyTypeNumbers[type] === 1 && !isShorts)
                          return subFlag.storiesType === 1; // For videoStory
                        if (isShorts) return subFlag.storiesType === 2; // For shorts}
                        if (storyTypeNumbers[type] === 3)
                          return subFlag.storiesType === 3; // For webStory
                        return true; // For any other type, include all
                      })
                      .map((subFlag) => (
                        <div
                          key={subFlag._id}
                          className="flex items-center gap-x-3 w-full pr-6"
                        >
                          <input
                            type="checkbox"
                            value={subFlag._id}
                            name={subFlag._id}
                            id={subFlag._id}
                            checked={section.includes(subFlag._id)}
                            className="w-4 h-4"
                            onChange={() => handleFlagClick(subFlag._id)}
                          />
                          <label
                            htmlFor={subFlag._id}
                            className="text-fadeGray"
                          >
                            {subFlag.name}
                          </label>
                        </div>
                      ))}
                  </div>
                </div>
              );
            })
          : null}
      </div>
    </div>
  );
};

export default Flags;
