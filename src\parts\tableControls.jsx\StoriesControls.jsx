import React, { useEffect, useState } from "react";
import { DebouncedInput } from "../FormComponents";
import Button from "../Button";
import { useDispatch, useSelector } from "react-redux";
import { CiFilter } from "react-icons/ci";
import { toggleFilters } from "../../store/slices/tableSlice";
import {
  videoFilterStoryStatus,
  videoStoriesInputFilter,
} from "../../store/slices/videoStorySlice";
import {
  filterStoryStatus,
  storiesInputFilter,
} from "../../store/slices/storiesSlice";
import {
  categoriesInputFilter,
  subCategoryFilterStoryStatus,
} from "../../store/slices/categoriesSlice";
import {
  flaggedStoryFilterStoryStatus,
  flaggedStoryInputFilter,
} from "../../store/slices/flaggedStoriesSlice";
import { useLocation, useNavigate, useSearchParams } from "react-router-dom";
import {
  webStoriesInputFilter,
  webStoryFilterStoryStatus,
} from "../../store/slices/webstorySlice";

const tabs = [
  { label: "All", value: "all", actualVal: null },
  { label: "Published", value: "published", actualVal: 1 },
  { label: "Unpublished", value: "unpublished", actualVal: 0 },
  { label: "Drafts", value: "drafts", actualVal: 3 },
  { label: "Scheduled", value: "scheduled", actualVal: 4 },
];

const StoriesControls = ({ module = "stories", showOnlyAll }) => {
  const [searchParams] = useSearchParams();
  let filtredTab;
  const filterStatus = searchParams.get("status");
  if (filterStatus) {
    filtredTab = tabs.find((t) => t.value === filterStatus);
  } else {
    filtredTab = { value: "all" };
  }
  const [active, setActive] = useState(filtredTab.value);
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation();
  const {
    filter: { search },
  } = useSelector((state) => state[module]);
  // Click handler to set the active state and make API call
  const handleTabClick = (value) => {
    setActive(value);
    if (module === "stories") {
      navigate({
        pathname: "/admin/text-stories",
        search: `status=${value}`,
      });
      dispatch(filterStoryStatus(value));
    }
    if (module === "videoStory") {
      navigate({
        pathname: "/admin/video-stories",
        search: `status=${value}`,
      });
      dispatch(videoFilterStoryStatus(value));
    }
    if (module === "categories") {
      const searchParams = new URLSearchParams(location.search);
      searchParams.set("status", value);
      navigate({
        pathname: location.pathname,
        search: searchParams.toString(),
      });
      dispatch(subCategoryFilterStoryStatus(value));
    }
    if (module === "flaggedStory") {
      dispatch(flaggedStoryFilterStoryStatus(value));
    }
    if (module === "webStory") {
      navigate({
        pathname: "/admin/web-stories",
        search: `status=${value}`,
      });
      dispatch(webStoryFilterStoryStatus(value));
    }
    // This will automatically refetch based on the active value with RTK Query
  };

  const handleFilterClick = () => {
    dispatch(toggleFilters());
  };

  const finalTab = showOnlyAll ? [tabs[0]] : tabs;
  return (
    <div className="px-5 gap-3 md:gap-0 flex flex-col md:flex-row py-1 bg-white items-center w-full justify-between sticky top-0 z-20">
      <div className="font-medium text-center">
        <ul className="flex flex-wrap -mb-px">
          {finalTab.map((tab) => (
            <li key={tab.value} className="me-2">
              <button
                onClick={() => handleTabClick(tab.value)}
                className={`inline-block px-4 h-10 border-b-2 rounded-t-lg hover:text-gray-600 transition-all duration-300 ${
                  active === tab.value
                    ? "text-primary border-primary"
                    : "border-transparent"
                }`}
              >
                <h3>{tab.label}</h3>
              </button>
            </li>
          ))}
        </ul>
      </div>
      <div className="flex gap-x-10 items-center">
        <DebouncedInput
          type="text"
          value={search ?? ""}
          onChange={(value) => {
            if (module === "stories") {
              dispatch(storiesInputFilter(value));
            }
            if (module === "videoStory") {
              dispatch(videoStoriesInputFilter(value));
            }
            if (module === "webStory") {
              dispatch(webStoriesInputFilter(value));
            }
            if (module === "categories" || module === "subcategories") {
              dispatch(categoriesInputFilter(value));
            }
            if (module === "flaggedStory" || module === "subcategories") {
              dispatch(flaggedStoryInputFilter(value));
            }
          }}
          placeholder="Search..."
          className="border shadow rounded w-full md:w-auto h-8"
        />
        <Button
          variant="secondary"
          rounded="full"
          customClasses="flex items-center gap-2 mt-3 md:mt-0 h-8"
          onClick={handleFilterClick}
        >
          <CiFilter />
          <span>Filter</span>
        </Button>
      </div>
    </div>
  );
};

export default StoriesControls;
