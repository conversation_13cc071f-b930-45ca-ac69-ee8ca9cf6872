import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import { baseQuery } from "./baseConfig";
import { apiEndpoints } from "../../utils/constants";

export const mediaLibraryApi = createApi({
	reducerPath: "mediaLibraryApi",
	baseQuery: baseQuery,
	tagTypes: ["MediaData", "UploadedMediaData", "FolderData"],
	endpoints: (builder) => ({
		getMedia: builder.mutation({
			query: ({ search = "", body }) => ({
				url: apiEndpoints.getMediaList,
				method: "POST",
				params: { search },
				body: body,
			}),
			invalidatesTags: ["MediaData"],
		}),
		getFolderList: builder.mutation({
			query: (body) => ({
				url: apiEndpoints.postFolderList,
				method: "POST",
				body: body,
			}),
			invalidatesTags: ["FolderData"],
		}),
		getMediaLibraryList: builder.mutation({
			query: (body) => ({
				url: apiEndpoints.postMediaLibraryList,
				method: "POST",
				body: body,
			}),
			invalidatesTags: ["MediaData"],
		}),
		uploadMedia: builder.mutation({
			query: (formData) => ({
				url: apiEndpoints.postMedia,
				body: formData,
				method: "POST",
			}),
			invalidatesTags: ["UploadedMediaData"],
		}),
		updateMediaTags: builder.mutation({
			query: ({ mediaId, tags }) => ({
				url: `${apiEndpoints.patchMedia}/${mediaId}/tags`,
				method: "PATCH",
				body: { tags },
			}),
			invalidatesTags: ["MediaData"],
		}),
		createFolder: builder.mutation({
			query: (folderData) => ({
				url: apiEndpoints.postFolder,
				method: "POST",
				body: folderData,
			}),
			invalidatesTags: ["FolderData"],
		}),
		deleteFolder: builder.mutation({
			query: (folderId) => ({
				url: `${apiEndpoints.deleteFolder}/${folderId}`,
				method: "POST",
			}),
			invalidatesTags: ["FolderData"],
		}),
		getImageTagsList: builder.mutation({
			query: (body) => ({
				url: "/api/image-tag/get-list",
				method: "POST",
				body: body,
			}),
		}),
		createImageTag: builder.mutation({
			query: (data) => ({
				url: "/api/image-tag/create",
				method: "POST",
				body: data,
			}),
		}),
		addMediaTag: builder.mutation({
			query: (data) => ({
				url: "/api/image-tag/add-media-tag",
				method: "POST",
				body: data,
			}),
			invalidatesTags: ["MediaData"],
		}),
		deleteMediaTag: builder.mutation({
			query: (data) => ({
				url: "/api/image-tag/delete-media-tag",
				method: "POST",
				body: data,
			}),
			invalidatesTags: ["MediaData"],
		}),
		deleteMedia: builder.mutation({
			query: (mediaId) => ({
				url: `/api/media-library/delete/${mediaId}`,
				method: "POST",
			}),
			invalidatesTags: ["MediaData"],
		}),
		updateMedia: builder.mutation({
			query: ({ mediaId, ...updateData }) => ({
				url: `/api/media-library/update/${mediaId}`,
				method: "PATCH",
				body: updateData,
			}),
			invalidatesTags: ["MediaData"],
		}),
		createMedia: builder.mutation({
			query: (mediaData) => ({
				url: apiEndpoints.postMediaLibraryCreate,
				method: "POST",
				body: mediaData,
			}),
			invalidatesTags: ["MediaData"],
		}),
	}),
});

// Export hooks for usage in functional components, which are
// auto-generated based on the defined endpoints
export const {
	useGetMediaMutation,
	useUploadMediaMutation,
	useGetFolderListMutation,
	useGetMediaLibraryListMutation,
	useUpdateMediaTagsMutation,
	useCreateFolderMutation,
	useDeleteFolderMutation,
	useDeleteMediaMutation,
	useUpdateMediaMutation,
	useCreateMediaMutation,
	useGetImageTagsListMutation,
	useCreateImageTagMutation,
	useAddMediaTagMutation,
	useDeleteMediaTagMutation,
} = mediaLibraryApi;
