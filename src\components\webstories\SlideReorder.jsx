import React from "react";
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
} from "@dnd-kit/core";
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  horizontalListSortingStrategy,
} from "@dnd-kit/sortable";
import { GrDrag } from "react-icons/gr";
import { SortableSlide } from "./SortableSlide";
import { restrictToHorizontalAxis } from "@dnd-kit/modifiers";

const SlideReorder = ({ slides, onReorder }) => {
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  const handleDragEnd = (event) => {
    const { active, over } = event;

    if (active.id !== over.id) {
      const oldIndex = slides.findIndex(
        (slide) => slide._id.toString() === active.id
      );
      const newIndex = slides.findIndex(
        (slide) => slide._id.toString() === over.id
      );

      onReorder(arrayMove(slides, oldIndex, newIndex));
    }
  };

  return (
    <div className="p-4 mt-4 rounded-md">
      <h3 className="text-lg font-semibold mb-4">Reorder Slides</h3>

      <DndContext
        sensors={sensors}
        collisionDetection={closestCenter}
        onDragEnd={handleDragEnd}
        modifiers={[restrictToHorizontalAxis]}
      >
        <SortableContext
          items={slides.map((slide) => slide._id.toString())}
          strategy={horizontalListSortingStrategy}
        >
          <div className="flex gap-4 overflow-x-auto pb-4">
            {slides.map((slide, index) => (
              <SortableSlide
                key={slide._id}
                id={slide._id.toString()}
                index={index}
                title={slide.title}
              />
            ))}
          </div>
        </SortableContext>
      </DndContext>
    </div>
  );
};

export default SlideReorder;
