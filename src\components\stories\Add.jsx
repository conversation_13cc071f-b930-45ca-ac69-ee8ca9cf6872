import React from "react";
import { CiImageOn, CiVideoOn, CiViewTable, CiGrid41 } from "react-icons/ci";
import { GiAstrolabe } from "react-icons/gi";
import Button from "../../parts/Button";
import { PiUploadSimpleLight, PiImagesSquareLight } from "react-icons/pi";
import { SlFrame } from "react-icons/sl";
import { GoCircle } from "react-icons/go";
import { IoCodeOutline } from "react-icons/io5";
import { useDispatch, useSelector } from "react-redux";
import {
	setShowEmbed,
	setShowGallery,
	setShowRelatedPosts,
	setShowSideStoryTab,
} from "../../store/slices/storiesSlice";
import { openMediaLibrary, setFolderPath, setMultiple } from "../../store/slices/mediaLibrarySlice";
import { folderPath } from "../../utils/constants";
import { setMediaLibraryCallback } from "../../utils/MediaLibraryManager";
import { FaRegChartBar } from "react-icons/fa";

const Divider = () => {
	return (
		<>
			<svg xmlns="http://www.w3.org/2000/svg" width={36} height={36} viewBox="0 0 36 36">
				<g fill="none" fillRule="evenodd">
					<g fill="#162D3D">
						<g>
							<g>
								<g>
									<path
										d="M30 25.5V27H4.5v-1.5H30zm-12.75-9c1.243 0 2.25 1.007 2.25 2.25S18.493 21 17.25 21 15 19.993 15 18.75s1.007-2.25 2.25-2.25zM12 18v1.5H4.5V18H12zm18 0v1.5h-7.5V18H30zm-12.75 0c-.414 0-.75.336-.75.75s.336.***********-.336.75-.75-.336-.75-.75-.75zM30 10.5V12H4.5v-1.5H30z"
										transform="translate(-823 -408) translate(536 204) translate(256 186) translate(31 18)"
									/>
								</g>
							</g>
						</g>
					</g>
				</g>
			</svg>
		</>
	);
};

const ExpandableList = () => {
	return (
		<svg width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path
				fill-rule="evenodd"
				clip-rule="evenodd"
				d="M31.75 25V26.5H11.75V25H31.75ZM31.75 17.5V19H11.75V17.5H31.75ZM31.75 10V11.5H11.75V10H31.75Z"
				fill="#162D3D"
			></path>
			<path
				fill-rule="evenodd"
				clip-rule="evenodd"
				d="M0.321677 9.95649C0.226121 9.85904 0.226121 9.70165 0.321677 9.6042L0.845394 9.07328C0.891622 9.02636 0.95435 9 1.01976 9C1.08517 9 1.1479 9.02636 1.19413 9.07328L4.94148 12.8722L8.68759 9.07453C8.73382 9.02761 8.79655 9.00125 8.86196 9.00125C8.92737 9.00125 8.9901 9.02761 9.03633 9.07453L9.56004 9.6042C9.60632 9.65107 9.63232 9.71466 9.63232 9.78097C9.63232 9.84728 9.60632 9.91088 9.56004 9.95774L5.11646 14.4625C5.07023 14.5094 5.0075 14.5358 4.94209 14.5358C4.87668 14.5358 4.81395 14.5094 4.76772 14.4625L0.322909 9.95649H0.321677Z"
				fill="#162D3D"
			></path>
		</svg>
	);
};

// buttons for elements. can use the value for identifying the button and use in the editor

const Add = ({ editor }) => {
	const { showSideStoryTab } = useSelector((state) => state.stories);
	const dispatch = useDispatch();

	const mediaButtons = [
		{
			name: "Image",
			icon: <CiImageOn className="text-3xl" />,
			value: "Image",
			onClickHandler: () => {
				setMediaLibraryCallback((file) => {
					console.log(file, "file");
					editor
						.chain()
						.setImageBlock({
							src: file?.[0]?.imageUrl || "",
							alt: file?.[0]?.altName || "",
							caption: file?.[0]?.caption || "",
							courtesy: file?.[0]?.courtesy || "",
						})
						.setImageBlockCourtesy(file?.[0]?.courtesy || "")
						.setImageBlockAlt(file?.[0]?.altName || "")
						.setImageBlockCaption(file?.[0]?.caption || "")
						.focus()
						.run();
				});
				dispatch(openMediaLibrary());
				dispatch(setFolderPath(folderPath.imageBlock));
				dispatch(setShowSideStoryTab(false));
			},
		},

		{
			name: "Gallery",
			icon: <CiGrid41 className="text-3xl" />,
			value: "Gallery",
			onClickHandler: () => {
				setMediaLibraryCallback((file) => {
					const galleryBlockData = file.map((item) => ({
						src: item?.imageUrl || "",
						alt: item?.altName || "",
						caption: item?.caption || "",
						courtesy: item?.courtesy || "",
						link: {
							url: "",
							target: "_blank",
							noreferrer: "noreferrer",
							nofollow: "",
							sponsored: "",
						},
					}));
					editor.chain().setGalleryBlock({ data: galleryBlockData }).focus().run();
				});
				dispatch(openMediaLibrary());
				dispatch(setFolderPath(folderPath.galleryBlock));
				dispatch(setMultiple(true));
				dispatch(setShowSideStoryTab(false));
			},
		},
		{
			name: "Video",
			icon: <CiVideoOn className="text-3xl" />,
			value: "Video",
			onClickHandler: () => {
				editor.chain().focus().setVideoUpload().run();
				dispatch(setShowSideStoryTab(false));
			},
		},
		{
			name: "Gif",
			icon: <CiVideoOn className="text-3xl" />,
			value: "Gif",
		},
		{
			name: "File",
			icon: <PiUploadSimpleLight className="text-3xl" />,
			value: "File",
		},
		{
			name: "Embed",
			icon: <SlFrame className="text-3xl" />,
			value: "Embed",
			onClickHandler: () => {
				dispatch(setShowEmbed(true));
				dispatch(setShowSideStoryTab(false));
			},
		},
		{
			name: "Related Posts",
			icon: <PiImagesSquareLight className="text-3xl" />,
			value: "Related Posts",
			onClickHandler: () => {
				dispatch(setShowRelatedPosts(true));
				dispatch(setShowSideStoryTab(false));
			},
		},
	];

	// buttons for elements. can use the value for identifying the button and use in the editor
	const elementsButtons = [
		{
			name: "Divider",
			icon: <Divider className="text-3xl" />,
			value: "Divider",
			onClickHandler: () => {
				editor.chain().focus().setHorizontalRule().run();
				//   dispatch(setShowSideStoryTab(false));
			},
		},

		{
			name: "Button",
			icon: <GoCircle className="text-3xl" />,
			value: "Button",
		},
		{
			name: "Table",
			icon: <CiViewTable className="text-3xl" />,
			value: "Table",
		},
		{
			name: "Expandable List",
			icon: <ExpandableList className="text-3xl" />,
			value: "Expandable List",
		},
		{
			name: "Poll",
			icon: <FaRegChartBar className="text-3xl" />,
			value: "Poll",
		},
	];

	const astroButtons = [
		{
			name: "Astro Widget",
			icon: <GiAstrolabe className="text-3xl" />,
			value: "Astro Widget",
			onClickHandler: () => {
				editor.chain().focus().setAstroBlock().run();
				dispatch(setShowSideStoryTab(false));
			},
		},
	];

	return (
		<div className="px-2 py-5 flex flex-col gap-y-6">
			<div className="mx-auto">
				<h3>Media</h3>
				<div className="mt-2 flex flex-wrap items-center gap-3 w-full">
					{mediaButtons.map((button, index) => (
						<Button
							key={index}
							onClick={button.onClickHandler}
							variant="outline"
							customClasses="h-24 w-24 flex flex-col items-center justify-center border rounded text-gray-800 border-gray-500 transition-all duration-200"
						>
							{button.icon}
							<div className="text-xs mt-2">{button.name}</div>
						</Button>
					))}
				</div>
			</div>
			<div>
				<h3>Elements</h3>
				<div className="mt-2 flex flex-wrap items-center gap-3">
					{elementsButtons.map((button, index) => (
						<Button
							key={index}
							onClick={button.onClickHandler}
							variant="outline"
							customClasses="h-24 w-24 flex flex-col items-center justify-center border rounded text-gray-800 border-gray-500  transition-all duration-200"
						>
							{button.icon}
							<div className="text-xs mt-3">{button.name}</div>
						</Button>
					))}
				</div>
			</div>
			<div>
				<h3>Astro Element</h3>
				<div className="mt-2 flex flex-wrap items-center gap-3">
					{astroButtons.map((button, index) => (
						<Button
							key={index}
							onClick={button.onClickHandler}
							variant="outline"
							customClasses="h-24 w-24 flex flex-col items-center justify-center border rounded text-gray-800 border-gray-500 hover:text-primary hover:border-primary transition-all duration-200"
						>
							{button.icon}
							<div className="text-xs mt-3">{button.name}</div>
						</Button>
					))}
				</div>
			</div>
			<div>
				<h3>From the Web</h3>
				<Button
					variant="outline"
					customClasses="h-24 w-24 flex flex-col items-center justify-center border rounded text-gray-800 border-gray-500 hover:text-primary hover:border-primary transition-all duration-200"
				>
					<IoCodeOutline className="text-3xl" />
					<div className="text-xs mt-3">HTML code</div>
				</Button>
			</div>
		</div>
	);
};

export default Add;
