import React, { useEffect, useMemo } from "react";
import { useDispatch, useSelector } from "react-redux";
import { IoLockClosedOutline } from "react-icons/io5";
import { Link, useNavigate } from "react-router-dom";
import Table from "../parts/Table";
import Container from "../parts/Container";
import { BiChevronRight } from "react-icons/bi";
import { toast } from "react-toastify";
import BreadCrumb from "../parts/BreadCrumb";
import {
  incrementOffset,
  resetFilters,
  resetOffset,
  setFetchedData,
  setInitialFlaggedStory,
} from "../store/slices/flaggedStoriesSlice";
import { apiEndpoints } from "../utils/constants";
import useInfiniteScrollData from "../utils/useInfiniteScrollData";
import Loader from "../parts/Loader";

const frontendUrl = import.meta.env.VITE_CLIENT_URL;
const FlaggedStories = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  // config for the stories api
  const storiesApiConfig = {
    setDataAction: setFetchedData,
    resetDataAction: resetFilters,
    sliceName: "flaggedStory",
  };

  //resetting the filters on the intital load
  useEffect(() => {
    dispatch(resetFilters());
    dispatch(resetOffset());
  }, []);

  const {
    data,
    isLoading,
    offset,
    filter,
    isError,
    error,
    fetchData,
    handleSearch,
    isFetching,
  } = useInfiniteScrollData({
    config: storiesApiConfig,
    url: apiEndpoints.postFlaggedStoriesList,
  });

  // handling the error here
  if (isError) {
    if (error.status === 401) {
      toast.error("Session Expired", { toastId: "text_story_fetch_error" });
      navigate("/signin");
    }
  }

  // load the data on intial page load and also when the offset changes
  useEffect(() => {
    fetchData(true);
  }, [offset, filter.status, filter.search]);

  // increase the offset when user has scrolled till the last row of the table which in turn fethes the data
  const fetchMoreData = () => {
    dispatch(incrementOffset());
  };

  const columns = [
    {
      accessorKey: "_id",
      enableSorting: false,
      header: () => <IoLockClosedOutline className="texgt-primary text-xl" />,
      cell: ({ row }) =>
        row.original.isLocked ? (
          <IoLockClosedOutline className="text-primary text-xl" />
        ) : null,
    },
    {
      accessorKey: "name",
      id: "name",
      width: 200,
      header: () => "Flag Name",
      cell: ({ row }) => {
        return (
          <div className="flex items-center gap-x-2 font-semibold ">
            <div>
              <div className="line-clamp-2">{row.original?.name}</div>
              <div className="text-[#969696] text-xs font-normal flex items-center space-x-2">
                URL slug: {row.original?.slug}
              </div>
            </div>
          </div>
        );
      },
    },
    // {
    //   accessorKey: "submenus",
    //   header: () => "Sub FlaggedStories",
    // },
    {
      accessorKey: "section",
      header: () => "Section",
      cell: ({ row }) => (
        <div className="flex flex-col gap-y-1">
          {row.original?.section?.map((menu) => {
            return (
              <button
                onClick={() => {
                  const subSectionIds =
                    menu.name === "All"
                      ? row.original.submenus.map((item) => item._id)
                      : [menu._id];

                  navigate(
                    `/admin/flagged-sub-stories/${menu.name
                      .toLowerCase()
                      .replace(" ", "-")}?ids=${encodeURIComponent(
                      JSON.stringify(subSectionIds)
                    )}&name=${encodeURIComponent(menu.name)}&level=${
                      menu.type
                    }&type=${menu.storiesType}`
                  );
                }}
                key={menu._id}
                // to={`/admin/subcategory/${menu._id}`}
                className="hover:text-fadeGray flex items-center gap-x-2 group"
              >
                <span> {menu.name}</span>

                <BiChevronRight className="opacity-0 group-hover:opacity-100 group-hover:text-fadeGray text-xl transition-all duration-150" />
              </button>
            );
          })}
        </div>
      ),
    },
    {
      accessorKey: "stories",
      header: "Stories",
      cell: ({ row }) => (
        <div className="flex flex-col gap-y-1">
          {row.original?.section?.map((menu) => {
            return <div key={menu._id}>{menu.postCount} Stories</div>;
          })}
        </div>
      ),
    },
    // {
    //   accessorKey: "timestamp",
    //   header: "Action",
    //   cell: ({ row }) => {
    //     return (
    //       <div className="flex items-center gap-x-2">
    //         <RoundedIconsButton
    //         // onClick={() => handleViewClick(row.original.viewLink)}
    //         >
    //           <BsEye className="h-[15px] w-[15px]" />
    //         </RoundedIconsButton>

    //         <RoundedIconsButton>
    //           <FiEdit
    //             className="h-[15px] w-[15px]"
    //             // onClick={() =>
    //             //   navigate(`/admin/edit-video-story/${row.original._id}`)
    //             // }
    //           />
    //         </RoundedIconsButton>
    //         <RoundedIconsButton>
    //           <FaRegTrashAlt className="h-[15px] w-[15px]" />
    //         </RoundedIconsButton>
    //       </div>
    //     );
    //   },
    // },
  ];

  return (
    <Container>
      <div className="lg:flex items-center justify-between mb-5">
        <div>
          <div className="flex items-center text-sm">
            <Link
              onClick={() => dispatch(resetFilters())}
              to={"/admin/flagged-stories"}
              className="hover:bg-white text-blackShade rounded-full px-3 py-1 bg-transparent "
            >
              Flagged Stories
            </Link>
            {/* <BiChevronRight className="text-xl" />
            <p className="text-fadeGray pl-2 capitalize">
              {searchParams.get("status")}
            </p> */}
          </div>
          <BreadCrumb
            title={"Flagged Stories"}
            description={
              "Flag and prioritize stories for your page, reorder and customize their visibility."
            }
          />
        </div>
      </div>
      <Table
        module="flaggedStory"
        data={data}
        isLoading={isLoading}
        columns={columns}
        isFetching={isFetching}
        fetchMoreData={fetchMoreData}
        customClass={"flaggedStories"}
        enableRowSelection={false}
        enableMultiRowSelection={false}
      />
    </Container>
  );
};

export default FlaggedStories;
