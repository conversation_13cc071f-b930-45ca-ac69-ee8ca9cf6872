.tiptap-editor-container {
  position: relative;
  display: block;
  width: 100%;
  height: 100%;
}
.editor-content {
  position: relative;
}

.editor-block {
  position: relative;
  margin-bottom: 10px;
}

.inlinetool-bar {
  display: none;
  position: absolute;
  top: 0;
  right: 0;
  z-index: 99;
}

.bubble-menu {
  display: flex;
  align-items: center;
  height: 40px;
  position: relative;
  background-color: #fff;
  border: 1px solid rgba(51, 51, 51, 0.1);
  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.07);
  border-radius: 2px;
  padding: 0 4px;
  box-sizing: border-box;
  font-size: 14px;
  font-weight: 500;
}

.link-preview {
  display: flex;
  white-space: nowrap;
  width: 100%;
}

.tiptap blockquote {
  display: block;
  padding-left: 1rem;
  border-left: 3px solid rgba(61, 37, 20, 0.12);
}

.tiptap ul {
  list-style: disc !important;
}

.tiptap ul li {
  list-style: disc !important;
}

.editor-block:hover .inlinetool-bar,
.editor-block:focus-within .inlinetool-bar {
  display: block;
}

.btn-add-inlinetool {
  pointer-events: auto;
}
.inlinetool-bar {
  display: flex;
  align-items: center;
  justify-content: center;
}
.btn-add-inlinetool {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 21px;
  height: 21px;
  border: none;
  background-color: var(--primary-light-color);
  border-radius: 4px;
  color: var(--primary-color);
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 3;
}
.btn-add-inlinetool svg {
  width: 14px;
  height: 14px;
}

.btn-add-inlinetool:hover {
  background-color: var(--primary-color);
  color: #fff;
}

.node-astroBlock {
  max-width: 100vw !important;
}

.astro-widget-container {
  display: flex;
  align-items: flex-end;
  gap: 20px;
  background-color: #faf5ed;
}
.astro-widget-uploader {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 440px;
  aspect-ratio: 1/1.2;
  border-radius: 0.25rem;
  border-width: 2px;
  border-style: dotted;
  border-color: rgb(0 0 0 / var(--tw-border-opacity));
  --tw-border-opacity: 0.1;
  padding: 0.5rem;
  transition: border 0.16s cubic-bezier(0.45, 0.05, 0.55, 0.95);
}
.astro-widget-image {
  position: relative;
  width: 440px;
  aspect-ratio: 1/1.3;
  overflow: hidden;
}
.astro-widget-image img {
  position: absolute;
  inset: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.astro-widget-text {
  width: fit-content;
  height: fit-content;
  flex: 1;
  font-family: "Arial", sans-serif;
  color: #000;
  padding: 16px;
}

.astro-widget-text p {
  margin-top: 8px;
  font-size: 1rem !important;
  line-height: 1.6 !important;
}

.ProseMirror .is-empty:before {
  --tw-text-opacity: 1;
  color: rgb(115 115 115 / var(--tw-text-opacity));
}

.ProseMirror [data-type="column"] > :first-child,
.ProseMirror > :first-child:first-child {
  margin-top: 0;
}

.ProseMirror [data-type="column"] > :last-child,
.ProseMirror > :first-child:last-child {
  margin-bottom: 0;
}

.ProseMirror > * + * {
  margin-top: 0.75em;
}

.ProseMirror .node-imageUpload,
.ProseMirror .node-videoUpload {
  border-radius: 0.25rem;
  border-width: 2px;
  border-style: dotted;
  border-color: rgb(0 0 0 / var(--tw-border-opacity));
  --tw-border-opacity: 0.1;
  padding: 0.5rem;
}

.ProseMirror .node-imageUpload,
.ProseMirror .node-videoUpload {
  transition: border 0.16s cubic-bezier(0.45, 0.05, 0.55, 0.95);
}

.ProseMirror .node-imageUpload:hover,
.ProseMirror .node-videoUpload:hover {
  --tw-border-opacity: 0.3;
}

.ProseMirror .node-imageUpload.has-focus,
.ProseMirror .node-imageUpload:has(.is-active),
.ProseMirror .node-videoUpload.has-focus,
.ProseMirror .node-videoUpload:has(.is-active) {
  --tw-border-opacity: 0.4;
}

.ProseMirror [data-type="columns"].has-focus [data-type="column"],
.ProseMirror [data-type="columns"]:hover [data-type="column"] {
  --tw-border-opacity: 1;
  border-color: rgb(212 212 212 / var(--tw-border-opacity));
}

.ProseMirror [data-type="columns"] [data-type="column"].has-focus {
  --tw-border-opacity: 1;
  border-color: rgb(163 163 163 / var(--tw-border-opacity));
}
.ProseMirror .pos-relv {
  position: relative;
}
.ProseMirror .pos-full-wh {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.ProseMirror [data-type="column"] {
  border-radius: 0.25rem;
  border: 2px dotted transparent;
  padding: 0.25rem;
  transition: border 0.16s cubic-bezier(0.45, 0.05, 0.55, 0.95);
}

.ProseMirror [data-type="column"]:hover {
  --tw-border-opacity: 1;
  border-color: rgb(245 245 245 / var(--tw-border-opacity));
}

.ProseMirror [data-type="column"].has-focus,
.ProseMirror [data-type="column"]:has(.is-active) {
  --tw-border-opacity: 1;
  border-color: rgb(245 245 245 / var(--tw-border-opacity));
}

.ProseMirror .node-imageBlock img,
.ProseMirror .node-videoBlock video {
  overflow: hidden;
  border-color: transparent;
}

/* .ProseMirror .node-imageBlock:hover img,
.ProseMirror .node-videoBlock:hover video {
  border-width: 2px;
  --tw-border-opacity: 1;
  border-color: rgb(245 245 245 / var(--tw-border-opacity));
} */

/* .ProseMirror .node-imageBlock.has-focus img,
.ProseMirror .node-imageBlock:has(.is-active) img,
.ProseMirror .node-videoBlock.has-focus video,
.ProseMirror .node-videoBlock:has(.is-active) video {
  border-width: 2px;
  --tw-border-opacity: 1;
  border-color: rgb(38 38 38 / var(--tw-border-opacity));
} */

.ProseMirror .ProseMirror-gapcursor + .node-imageBlock,
.ProseMirror .ProseMirror-gapcursor + .node-imageUpload,
.ProseMirror .ProseMirror-gapcursor + .node-videoBlock,
.ProseMirror .ProseMirror-gapcursor + .node-videoUpload,
.ProseMirror .ProseMirror-gapcursor + [data-type="blockquoteFigure"] {
  outline-color: #404040;
}

.ProseMirror .ProseMirror-gapcursor + .node-imageBlock:hover,
.ProseMirror .ProseMirror-gapcursor + .node-imageUpload:hover,
.ProseMirror .ProseMirror-gapcursor + .node-videoBlock:hover,
.ProseMirror .ProseMirror-gapcursor + .node-videoUpload:hover,
.ProseMirror .ProseMirror-gapcursor + [data-type="blockquoteFigure"]:hover {
  outline-color: #525252;
}

.ProseMirror [data-type="blockquoteFigure"] > div {
  border-radius: 0.5rem;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-left-width: 4px;
  --tw-border-opacity: 1;
  border-left-color: rgb(64 64 64 / var(--tw-border-opacity));
  --tw-bg-opacity: 0.8;
  padding: 0.5rem 1rem;
}

.ProseMirror [data-type="blockquoteFigure"] > blockquote {
  font-size: 1.125rem;
  line-height: 1.75rem;
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity));
}
.ProseMirror [data-type="blockquoteFigure"] figcaption {
  margin-top: 1rem;
  overflow: hidden;
  font-size: 0.875rem;
  line-height: 1.25rem;
  --tw-text-opacity: 1;
  color: rgb(115 115 115 / var(--tw-text-opacity));
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 0.15s;
}

/* PorseMirorr Css */

.spinner-animation {
  animation: spinner 1.6s linear infinite;
  animation-delay: -1.6s;
}

.spinner-animation-secondary {
  animation-delay: -1s;
}

@keyframes spinner {
  12.5% {
    x: 13px;
    y: 1px;
  }

  25% {
    x: 13px;
    y: 1px;
  }

  37.5% {
    x: 13px;
    y: 13px;
  }

  50% {
    x: 13px;
    y: 13px;
  }

  62.5% {
    x: 1px;
    y: 13px;
  }

  75% {
    x: 1px;
    y: 13px;
  }

  87.5% {
    x: 1px;
    y: 1px;
  }
}

.ProseMirror figure[data-type="imageBlock"],
.ProseMirror figure[data-type="videoBlock"] {
  margin: 0;
}

.ProseMirror figure[data-type="imageBlock"] img,
.ProseMirror figure[data-type="videoBlock"] video {
  display: block;
  width: 100%;
  border-radius: 0.25rem;
}

.ProseMirror figure[data-type="blockquoteFigure"] {
  margin-top: 3.5rem;
  margin-bottom: 3.5rem;
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity));
}

.ProseMirror [data-type="blockquoteFigure"] blockquote,
.ProseMirror > blockquote blockquote {
  margin: 0;
}

.ProseMirror [data-type="blockquoteFigure"] blockquote > :first-child,
.ProseMirror > blockquote blockquote > :first-child {
  margin-top: 0;
}

.ProseMirror [data-type="blockquoteFigure"] blockquote > :last-child,
.ProseMirror > blockquote blockquote > :last-child {
  margin-bottom: 0;
}

.ProseMirror [data-type="columns"] {
  margin-top: 3.5rem;
  margin-bottom: 3rem;
  display: grid;
  gap: 1rem;
}

.ProseMirror [data-type="columns"].layout-sidebar-left {
  grid-template-columns: 40fr 60fr;
}

.ProseMirror [data-type="columns"].layout-sidebar-right {
  grid-template-columns: 60fr 40fr;
}

.ProseMirror [data-type="columns"].layout-two-column {
  grid-template-columns: 1fr 1fr;
}

.ProseMirror [data-type="column"] {
  overflow: auto;
}

.ProseMirror code {
  border-radius: 0.125rem;
  --tw-bg-opacity: 1;
  background-color: rgb(23 23 23 / var(--tw-bg-opacity));
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas,
    Liberation Mono, Courier New, monospace;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
  caret-color: #fff;
  --tw-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -4px rgba(0, 0, 0, 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color),
    0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),
    var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.ProseMirror code::-moz-selection {
  background-color: hsla(0, 0%, 100%, 0.3);
}

.ProseMirror code::selection {
  background-color: hsla(0, 0%, 100%, 0.3);
}

.ProseMirror pre {
  margin-top: 3rem;
  margin-bottom: 3rem;
  border-radius: 0.25rem;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(0 0 0 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(64 64 64 / var(--tw-bg-opacity));
  padding: 1rem;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
  caret-color: #fff;
}

.ProseMirror pre ::-moz-selection {
  background-color: hsla(0, 0%, 100%, 0.2);
}

.ProseMirror pre ::selection {
  background-color: hsla(0, 0%, 100%, 0.2);
}

.ProseMirror pre code {
  background-color: inherit;
  padding: 0;
  color: inherit;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),
    var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.ProseMirror pre .hljs-comment,
.ProseMirror pre .hljs-quote {
  --tw-text-opacity: 1;
  color: rgb(163 163 163 / var(--tw-text-opacity));
}

.ProseMirror pre .hljs-attribute,
.ProseMirror pre .hljs-link,
.ProseMirror pre .hljs-name,
.ProseMirror pre .hljs-regexp,
.ProseMirror pre .hljs-selector-class,
.ProseMirror pre .hljs-selector-id,
.ProseMirror pre .hljs-tag,
.ProseMirror pre .hljs-template-variable,
.ProseMirror pre .hljs-variable {
  --tw-text-opacity: 1;
  color: rgb(252 165 165 / var(--tw-text-opacity));
}

.ProseMirror pre .hljs-built_in,
.ProseMirror pre .hljs-builtin-name,
.ProseMirror pre .hljs-literal,
.ProseMirror pre .hljs-meta,
.ProseMirror pre .hljs-number,
.ProseMirror pre .hljs-params,
.ProseMirror pre .hljs-type {
  --tw-text-opacity: 1;
  color: rgb(253 186 116 / var(--tw-text-opacity));
}

.ProseMirror pre .hljs-bullet,
.ProseMirror pre .hljs-string,
.ProseMirror pre .hljs-symbol {
  --tw-text-opacity: 1;
  color: rgb(190 242 100 / var(--tw-text-opacity));
}

.ProseMirror pre .hljs-section,
.ProseMirror pre .hljs-title {
  --tw-text-opacity: 1;
  color: rgb(253 224 71 / var(--tw-text-opacity));
}

.ProseMirror pre .hljs-keyword,
.ProseMirror pre .hljs-selector-tag {
  --tw-text-opacity: 1;
  color: rgb(94 234 212 / var(--tw-text-opacity));
}

.ProseMirror pre .hljs-emphasis {
  font-style: italic;
}

.ProseMirror pre .hljs-strong {
  font-weight: 700;
}

.ProseMirror .collaboration-cursor__caret {
  pointer-events: none;
  position: relative;
  margin-left: -1px;
  margin-right: -1px;
  overflow-wrap: normal;
  word-break: normal;
  border-right-width: 1px;
  border-left-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(0 0 0 / var(--tw-border-opacity));
}

.ProseMirror .collaboration-cursor__label {
  position: absolute;
  left: -1px;
  top: -1.4em;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  white-space: nowrap;
  border-radius: 0.25rem;
  border-top-left-radius: 0;
  padding: 0.125rem 0.375rem;
  font-size: 0.75rem;
  line-height: 1rem;
  font-weight: 600;
  line-height: 1;
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity));
}

.ProseMirror ol {
  list-style-type: decimal;
}

.ProseMirror ul {
  list-style-type: disc;
}

.ProseMirror ol,
.ProseMirror ul {
  margin-top: 1.5rem;
  margin-bottom: 1.5rem;
  padding: 0 2rem;
}

.ProseMirror ol:first-child,
.ProseMirror ul:first-child {
  margin-top: 0;
}

.ProseMirror ol:last-child,
.ProseMirror ul:last-child {
  margin-bottom: 0;
}

.ProseMirror ol li,
.ProseMirror ol ol,
.ProseMirror ol ul,
.ProseMirror ul li,
.ProseMirror ul ol,
.ProseMirror ul ul {
  margin-top: 0.25rem;
  margin-bottom: 0.25rem;
}

.ProseMirror ol p,
.ProseMirror ul p {
  margin-top: 0;
  margin-bottom: 0.25rem;
}

.ProseMirror > ol,
.ProseMirror > ul {
  margin-top: 2rem;
  margin-bottom: 2rem;
}

.ProseMirror > ol:first-child,
.ProseMirror > ul:first-child {
  margin-top: 0;
}

.ProseMirror > ol:last-child,
.ProseMirror > ul:last-child {
  margin-bottom: 0;
}

.ProseMirror ul[data-type="taskList"] {
  list-style-type: none;
  padding: 0;
}

.ProseMirror ul[data-type="taskList"] p {
  margin: 0;
}

.ProseMirror ul[data-type="taskList"] li {
  display: flex;
}

.ProseMirror ul[data-type="taskList"] li > label {
  margin-top: 0.25rem;
  margin-right: 0.5rem;
  flex: 1 1 auto;
  flex-shrink: 0;
  flex-grow: 0;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}

.ProseMirror ul[data-type="taskList"] li > div {
  flex: 1 1 auto;
}

.ProseMirror ul[data-type="taskList"] li[data-checked="true"] {
  text-decoration-line: line-through;
}

.ProseMirror .is-empty:before {
  pointer-events: none;
  float: left;
  height: 0;
  width: 100%;
  color: rgba(0, 0, 0, 0.4);
}

.ProseMirror > .is-editor-empty:first-child::before {
  content: attr(data-placeholder);
}

.ProseMirror
  blockquote
  .is-empty:not(.is-editor-empty):first-child:last-child:before {
  content: "Enter a quote";
}

.ProseMirror blockquote + figcaption.is-empty:not(.is-editor-empty):before {
  content: "Author";
}

.ProseMirror [data-placeholder][data-suggestion] :before,
.ProseMirror [data-placeholder][data-suggestion]:before {
  content: none !important;
}

.ProseMirror .tableWrapper {
  margin-top: 3rem;
  margin-bottom: 3rem;
}

.ProseMirror table {
  box-sizing: border-box;
  width: 100%;
  border-collapse: collapse;
  border-radius: 0.25rem;
  border-color: rgba(0, 0, 0, 0.1);
}

.ProseMirror table td,
.ProseMirror table th {
  position: relative;
  min-width: 100px;
  border-width: 1px;
  border-color: rgba(0, 0, 0, 0.1);
  padding: 0.5rem;
  text-align: left;
  vertical-align: top;
}

.ProseMirror table td:first-of-type:not(a),
.ProseMirror table th:first-of-type:not(a) {
  margin-top: 0;
}

.ProseMirror table td p,
.ProseMirror table th p {
  margin: 0;
}

.ProseMirror table td p + p,
.ProseMirror table th p + p {
  margin-top: 0.75rem;
}

.ProseMirror table th {
  font-weight: 700;
}

.ProseMirror table .column-resize-handle {
  pointer-events: none;
  position: absolute;
  bottom: -2px;
  right: -0.25rem;
  top: 0;
  display: flex;
  width: 0.5rem;
}

.ProseMirror table .column-resize-handle:before {
  margin-left: 0.5rem;
  height: 100%;
  width: 1px;
  background-color: rgba(0, 0, 0, 0.2);
}

.ProseMirror table .column-resize-handle:before {
  content: "";
}

.ProseMirror table .selectedCell {
  border-style: double;
  border-color: rgba(0, 0, 0, 0.2);
  background-color: rgba(0, 0, 0, 0.05);
}

.ProseMirror table .grip-column,
.ProseMirror table .grip-row {
  position: absolute;
  z-index: 10;
  display: flex;
  cursor: pointer;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.05);
}

.ProseMirror table .grip-column {
  left: 0;
  top: -0.75rem;
  margin-left: -1px;
  height: 0.75rem;
  width: calc(100% + 1px);
  border-left-width: 1px;
  border-color: rgba(0, 0, 0, 0.2);
}

.ProseMirror table .grip-column.selected:before,
.ProseMirror table .grip-column:hover:before {
  content: "";
  width: 0.625rem;
}

.ProseMirror table .grip-column:hover {
  background-color: rgba(0, 0, 0, 0.1);
}

.ProseMirror table .grip-column:hover:before {
  border-bottom: 2px;
  border-color: rgba(0, 0, 0, 0.6);
  border-style: dotted;
}

.ProseMirror table .grip-column.first {
  border-top-left-radius: 0.125rem;
  border-color: transparent;
}

.ProseMirror table .grip-column.last {
  border-top-right-radius: 0.125rem;
}

.ProseMirror table .grip-column.selected {
  border-color: rgba(0, 0, 0, 0.3);
  background-color: rgba(0, 0, 0, 0.3);
  --tw-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),
    var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.ProseMirror table .grip-column.selected:before {
  border-bottom-width: 2px;
  border-style: dotted;
}

.ProseMirror table .grip-row {
  left: -0.75rem;
  top: 0;
  margin-top: -1px;
  height: calc(100% + 1px);
  width: 0.75rem;
  border-top-width: 1px;
  border-color: rgba(0, 0, 0, 0.2);
}

.ProseMirror table .grip-row.selected:before,
.ProseMirror table .grip-row:hover:before {
  height: 0.625rem;
  content: "";
}

.ProseMirror table .grip-row:hover {
  background-color: rgba(0, 0, 0, 0.1);
}

.ProseMirror table .grip-row:hover:before {
  border-left: 2px;
  border-color: rgba(0, 0, 0, 0.6);
  border-style: dotted;
}

.ProseMirror table .grip-row.first {
  border-top-left-radius: 0.125rem;
  border-color: transparent;
}

.ProseMirror table .grip-row.last {
  border-bottom-left-radius: 0.125rem;
}

.ProseMirror table .grip-row.selected {
  border-color: rgba(0, 0, 0, 0.3);
  background-color: rgba(0, 0, 0, 0.3);
  --tw-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),
    var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.ProseMirror table .grip-row.selected:before {
  border-left-width: 2px;
  border-style: dotted;
}

.ProseMirror p {
  margin-top: 0.75rem;
  margin-bottom: 0.75rem;
  line-height: 1.625;
}

.ProseMirror p:first-child {
  margin-top: 0;
}

.ProseMirror p:last-child {
  margin-bottom: 0;
}

.ProseMirror > p {
  margin-top: 0rem;
  margin-bottom: 0rem;
}

.ProseMirror > p:first-child {
  margin-top: 0;
}

.ProseMirror > p:last-child {
  margin-bottom: 0;
}

.ProseMirror h1 {
  font-size: 1.875rem;
  line-height: 2.25rem;
}

.ProseMirror h2 {
  font-size: 1.5rem;
  line-height: 2rem;
}

.ProseMirror h3 {
  font-size: 1.25rem;
  line-height: 1.75rem;
}

.ProseMirror h4 {
  font-size: 1.125rem;
  line-height: 1.75rem;
}

.ProseMirror h5 {
  font-size: 1rem;
  line-height: 1.5rem;
}

.ProseMirror h6 {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.ProseMirror h1,
.ProseMirror h2,
.ProseMirror h3,
.ProseMirror h4,
.ProseMirror h5,
.ProseMirror h6 {
  font-weight: 700;
}

.ProseMirror h1:first-child,
.ProseMirror h2:first-child,
.ProseMirror h3:first-child,
.ProseMirror h4:first-child,
.ProseMirror h5:first-child,
.ProseMirror h6:first-child {
  margin-top: 0;
}

.ProseMirror h1:last-child,
.ProseMirror h2:last-child,
.ProseMirror h3:last-child,
.ProseMirror h4:last-child,
.ProseMirror h5:last-child,
.ProseMirror h6:last-child {
  margin-bottom: 0;
}

.ProseMirror h1,
.ProseMirror h2,
.ProseMirror h3 {
  margin-top: 3rem;
}

.ProseMirror h4,
.ProseMirror h5,
.ProseMirror h6 {
  margin-top: 2rem;
}

.ProseMirror a.link {
  pointer-events: none;
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity));
}
.ProseMirror a.link > span {
  color: unset !important;
}

.ProseMirror mark {
  border-radius: 0.125rem;
  --tw-bg-opacity: 1;
  background-color: rgb(239 68 68 / var(--tw-bg-opacity));
  -webkit-box-decoration-break: clone;
  box-decoration-break: clone;
  padding: 0.25rem 0;
  color: inherit;
}

.ProseMirror .image-wrapper,
.ProseMirror .video-wrapper {
  position: relative;
  cursor: pointer;
  transition: all 0.3s ease-in-out;
}
.ProseMirror .has-focus .image-wrapper,
.ProseMirror .has-focus .video-wrapper {
  box-shadow: 0 0 0 2px rgb(52 117 222);
}
.ProseMirror .image-wrapper:hover,
.ProseMirror .video-wrapper:hover {
  box-shadow: 0 0 0 2px rgb(52 117 222);
}
.ProseMirror .embed-wrapper {
  position: relative;
  width: 100%; /* Takes 100% of the parent’s width */
  padding-top: 56.25%; /* This creates a responsive aspect ratio (16:9). You can adjust this */
  overflow: hidden;
  cursor: pointer;
}
.ProseMirror .embed-wrapper iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 0;
  pointer-events: none;
}
/* .ProseMirror .embed-wrapper::before {
  content: "";
  display: block;
  padding-top: 56.25%;
} */
.ProseMirror img,
.ProseMirror video {
  height: 100%;
  width: 100%;
  max-width: 100%;
  object-fit: cover;
}

/* .ProseMirror iframe {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
} */

.ProseMirror [data-type="horizontalRule"] {
  margin-top: 2rem;
  margin-bottom: 2rem;
  cursor: pointer;
  padding-top: 1rem;
  padding-bottom: 1rem;
  transition-property: all;
  transition-duration: 0.1s;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  animation-duration: 0.1s;
  animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

.ProseMirror [data-type="horizontalRule"].ProseMirror-selectednode {
  background-color: rgba(0, 0, 0, 0.05);
}

.ProseMirror [data-type="horizontalRule"].ProseMirror-selectednode hr {
  border-top-color: rgba(0, 0, 0, 0.3);
}

.ProseMirror
  [data-type="horizontalRule"]:hover:not(
    .ProseMirror [data-type="horizontalRule"].ProseMirror-selectednode
  ) {
  background-color: rgba(0, 0, 0, 0.05);
}

.ProseMirror [data-type="horizontalRule"] hr {
  border-width: 1px 0 0;
  border-color: rgba(0, 0, 0, 0.2);
  background-color: rgba(0, 0, 0, 0.8);
}

.ProseMirror {
  z-index: 0;
  padding: 0rem 2rem 4rem 5rem;
  caret-color: #000;
  outline-width: 0;
}

@media (min-width: 1024px) {
  .ProseMirror {
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

.ProseMirror > * {
  margin-left: auto;
  margin-right: auto;
  max-width: 50rem;
}

.ProseMirror .selection {
  display: inline;
}

.ProseMirror ::-moz-selection {
  background-color: rgba(0, 0, 0, 0.1);
}

.ProseMirror .selection,
.ProseMirror ::selection {
  background-color: rgba(0, 0, 0, 0.1);
}
.ProseMirror > .react-renderer {
  margin-top: 1rem;
  margin-bottom: 1rem;
}

.ProseMirror > .react-renderer:first-child {
  margin-top: 0;
}

.ProseMirror > .react-renderer:last-child {
  margin-bottom: 0;
}

.ProseMirror.resize-cursor {
  cursor: col-resize;
}

.tippy-box[data-theme~="tomato"] {
  background-color: #e2edff;
  color: #3475de;
}

/* Floating menu */
.floating-menu {
  display: flex;

  border-radius: 0.5rem;
}

.floating-menu button {
  background-color: unset;
  padding: 0.275rem 0.425rem;
  border-radius: 0.3rem;
  color: #3475de;
}

.floating-menu button:hover {
  color: #fff;
  background-color: #3475de;
}

.floating-menu button.is-active {
  color: #fff;
  background-color: #3475de;
}

.floating-menu button.is-active:hover {
  color: #fff;
  background-color: #3475de;
}

.tippy-arrow {
  display: none;
}
.tippy-box {
  background-color: transparent;
  margin-bottom: 5px;
  margin-top: -10px;
}
