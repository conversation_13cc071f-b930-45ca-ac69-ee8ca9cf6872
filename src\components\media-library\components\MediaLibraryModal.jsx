import React, { useEffect, useState, useRef } from "react";
import { toast } from "react-toastify";
import Outside<PERSON><PERSON><PERSON><PERSON><PERSON> from "react-outside-click-handler";
import ConfirmationModal from "../../../parts/ConfirmationModal";
import useConfirmationModal from "../../../utils/useConfirmationModal";
import { useMediaLibrary } from "../hooks/useMediaLibrary";
import MediaLibraryHeader from "./MediaLibraryHeader";
import MediaLibraryContent from "./MediaLibraryContent";
import CreateFolderModal from "./CreateFolderModal";
import Aside from "./Aside";
import styles from "../MediaLibrary.module.css";

const MediaLibraryModal = () => {
	const {
		// State
		selectedFolders,
		selectedImages,
		currentFolder,
		folderHierarchy,
		folders,
		media,
		mediaPagination,
		isMultiple,
		fileType,
		folderPath,

		// Loading states
		isLoadingFolders,
		isLoadingMedia,
		isCreatingFolder,
		isDeletingFolder,

		// API functions
		createFolder,
		deleteFolder,

		// Actions
		loadFolders,
		loadMediaList,
		handleLoadMore,
		handleBreadcrumbClick,
		handleFolderEnter,
		handleShowFolderDetails,
		handleImageSelect,
		getBreadcrumbPath,
		isFolderSelected,
		isImageSelected,
		closeModal,

		// Dispatch for direct access
		dispatch,
		setSelectedFolders,
	} = useMediaLibrary();

	// Local state for modals
	const [showCreateFolder, setShowCreateFolder] = useState(false);
	const [newFolderName, setNewFolderName] = useState("");
	const [folderToDelete, setFolderToDelete] = useState(null);

	// Confirmation modal hook
	const { isModalOpen, openModal, closeModal: closeConfirmModal } = useConfirmationModal();

	// Track if data has been initialized to prevent infinite loops
	const isInitialized = useRef(false);

	// Load initial data only once
	useEffect(() => {
		if (!isInitialized.current) {
			isInitialized.current = true;
			loadFolders();
			loadMediaList(true);
		}
	}, []); // Empty dependency array - only run on mount

	// Folder creation handlers
	const handleAddFolderClick = () => {
		setShowCreateFolder(true);
	};

	const handleCreateFolder = async () => {
		if (!newFolderName.trim()) {
			toast.error("Please enter a folder name");
			return;
		}

		try {
			// Determine the current level and parent based on folder hierarchy
			const level = folderHierarchy.length;
			const parent = currentFolder || null;

			// Create the path based on hierarchy
			let path = "/" + newFolderName.trim().toLowerCase().replace(/\s+/g, "");
			if (folderHierarchy.length > 0) {
				// If we're inside folders, the path should be relative to current location
				const currentPath = folderHierarchy
					.map((f) => f.name.toLowerCase().replace(/\s+/g, ""))
					.join("/");
				path = "/" + currentPath + "/" + newFolderName.trim().toLowerCase().replace(/\s+/g, "");
			}

			const folderData = {
				name: newFolderName.trim(),
				path: path,
				level: level,
				parent: parent,
			};

			await createFolder(folderData).unwrap();

			// Reset form and close modal
			setNewFolderName("");
			setShowCreateFolder(false);

			// Reload folders to show the new folder
			await loadFolders();

			toast.success("Folder created successfully");
		} catch (error) {
			console.error("Failed to create folder:", error);
			toast.error("Failed to create folder");
		}
	};

	const handleCancelCreateFolder = () => {
		setNewFolderName("");
		setShowCreateFolder(false);
	};

	// Folder deletion handlers
	const handleDeleteFolder = (folderId, folderName) => {
		// Store folder info for deletion
		setFolderToDelete({ id: folderId, name: folderName });
		// Open confirmation modal
		openModal();
	};

	const confirmDeleteFolder = async () => {
		if (!folderToDelete) return;

		try {
			await deleteFolder(folderToDelete.id).unwrap();

			// Show success toast
			toast.success(`Folder "${folderToDelete.name}" deleted successfully`);

			// Reload folders to reflect the deletion
			await loadFolders();

			// Clear selection if the deleted folder was selected
			const isSelectedFolder = selectedFolders.some(
				(folder) => (folder._id || folder.id) === folderToDelete.id
			);
			if (isSelectedFolder) {
				dispatch(setSelectedFolders([]));
			}

			// Reset folder to delete state
			setFolderToDelete(null);
		} catch (error) {
			console.error("Failed to delete folder:", error);
			toast.error(`Failed to delete folder "${folderToDelete.name}"`);
		}
	};

	const handleCloseDeleteModal = () => {
		setFolderToDelete(null);
		closeConfirmModal();
	};

	// Placeholder handlers for image operations
	const handleRenameFolder = (folderId) => {
		console.log("Rename folder:", folderId);
	};

	const handleRenameImage = (imageId) => {
		console.log("Rename image:", imageId);
	};

	const handleDeleteImage = (imageId) => {
		console.log("Delete image:", imageId);
	};

	const handleAddImage = (imageId) => {
		console.log("Add image:", imageId);
	};

	return (
		<>
			<div className={styles.modal}>
				<OutsideClickHandler
					onOutsideClick={() => {
						// Don't close media library if any modal is open
						if (!showCreateFolder && !isModalOpen) {
							closeModal();
						}
					}}
				>
					<div className={styles.container}>
						<MediaLibraryHeader onClose={closeModal} />
						<div className={styles.cardBody}>
							<Aside fileType={fileType} folderPath={folderPath} currentFolder={currentFolder} />
							<div className={styles.mainContainer}>
								<MediaLibraryContent
									// Data
									folders={folders}
									media={media}
									selectedImages={selectedImages}
									selectedFolders={selectedFolders}
									mediaPagination={mediaPagination}
									isMultiple={isMultiple}
									// Loading states
									isLoadingFolders={isLoadingFolders}
									isLoadingMedia={isLoadingMedia}
									// Handlers
									getBreadcrumbPath={getBreadcrumbPath}
									onBreadcrumbClick={handleBreadcrumbClick}
									onAddFolderClick={handleAddFolderClick}
									isFolderSelected={isFolderSelected}
									onFolderClick={handleShowFolderDetails}
									onFolderDoubleClick={handleFolderEnter}
									onRenameFolder={handleRenameFolder}
									onDeleteFolder={handleDeleteFolder}
									isImageSelected={isImageSelected}
									onMediaClick={handleImageSelect}
									onRenameImage={handleRenameImage}
									onDeleteImage={handleDeleteImage}
									onAddImage={handleAddImage}
									onLoadMore={handleLoadMore}
								/>
							</div>
						</div>
					</div>
				</OutsideClickHandler>

				{/* Create Folder Modal */}
				<CreateFolderModal
					isOpen={showCreateFolder}
					folderName={newFolderName}
					onFolderNameChange={setNewFolderName}
					onCreateFolder={handleCreateFolder}
					onCancel={handleCancelCreateFolder}
					isCreating={isCreatingFolder}
					folderHierarchy={folderHierarchy}
				/>
			</div>

			{/* Confirmation Modal for Folder Deletion - Outside main container */}
			<div style={{ zIndex: 10000 }}>
				<ConfirmationModal
					isOpen={isModalOpen}
					isLoading={isDeletingFolder}
					toggleModal={handleCloseDeleteModal}
					message={
						folderToDelete
							? `Are you sure you want to delete the folder "${folderToDelete.name}"? This action cannot be undone.`
							: "Are you sure you want to delete this folder?"
					}
					onConfirm={confirmDeleteFolder}
				/>
			</div>
		</>
	);
};

export default MediaLibraryModal;
