import React, { useState } from "react";
import { Fi<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>lertCircle } from "react-icons/fi";
import { motion, AnimatePresence } from "framer-motion";

const UploadProgress = ({ uploads, onClose, onRetry }) => {
	const [isMinimized, setIsMinimized] = useState(false);

	if (uploads.length === 0) return null;

	const getStatusIcon = (status) => {
		switch (status) {
			case "completed":
				return <FiCheck className="text-green-500" />;
			case "error":
				return <FiAlertCircle className="text-red-500" />;
			default:
				return (
					<div className="animate-spin rounded-full h-4 w-4 border-2 border-blue-300 border-t-blue-600"></div>
				);
		}
	};

	const getStatusColor = (status) => {
		switch (status) {
			case "completed":
				return "bg-green-500";
			case "error":
				return "bg-red-500";
			default:
				return "bg-blue-500";
		}
	};

	return (
		<AnimatePresence>
			<motion.div
				initial={{ opacity: 0, y: 100, scale: 0.9 }}
				animate={{ opacity: 1, y: 0, scale: 1 }}
				exit={{ opacity: 0, y: 100, scale: 0.9 }}
				transition={{ duration: 0.3, ease: "easeOut" }}
				className="fixed bottom-4 right-4 z-50 bg-white rounded-lg shadow-2xl border border-gray-200 w-80 max-h-96 overflow-hidden"
			>
				{/* Header */}
				<div className="flex items-center justify-between p-4 border-b border-gray-100 bg-gray-50">
					<div className="flex items-center space-x-2">
						<h3 className="font-medium text-gray-900">Upload Progress</h3>
					</div>
					<div className="flex items-center space-x-2">
						<button
							onClick={() => setIsMinimized(!isMinimized)}
							className="text-gray-400 hover:text-gray-600 transition-colors"
						>
							<motion.div
								animate={{ rotate: isMinimized ? 180 : 0 }}
								transition={{ duration: 0.2 }}
							>
								<svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path
										strokeLinecap="round"
										strokeLinejoin="round"
										strokeWidth={2}
										d="M19 9l-7 7-7-7"
									/>
								</svg>
							</motion.div>
						</button>
						<button
							onClick={onClose}
							className="text-gray-400 hover:text-gray-600 transition-colors"
						>
							<FiX className="w-4 h-4" />
						</button>
					</div>
				</div>

				{/* File List */}
				<AnimatePresence>
					{!isMinimized && (
						<motion.div
							initial={{ height: 0, opacity: 0 }}
							animate={{ height: "auto", opacity: 1 }}
							exit={{ height: 0, opacity: 0 }}
							transition={{ duration: 0.3 }}
							className="max-h-64 overflow-y-auto"
						>
							{uploads.map((upload, index) => (
								<motion.div
									key={upload.id}
									initial={{ opacity: 0, x: 20 }}
									animate={{ opacity: 1, x: 0 }}
									transition={{ delay: index * 0.1 }}
									className="p-3 border-b border-gray-50 last:border-b-0"
								>
									<div className="flex items-center space-x-3">
										<div className="flex-shrink-0">{getStatusIcon(upload.status)}</div>
										<div className="flex-1 min-w-0">
											<p className="text-sm font-medium text-gray-900 truncate">
												{upload.fileName}
											</p>
											<div className="flex items-center space-x-2 mt-1">
												<div className="flex-1 bg-gray-200 rounded-full h-1.5 overflow-hidden">
													<motion.div
														className={`h-full rounded-full ${getStatusColor(upload.status)}`}
														initial={{ width: 0 }}
														animate={{ width: `${upload.progress}%` }}
														transition={{ duration: 0.3, ease: "easeOut" }}
													/>
												</div>
												<span className="text-xs text-gray-500">{upload.progress}%</span>
											</div>
											{upload.status === "error" && (
												<div className="flex items-center justify-between mt-1">
													<p className="text-xs text-red-600">{upload.error}</p>
													{onRetry && (
														<button
															onClick={() => onRetry(upload.id)}
															className="text-xs text-blue-600 hover:text-blue-800 font-medium"
														>
															Retry
														</button>
													)}
												</div>
											)}
										</div>
									</div>
								</motion.div>
							))}
						</motion.div>
					)}
				</AnimatePresence>
			</motion.div>
		</AnimatePresence>
	);
};

export default UploadProgress;
