const Grid = ({
  width = 20,
  height = 20,
  fontSize,
  strokeWidth = 1,
  fill = "currentColor",
  stroke,
  ...rest
}) => {
  const style = fontSize ? { fontSize } : undefined;
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 50 50"
      fill={fill}
      stroke={stroke}
      strokeWidth={strokeWidth}
      style={style}
      xmlns="http://www.w3.org/2000/svg"
      {...rest}
    >
      <path
        fillRule="evenodd"
        fill={fill}
        d="M3 2h20a1 1 0 0 1 1 1v20a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V3a1 1 0 0 1 1-1zm0 1v20h20V3H3zm24-1h20a1 1 0 0 1 1 1v20a1 1 0 0 1-1 1H27a1 1 0 0 1-1-1V3a1 1 0 0 1 1-1zm0 1v20h20V3H27zM3 26h20a1 1 0 0 1 1 1v20a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V27a1 1 0 0 1 1-1zm0 1v20h20V27H3zm24-1h20a1 1 0 0 1 1 1v20a1 1 0 0 1-1 1H27a1 1 0 0 1-1-1V27a1 1 0 0 1 1-1zm0 1v20h20V27H27z"
      />
    </svg>
  );
};

export default Grid;
