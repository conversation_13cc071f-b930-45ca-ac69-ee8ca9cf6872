import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import Button from "./Button";
import { RxCross2 } from "react-icons/rx";
import Accordion, { AccordionProvider } from "./Accordion";
import Select from "react-select";
import {
  applyLocalFilterState,
  incrementOffset,
  resetTableState,
  setAddFilter,
  toggleFilters,
} from "../store/slices/tableSlice";
import { useGetTagsListQuery } from "../store/apis/tagsApi";
import { useGetAuthorsListQuery } from "../store/apis/authorsApi";
import { usePostCategoriesMutation } from "../store/apis/storiesApi";
import OutsideClickHandler from "react-outside-click-handler";

const Filters = ({ isVideo = false }) => {
  const { showFilters, offset } = useSelector((state) => state.table);
  const dispatch = useDispatch();

  // Local state for filters
  const [filterState, setFilterState] = useState({
    publishedTime: "",
    tags: [],
    authors: [],
    categories: [],
  });

  const [
    postCategories,
    { isLoading: categoriesLoading, data: categoriesData },
  ] = usePostCategoriesMutation();

  const { data: fetchedTags, isFetching } = useGetTagsListQuery({
    search: "",
    limit: 10,
    offset,
  });
  const { data: authorsData } = useGetAuthorsListQuery({
    search: "",
    limit: 100,
  });

  useEffect(() => {
    postCategories({ isVideo });
  }, [isVideo, postCategories]);

  // Handlers for managing local state
  const handlePublishedTimeChange = (e) =>
    setFilterState((prev) => ({ ...prev, publishedTime: e.target.value }));

  const handleTagClick = (tagId) => {
    setFilterState((prev) => ({
      ...prev,
      tags: prev.tags.includes(tagId)
        ? prev.tags.filter((id) => id !== tagId)
        : [...prev.tags, tagId],
    }));
  };

  useEffect(() => {
    return () => {
      dispatch(resetTableState());
    };
  }, []);

  const handleAuthorsChange = (selected) => {
    const authorIds = selected.map((author) => author.value);
    setFilterState((prev) => ({ ...prev, authors: authorIds }));
  };

  const handleCategoryChange = (categoryId) => {
    setFilterState((prev) => ({
      ...prev,
      categories: prev.categories.includes(categoryId)
        ? prev.categories.filter((id) => id !== categoryId)
        : [...prev.categories, categoryId],
    }));
  };

  // Function to handle adding a tag
  const handleAddTag = (tagElem) => {
    setFilterState((prev) => ({
      ...prev,
      tags: [...prev.tags, tagElem._id], // Add the tag ID to the selected list
    }));
  };

  // Function to handle removing a tag
  const handleDeleteTag = (tagElem) => {
    setFilterState((prev) => ({
      ...prev,
      tags: prev.tags.filter((id) => id !== tagElem._id), // Remove the tag ID from the selected list
    }));
  };

  // Show more tags when "Show More" button is clicked
  const handleShowMore = () => {
    dispatch(incrementOffset());
  };

  // Submit button sends local state to Redux
  const handleFilterSubmit = () => {
    dispatch(setAddFilter(true)); // Update Redux store with local state
    dispatch(applyLocalFilterState(filterState));
    dispatch(toggleFilters()); // Close filters
  };

  return (
    <div
      className={`${
        showFilters ? "block" : "hidden w-0"
      } fixed top-0 right-0 z-20 h-screen w-full bg-slate-600 bg-opacity-40 rounded-r-md shadow-2xl transition-all duration-800 ease-in-out overflow-hidden`}
    >
      <div className="w-full md:w-96 bg-white absolute right-0 top-0 h-screen">
        <OutsideClickHandler
          onOutsideClick={() => showFilters && dispatch(toggleFilters())}
        >
          <div className="flex items-center w-full justify-between border-b p-5">
            <h2 className="text-lg font-semibold">Filter Your Stories</h2>
            <Button
              variant="outline"
              customClasses="border-none"
              onClick={() => dispatch(toggleFilters())}
            >
              <RxCross2 className="text-2xl text-gray-800 font-extrabold" />
            </Button>
          </div>
          <div>
            <AccordionProvider>
              <div className="divide-y divide-slate-200">
                {/* Published Time */}
                <Accordion title="Published Time" index={0}>
                  <select
                    value={filterState.publishedTime}
                    onChange={handlePublishedTimeChange}
                    className="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
                  >
                    <option value="">Select</option>
                    <option value="lastWeek">Last 1 Week</option>
                    <option value="last3Month">Last 3 Months</option>
                    <option value="last6Month">Last 6 Months</option>
                    <option value="last12Month">Last 12 Months</option>
                  </select>
                </Accordion>

                {/* Tags */}
                <Accordion title="Tags" index={1}>
                  <div className="flex flex-wrap gap-2 overflow-y-scroll max-h-96">
                    {/* Tags Display */}
                    <div className="flex flex-wrap gap-2 mt-4">
                      {fetchedTags
                        ? fetchedTags.data.map((tagElem) => (
                            <button
                              key={tagElem._id}
                              onClick={() =>
                                filterState.tags.includes(tagElem._id)
                                  ? handleDeleteTag(tagElem)
                                  : handleAddTag(tagElem)
                              }
                              className={`px-3 py-1 border rounded-full group flex items-center justify-between ${
                                filterState.tags.includes(tagElem._id)
                                  ? "bg-blue-500 text-white"
                                  : "bg-gray-100 text-black"
                              }`}
                            >
                              {tagElem.name}
                              <RxCross2
                                className={`text-[16px] ml-2 ${
                                  filterState.tags.includes(tagElem._id)
                                    ? ""
                                    : "hidden"
                                }`}
                              />
                            </button>
                          ))
                        : null}
                    </div>

                    {/* Show More Button */}
                    {fetchedTags?.data?.length < fetchedTags?.count &&
                      (isFetching ? (
                        <div className="text-center min-h-10 w-full pb-10">
                          <div className="animate-spin rounded-full border-4 border-gray-200 border-t-primary h-8 w-8 mx-auto" />
                        </div>
                      ) : (
                        <button
                          className="text-primary mt-3"
                          onClick={handleShowMore}
                        >
                          {fetchedTags?.data?.length < fetchedTags?.count
                            ? "Show More..."
                            : ""}
                        </button>
                      ))}
                  </div>
                </Accordion>

                {/* Authors */}
                <Accordion title="Authors" index={2}>
                  <Select
                    isMulti
                    value={authorsData?.data
                      .filter((author) =>
                        filterState.authors.includes(author._id)
                      )
                      .map((author) => ({
                        value: author._id,
                        label: author.name,
                      }))}
                    options={authorsData?.data.map((author) => ({
                      value: author._id,
                      label: author.name,
                    }))}
                    onChange={handleAuthorsChange}
                  />
                </Accordion>

                {/* Categories */}
                <Accordion title="Category" index={3}>
                  <div className="text-sm flex flex-col gap-y-4 mt-4 max-h-96 overflow-y-scroll py-5">
                    {categoriesData?.data.map((category) => (
                      <div key={category._id}>
                        <div className="text-[#00000080]">{category.name}</div>
                        <div className="pl-2 mt-2 flex flex-col gap-y-2 w-full">
                          {category.subcategory.map((subCat) => (
                            <div
                              key={subCat._id}
                              className="flex items-center gap-x-3 w-full pr-6"
                            >
                              <input
                                type="checkbox"
                                checked={filterState.categories.includes(
                                  subCat._id
                                )}
                                id={subCat._id}
                                onChange={() =>
                                  handleCategoryChange(subCat._id)
                                }
                                className="w-4 h-4"
                              />
                              <label
                                htmlFor={subCat._id}
                                className="text-fadeGray"
                              >
                                {subCat.name}
                              </label>
                            </div>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                </Accordion>
              </div>
            </AccordionProvider>
          </div>
          <div className="text-black absolute bottom-0 w-full flex justify-between px-5 py-2 border-t">
            <Button
              variant="outline"
              customClasses="border-gray-500 text-gray-500"
              rounded="full"
              onClick={() => dispatch(toggleFilters())}
            >
              Cancel
            </Button>
            <Button rounded="full" onClick={handleFilterSubmit}>
              Submit
            </Button>
          </div>
        </OutsideClickHandler>
      </div>
    </div>
  );
};

export default Filters;
