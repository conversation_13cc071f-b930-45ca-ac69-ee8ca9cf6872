import React from "react";

const Grid = ({
  data = [],
  properties = { resize: "cover", ratio: "1/1", column: 3 },
}) => {
  return (
    <>
      <div
        className={`grid gap-[5px]`}
        style={{ gridTemplateColumns: `repeat(${properties.column}, 1fr)` }}
      >
        {data?.map((item, index) => {
          return (
            <div
              className={`relative w-full h-fit overflow-hidden`}
              style={{ aspectRatio: properties.ratio }}
              key={`grid-item-${index}`}
            >
              <img
                className={`absolute inset-0 w-full h-full`}
                style={{ objectFit: properties.resize }}
                src={item?.src || ""}
                alt={item?.alt || `Grid Image ${index}`}
              />
            </div>
          );
        })}
      </div>
    </>
  );
};

export default Grid;
