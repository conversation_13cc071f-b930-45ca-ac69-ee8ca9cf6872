import React from "react";

const AlignLeft = () => {
  return (
    <svg
      width={16}
      height={16}
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M1 0H0V16H1V0ZM16 10.5C16 9.7 15.3 9 14.5 9H4.5C3.7 9 3 9.7 3 10.5V13.5C3 14.3 3.7 15 4.5 15H14.5C15.3 15 16 14.3 16 13.5V10.5ZM15 13.5C15 13.8 14.8 14 14.5 14H4.5C4.2 14 4 13.8 4 13.5V10.5C4 10.2 4.2 10 4.5 10H14.5C14.8 10 15 10.2 15 10.5V13.5ZM9.5 7H4.5C3.7 7 3 6.3 3 5.5V2.5C3 1.7 3.7 1 4.5 1H9.5C10.3 1 11 1.7 11 2.5V5.5C11 6.3 10.3 7 9.5 7ZM4.5 2C4.2 2 4 2.2 4 2.5V5.5C4 5.8 4.2 6 4.5 6H9.5C9.8 6 10 5.8 10 5.5V2.5C10 2.2 9.8 2 9.5 2H4.5Z"
        fill="currentColor"
      ></path>
    </svg>
  );
};

export default AlignLeft;
