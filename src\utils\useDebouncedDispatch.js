import { useDispatch } from "react-redux";
import { useCallback } from "react";
import debounce from "lodash.debounce";

// Utility to create a debounced dispatch function
const useDebouncedDispatch = (debounceTime = 500) => {
  const dispatch = useDispatch();

  return useCallback(
    debounce((actionCreator, name, value) => {
      dispatch(actionCreator({ name, value }));
    }, debounceTime),
    [dispatch, debounceTime]
  );
};

export default useDebouncedDispatch;
