import { createSlice } from "@reduxjs/toolkit";

// Load initial state from localStorage
const loadState = () => {
  try {
    const token = localStorage.getItem('token');
    const user = localStorage.getItem('user');
    return {
      token: token ? token : null,
      user: user ? JSON.parse(user) : null,
    };
  } catch (err) {
    // If there's any error, return the default initial state
    return {
      token: null,
      user: null,
    };
  }
};

const initialState = loadState();

export const userSlice = createSlice({
  name: "user",
  initialState,
  reducers: {
    login: (state, {payload}) => {
      const { token, user } = payload;
      state.token = token;
      state.user = user;  
      localStorage.setItem("token",
        token);
      localStorage.setItem("user", JSON.stringify(user));
    },
    logout: (state, action) => {
      state.token = null;
      state.user = null;
      localStorage.removeItem("token");
      localStorage.removeItem("user");
    },
  },
});
1;

// Action creators are generated for each case reducer function
export const { login, logout } = userSlice.actions;

export default userSlice.reducer;
