import { BubbleMenu as BaseBubbleMenu, useEditorState } from "@tiptap/react";
import React, { useState, useEffect, useCallback, useRef } from "react";
import { getRenderContainer } from "../../../utils/getRenderContainer";
import SizeSmall from "../../../../../public/svg/SizeSmall";
import SizeMedium from "../../../../../public/svg/SizeMedium";
import SizeFullWidth from "../../../../../public/svg/SizeFullWidth";
import SizeOriginal from "../../../../../public/svg/SizeOriginal";
import AlignLeft from "../../../../../public/svg/AlignLeft";
import AlignCenter from "../../../../../public/svg/AlignCenter";
import AlignRight from "../../../../../public/svg/AlignRight";
import "../../../../styles/stories.css";
import { FiSettings, FiX } from "react-icons/fi";
import {
  closeAllDropdowns,
  toggleDropdown,
} from "../../../../store/slices/editorSlice";
import { MdKeyboardArrowDown } from "react-icons/md";
import { BiTrash } from "react-icons/bi";
import { useDispatch, useSelector } from "react-redux";

import { DropdownMenu } from "../../../../parts/FormComponents";
import Button from "../../../../parts/Button";

const imageSizeIcons = {
  50: <SizeSmall />,
  75: <SizeMedium />,
  0: <SizeFullWidth />,
  100: <SizeOriginal />,
};

const imageAlignIcons = {
  left: <AlignLeft />,
  center: <AlignCenter />,
  right: <AlignRight />,
};

export const VideoBlockMenu = ({ editor, appendTo }) => {
  const menuRef = useRef(null);
  const tippyInstance = useRef(null);
  const [open, setOpen] = useState(false);
  const { dropdowns } = useSelector((state) => state.editor);
  const [caption, setCaption] = useState("");
  const [courtesy, setCourtesy] = useState("");
  const [alt, setAlt] = useState("");
  const [imageContent, setImageContent] = useState({
    size: 100,
    imageAlign: "left",
  });
  const dispatch = useDispatch();

  useEffect(() => {
    if (editor) {
      const attributes = editor.getAttributes("imageBlock");
      setCaption(attributes?.caption ?? "");
      setCourtesy(attributes?.courtesy ?? "");
      setAlt(attributes?.alt ?? "");
    }
  }, [editor, open]);

  const createTrigger = useCallback(
    (content, dropdownKey) => (
      <button
        className="p-1 hover:bg-gray-100 rounded-sm flex items-center gap-1"
        onClick={() => dispatch(toggleDropdown(dropdownKey))}
      >
        {content}
        <MdKeyboardArrowDown size={16} />
      </button>
    ),
    [dispatch]
  );

  const getReferenceClientRect = useCallback(() => {
    const renderContainer = getRenderContainer(editor, "node-videoBlock");
    const rect =
      renderContainer?.getBoundingClientRect() ||
      new DOMRect(-1000, -1000, 0, 0);

    return rect;
  }, [editor]);

  const onImagePropChange = ({ id, value }) => {
    setImageContent({ ...imageContent, [id]: value });
  };

  const shouldShow = useCallback(() => {
    const isActive = editor.isActive("videoBlock");
    return isActive;
  }, [editor]);

  const onAlignVideo = useCallback(
    (alignment) => {
      editor
        .chain()
        .focus(undefined, { scrollIntoView: false })
        .setVideoBlockAlign(alignment)
        .run();
    },
    [editor]
  );

  const onWidthChange = useCallback(
    (value) => {
      editor
        .chain()
        .focus(undefined, { scrollIntoView: false })
        .setVideoBlockWidth(value)
        .run();
    },
    [editor]
  );

  const updateVideoAttributes = useCallback(() => {
    editor
      .chain()
      .focus(undefined, { scrollIntoView: false })
      .setVideoBlockCaption(caption)
      .setVideoBlockCourtesy(courtesy)
      .setVideoBlockAlt(alt)
      .run();
  }, [editor, caption, courtesy, alt]);

  return (
    <>
      <BaseBubbleMenu
        editor={editor}
        pluginKey="videoBlockMenu"
        shouldShow={shouldShow}
        updateDelay={0}
        tippyOptions={{
          interactive: true,
          offset: [0, 8],
          maxWidth: "100%",
          popperOptions: {
            modifiers: [{ name: "flip", enabled: false }],
          },
          getReferenceClientRect,
          onCreate: (instance) => {
            tippyInstance.current = instance;
          },
          appendTo: () => {
            return appendTo?.current;
          },
        }}
      >
        <div className="bubble-menu" ref={menuRef}>
          <div className="bubble-menu-icon text-fadeGray py-1 text-xs ">
            <DropdownMenu
              isOpen={dropdowns.size}
              onSelect={(value) => {
                onWidthChange(parseInt(value));
                onImagePropChange({ id: "size", value });
                dispatch(closeAllDropdowns());
              }}
              trigger={createTrigger(
                <button className="px-1 rounded-sm flex items-center justify-center">
                  {imageSizeIcons[imageContent.size]}
                </button>,
                "size"
              )}
            >
              <button
                value={50}
                className="w-full hover:bg-blueShade px-2 py-2 whitespace-nowrap flex items-center gap-2"
              >
                <SizeSmall /> Small
              </button>
              <button
                value={75}
                className="w-full hover:bg-blueShade px-2 py-2 whitespace-nowrap flex items-center gap-2"
              >
                <SizeMedium /> Medium
              </button>
              <button
                value={0}
                className="w-full hover:bg-blueShade px-2 py-2 whitespace-nowrap flex items-center gap-2"
              >
                <SizeFullWidth /> Full Width
              </button>
              <button
                value={100}
                className="w-full hover:bg-blueShade px-2 whitespace-nowrap py-2 flex items-center gap-2"
              >
                <SizeOriginal /> Original Size
              </button>
            </DropdownMenu>
          </div>
          <span className="bubble-menu-divider"></span>
          <div className="bubble-menu-icon text-fadeGray py-1 text-xs ">
            {/* Dropdown Menu */}
            <DropdownMenu
              isOpen={dropdowns.imageAlignment}
              onSelect={(value) => {
                onAlignVideo(value);
                onImagePropChange({ id: "imageAlign", value });
                dispatch(closeAllDropdowns());
              }}
              trigger={createTrigger(
                <button className="px-1 rounded-sm flex items-center justify-center">
                  {imageAlignIcons[imageContent.imageAlign]}
                </button>,
                "imageAlignment"
              )}
            >
              <button
                value="left"
                className="w-full hover:bg-blueShade px-2 py-2 whitespace-nowrap flex items-center gap-2"
              >
                <AlignLeft /> <span>Align left</span>
              </button>
              <button
                value="center"
                className="w-full hover:bg-blueShade px-2 py-2 whitespace-nowrap flex items-center gap-2"
              >
                <AlignCenter /> <span>Align center</span>
              </button>
              <button
                value="right"
                className="w-full hover:bg-blueShade px-2 py-2 whitespace-nowrap flex items-center gap-2"
              >
                <AlignRight /> <span>Align right</span>
              </button>
            </DropdownMenu>
          </div>
          <span className="bubble-menu-divider"></span>
          <button
            className="bubble-menu-icon text-fadeGray"
            onClick={() => setOpen(true)}
          >
            <div className="p-1 hover:bg-gray-100 rounded-sm flex items-center gap-1">
              <FiSettings />
            </div>
          </button>
          {/* <span className="bubble-menu-divider"></span>
          <button className="bubble-menu-icon text-fadeGray" onClick={() => onRemoveImage()}>
            <div className="p-1 hover:bg-gray-100 rounded-sm flex items-center gap-1">
              <BiTrash />
            </div>
          </button> */}
        </div>
      </BaseBubbleMenu>
      <div
        className={`${
          open ? "block" : "hidden"
        } fixed top-0 right-0 z-20 h-screen w-full bg-slate-600 bg-opacity-40 rounded-r-md shadow-2xl transition-all duration-800 ease-in-out overflow-hidden`}
      >
        <div className="w-full md:w-96 bg-white absolute right-0 top-0 h-screen text-fadeGray">
          <div className="menu-drawer">
            <div className="headsec">
              <h2 className="heading">Video</h2>
              <FiX className="close" onClick={() => setOpen(false)} />
            </div>
            <div className="w-full p-5 h-[calc(100vh-129px)] overflow-y-scroll">
              <div className="flex flex-col gap-y-4">
                <div className="flex flex-col gap-y-2">
                  <p className="text-base">Video</p>
                  <div className="flex justify-center">
                    <video
                      controls
                      src={editor.getAttributes("videoBlock")?.src || ""}
                      alt="Selected file"
                    />
                  </div>
                </div>
                <div className="flex flex-col gap-y-2">
                  <div className="text-base">Caption</div>
                  <input
                    id="caption"
                    type="text"
                    className="border text-sm border-gray-300 rounded-md px-4 py-2 w-full focus:outline-none focus:border-blue-500"
                    value={caption}
                    onChange={(e) => setCaption(e.target.value)}
                    placeholder="Add a caption here"
                  />
                </div>
                <div className="flex flex-col gap-y-2">
                  <p className="text-base">Courtesy</p>
                  <input
                    id="courtesy"
                    type="text"
                    className="border text-sm border-gray-300 rounded-md px-4 py-2 w-full focus:outline-none focus:border-blue-500"
                    value={courtesy}
                    onChange={(e) => setCourtesy(e.target.value)}
                    placeholder="Add a courtesy here"
                  />
                </div>
                <div className="flex flex-col gap-y-2">
                  <p className="general-p-small">Alt name</p>
                  <input
                    id="alt"
                    type="text"
                    className="border text-sm border-gray-300 rounded-md px-4 py-2 w-full focus:outline-none focus:border-blue-500"
                    value={alt}
                    onChange={(e) => setAlt(e.target.value)}
                    placeholder="e.g. A cat sleeping on a white blanket"
                  />
                </div>
              </div>
            </div>
            <div className="footersec">
              <Button
                variant="secondary"
                rounded="full"
                className="btn-primary-outline"
                onClick={() => setOpen(false)}
              >
                Cancel
              </Button>
              <Button
                rounded="full"
                onClick={() => {
                  updateVideoAttributes();
                  setOpen(false);
                }}
              >
                Save
              </Button>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default VideoBlockMenu;
