import React from "react";
import { GoCheck } from "react-icons/go";
import styles from "../MediaLibrary.module.css";
import Button from "../../../parts/Button";

const Gallery = ({
  isLoading,
  hasMore,
  isMultiple,
  data = [],
  selectedFiles = [],
  loadMedia,
  setSelectedFiles,
}) => {
  const isSameFile = (a, b) => a.imageUrl === b.imageUrl;

  const isSelected = (file) =>
    selectedFiles.some((selected) => isSameFile(selected, file));

  const handleSelectFile = (fileData) => {
    setSelectedFiles((prev) => {
      const exists = prev.some((item) => isSameFile(item, fileData));
      if (isMultiple) {
        return exists
          ? prev.filter((item) => !isSameFile(item, fileData))
          : [...prev, fileData];
      } else {
        return exists ? [] : [fileData];
      }
    });
  };

  return (
    <div className={`${styles.galleryContainer} overflow-y-auto scrollbar`}>
      <div className="w-full grid grid-cols-4 gap-4">
        {data.map((item, index) => (
          <div
            className="relative flex justify-center group cursor-pointer"
            key={`media-item-${index}`}
            onClick={() => handleSelectFile(item)}
          >
            {isSelected(item) && (
              <div className="absolute right-[-5px] top-[-5px] flex justify-center items-center w-[20px] h-[20px] z-50 rounded-full bg-[#3b82f6]">
                <GoCheck className="text-white text-sm stroke-1" />
              </div>
            )}

            <div
              className={`w-full h-fit mx-auto aspect-square overflow-hidden bg-gray-100 relative transition-all duration-100 ease-in-out hover:opacity-70 hover:border-2 hover:border-[#3b82f6] ${
                isSelected(item) ? "border-2 border-[#3b82f6]" : ""
              }`}
            >
              <img
                className="absolute inset-0 w-full h-full object-contain"
                src={item?.imageUrl || ""}
                alt={`Media Image ${index}`}
              />
            </div>
          </div>
        ))}
      </div>

      {/* Loader */}
      {isLoading && (
        <div className="flex justify-center my-4">
          <div className="animate-spin rounded-full h-8 w-8 border-[2px] border-gray-300 border-t-blue-500"></div>
        </div>
      )}

      {/* Load More Button */}
      {!isLoading && hasMore && (
        <div className="flex justify-center my-4">
          <Button
            variant="primary"
            size="sm"
            rounded="full"
            className="btn-primary"
            onClick={() => loadMedia()}
          >
            Load More
          </Button>
        </div>
      )}
    </div>
  );
};

export default Gallery;
