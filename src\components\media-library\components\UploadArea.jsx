import React, { useState, useRef } from "react";
import { useDispatch, useSelector } from "react-redux";
import { toast } from "react-toastify";
import { MdCloudUpload, MdClose } from "react-icons/md";
import {
	setUploadProgress,
	addUploadProgress,
	updateUploadProgress,
	removeUploadProgress,
	setIsUploading,
} from "../../../store/slices/mediaLibrarySlice";

/**
 * Component for file upload functionality
 */
const UploadArea = ({ 
	currentFolder = null,
	onUploadComplete,
	acceptedFileTypes = "image/*,video/*",
	maxFileSize = 10 * 1024 * 1024, // 10MB
	className = "" 
}) => {
	const dispatch = useDispatch();
	const upload = useSelector((state) => state.mediaLibrary.upload);
	const [isDragOver, setIsDragOver] = useState(false);
	const fileInputRef = useRef(null);
	
	const handleFileSelect = (files) => {
		const fileArray = Array.from(files);
		const validFiles = fileArray.filter(file => {
			// Check file size
			if (file.size > maxFileSize) {
				toast.error(`File ${file.name} is too large. Maximum size is ${formatBytes(maxFileSize)}`);
				return false;
			}
			
			// Check file type
			const acceptedTypes = acceptedFileTypes.split(',').map(type => type.trim());
			const isValidType = acceptedTypes.some(type => {
				if (type === '*/*') return true;
				if (type.endsWith('/*')) {
					return file.type.startsWith(type.slice(0, -1));
				}
				return file.type === type;
			});
			
			if (!isValidType) {
				toast.error(`File ${file.name} is not a supported file type`);
				return false;
			}
			
			return true;
		});
		
		if (validFiles.length > 0) {
			uploadFiles(validFiles);
		}
	};
	
	const uploadFiles = async (files) => {
		dispatch(setIsUploading(true));
		
		// Initialize progress for each file
		const initialProgress = files.map(file => ({
			id: `${file.name}-${Date.now()}-${Math.random()}`,
			name: file.name,
			size: file.size,
			progress: 0,
			status: 'uploading', // uploading, completed, error
			file: file
		}));
		
		dispatch(setUploadProgress(initialProgress));
		
		// Upload files one by one or in parallel
		const uploadPromises = initialProgress.map(async (fileProgress) => {
			try {
				await uploadSingleFile(fileProgress);
			} catch (error) {
				dispatch(updateUploadProgress({
					id: fileProgress.id,
					progress: { status: 'error', error: error.message }
				}));
			}
		});
		
		await Promise.all(uploadPromises);
		dispatch(setIsUploading(false));
		
		if (onUploadComplete) {
			onUploadComplete();
		}
	};
	
	const uploadSingleFile = async (fileProgress) => {
		const formData = new FormData();
		formData.append('file', fileProgress.file);
		formData.append('folder', currentFolder?._id || '');
		formData.append('path', currentFolder?.path || 'media/');
		
		try {
			const xhr = new XMLHttpRequest();
			
			// Track upload progress
			xhr.upload.addEventListener('progress', (event) => {
				if (event.lengthComputable) {
					const progress = Math.round((event.loaded / event.total) * 100);
					dispatch(updateUploadProgress({
						id: fileProgress.id,
						progress: { progress }
					}));
				}
			});
			
			// Handle completion
			xhr.addEventListener('load', () => {
				if (xhr.status === 200) {
					const response = JSON.parse(xhr.responseText);
					if (response.status === 'success') {
						dispatch(updateUploadProgress({
							id: fileProgress.id,
							progress: { status: 'completed', progress: 100 }
						}));
						toast.success(`${fileProgress.name} uploaded successfully`);
					} else {
						throw new Error(response.message || 'Upload failed');
					}
				} else {
					throw new Error(`Upload failed with status ${xhr.status}`);
				}
			});
			
			// Handle errors
			xhr.addEventListener('error', () => {
				throw new Error('Upload failed due to network error');
			});
			
			// Start upload
			xhr.open('POST', '/api/media-library/upload');
			xhr.send(formData);
			
			// Return a promise that resolves when upload is complete
			return new Promise((resolve, reject) => {
				xhr.addEventListener('load', () => {
					if (xhr.status === 200) {
						resolve();
					} else {
						reject(new Error(`Upload failed with status ${xhr.status}`));
					}
				});
				xhr.addEventListener('error', () => {
					reject(new Error('Upload failed due to network error'));
				});
			});
			
		} catch (error) {
			console.error('Upload error:', error);
			throw error;
		}
	};
	
	const formatBytes = (bytes, decimals = 2) => {
		if (bytes === 0) return '0 Bytes';
		const k = 1024;
		const dm = decimals < 0 ? 0 : decimals;
		const sizes = ['Bytes', 'KB', 'MB', 'GB'];
		const i = Math.floor(Math.log(bytes) / Math.log(k));
		return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
	};
	
	const handleDragOver = (e) => {
		e.preventDefault();
		setIsDragOver(true);
	};
	
	const handleDragLeave = (e) => {
		e.preventDefault();
		setIsDragOver(false);
	};
	
	const handleDrop = (e) => {
		e.preventDefault();
		setIsDragOver(false);
		const files = e.dataTransfer.files;
		handleFileSelect(files);
	};
	
	const handleFileInputChange = (e) => {
		const files = e.target.files;
		if (files && files.length > 0) {
			handleFileSelect(files);
		}
		// Reset input value to allow selecting the same file again
		e.target.value = '';
	};
	
	const removeUploadItem = (id) => {
		dispatch(removeUploadProgress(id));
	};
	
	return (
		<div className={className}>
			{/* Upload Drop Zone */}
			<div
				className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
					isDragOver
						? "border-blue-500 bg-blue-50"
						: "border-gray-300 hover:border-gray-400"
				}`}
				onDragOver={handleDragOver}
				onDragLeave={handleDragLeave}
				onDrop={handleDrop}
			>
				<MdCloudUpload className="mx-auto text-4xl text-gray-400 mb-4" />
				<p className="text-lg font-medium text-gray-700 mb-2">
					Drop files here or click to upload
				</p>
				<p className="text-sm text-gray-500 mb-4">
					Supports images and videos up to {formatBytes(maxFileSize)}
				</p>
				
				<button
					onClick={() => fileInputRef.current?.click()}
					className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
					disabled={upload.isUploading}
				>
					{upload.isUploading ? "Uploading..." : "Choose Files"}
				</button>
				
				<input
					ref={fileInputRef}
					type="file"
					multiple
					accept={acceptedFileTypes}
					onChange={handleFileInputChange}
					className="hidden"
				/>
			</div>
			
			{/* Upload Progress */}
			{upload.progress.length > 0 && (
				<div className="mt-4 space-y-2">
					<h4 className="text-sm font-medium text-gray-700">Upload Progress</h4>
					{upload.progress.map((item) => (
						<div
							key={item.id}
							className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg"
						>
							{/* File Info */}
							<div className="flex-1 min-w-0">
								<p className="text-sm font-medium text-gray-700 truncate">
									{item.name}
								</p>
								<p className="text-xs text-gray-500">
									{formatBytes(item.size)}
								</p>
							</div>
							
							{/* Progress Bar */}
							<div className="flex-1 max-w-32">
								<div className="w-full bg-gray-200 rounded-full h-2">
									<div
										className={`h-2 rounded-full transition-all duration-300 ${
											item.status === 'completed'
												? 'bg-green-500'
												: item.status === 'error'
												? 'bg-red-500'
												: 'bg-blue-500'
										}`}
										style={{ width: `${item.progress || 0}%` }}
									/>
								</div>
								<p className="text-xs text-gray-500 mt-1">
									{item.status === 'completed'
										? 'Completed'
										: item.status === 'error'
										? 'Error'
										: `${item.progress || 0}%`}
								</p>
							</div>
							
							{/* Remove Button */}
							<button
								onClick={() => removeUploadItem(item.id)}
								className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
								title="Remove"
							>
								<MdClose className="w-4 h-4" />
							</button>
						</div>
					))}
				</div>
			)}
		</div>
	);
};

export default UploadArea;
