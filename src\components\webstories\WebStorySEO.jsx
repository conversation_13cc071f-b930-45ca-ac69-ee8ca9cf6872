import React, { useState } from "react";
import BasicSEO from "../stories/BasicSEO";
import SocialShare from "../stories/SocialShare";
const tabs = [
  { label: "Basics", value: "basics" },
  { label: "Social Share", value: "social_share" },
];

const WebStorySEO = () => {
  const [active, setActive] = useState("basics");
  // Click handler to set the active state and make API call
  const handleTabClick = (tab) => {
    setActive(tab);
  };

  return (
    <div className="mb-5 bg-white rounded-md border border-[#c1e4fe]  pb-4">
      <div className="pl-5 text-lg font-semibold  py-5 border-b">
        SEO Settings
      </div>
      <div className="grid grid-cols-2 -mb-px w-full border-b">
        {tabs.map((tab) => (
          <div key={tab.value} className="me-2">
            <button
              onClick={() => handleTabClick(tab.value)}
              className={`inline-block w-full px-7 h-10 border-b-2 rounded-t-lg hover:text-gray-600 transition-all duration-300 ${
                active === tab.value
                  ? "text-primary border-primary"
                  : "border-transparent"
              }`}
            >
              <h3>{tab.label}</h3>
            </button>
          </div>
        ))}
      </div>
      <div className="mt-4 px-3 ">
        {active === "basics" && <BasicSEO type="webStory" />}
        {active === "social_share" && <SocialShare type="webStory" />}
      </div>
    </div>
  );
};
export default WebStorySEO;
