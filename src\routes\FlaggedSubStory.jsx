import React, { useEffect, useMemo, useState } from "react";
import Container from "../parts/Container";
import Button, { RoundedIconsButton } from "../parts/Button";
import { BiChevronRight, BiPlus } from "react-icons/bi";
import { useDispatch, useSelector } from "react-redux";
import { DropdownButton } from "../parts/FormComponents";
import Filters from "../parts/Filters";
import { BsEye } from "react-icons/bs";
import { FiEdit } from "react-icons/fi";
import { FaRegTrashAlt } from "react-icons/fa";
import { Link, useLocation, useNavigate, useParams, useSearchParams } from "react-router-dom";

import useInfiniteScrollData from "../utils/useInfiniteScrollData";
import DraggableTable from "../parts/DraggableTable";
import {
	incrementOffset,
	resetData,
	resetFilters,
	resetSubFlagFilter,
	setFetchedData,
	setInitialFlaggedStory,
} from "../store/slices/flaggedStoriesSlice";
import { apiEndpoints } from "../utils/constants";
import useConfirmationModal from "../utils/useConfirmationModal";
import {
	useBulkUpdateFlaggedStoryMutation,
	useDeleteFlaggedSubStoryMutation,
	useUpdateStatusMutation,
} from "../store/apis/flaggedStoriesApi";
import ConfirmationModal from "../parts/ConfirmationModal";
import { toast } from "react-toastify";
import Loader from "../parts/Loader";
import AddToSubMenu from "../parts/AddToSubMenu";
import AddArticle from "../components/subcategories/AddArticle";
import BreadCrumb from "../parts/BreadCrumb";
import { resetSubCategoryFilters, setSubCategoryFilter } from "../store/slices/categoriesSlice";

const FlaggedSubStory = () => {
	const frontendUrl = import.meta.env.VITE_CLIENT_URL;
	const navigate = useNavigate();
	const dispatch = useDispatch();
	const { state } = useLocation();
	const [selectedRows, setSelectedRows] = useState([]);
	const [searchParams] = useSearchParams();
	const subsectionIds = JSON.parse(decodeURIComponent(searchParams.get("ids")));
	const name = searchParams.get("name");
	const type = searchParams.get("type");
	const level = searchParams.get("level");
	const { isModalOpen, rowIdToDelete, openModal, closeModal } = useConfirmationModal();
	const [deleteFlaggedSubStory, { isLoading: isDeleting, error: deleteError }] =
		useDeleteFlaggedSubStoryMutation();
	const [bulkUpdateFlaggedStory, { isLoading: isBulkUpdating, error: bulkError }] =
		useBulkUpdateFlaggedStoryMutation();

	const [updateStatus] = useUpdateStatusMutation();
	const [showSideMenu, setShowSideMenu] = useState(false);
	const [refetch, setRefetch] = useState(false);

	const { addFilter, showFilters, tag, category, writer, publishedTime } = useSelector(
		(state) => state.table
	);

	const {
		filter: { section },
	} = useSelector((state) => state.flaggedStory);

	// config for the stories api
	const storiesApiConfig = {
		setDataAction: setFetchedData,
		resetDataAction: resetFilters,
		sliceName: "flaggedStory",
	};

	//resetting the filters on the intital load
	useEffect(() => {
		dispatch(resetFilters());
		if (subsectionIds) {
			dispatch(setInitialFlaggedStory(subsectionIds));
		}

		return () => {
			dispatch(resetData());
			dispatch(resetSubCategoryFilters());
		};
	}, []);

	useEffect(() => {
		dispatch(setSubCategoryFilter({ type, level }));
	}, [type, level]);

	const { data, isLoading, offset, filter, isError, error, fetchData, handleSearch, isFetching } =
		useInfiniteScrollData({
			config: storiesApiConfig,
			url: apiEndpoints.postFlaggedStoryAllList,
			ids: subsectionIds,
			fetch: section.length > 0 ? true : false,
		});
	// handling the error here
	if (isError) {
		if (error.status === 401) {
			toast.error("Session Expired", { toastId: "video_story_fetch_error" });
			navigate("/signin");
		}
	}

	// load the data on intial page load and also when the offset changes
	useEffect(() => {
		fetchData(subsectionIds ? true : false);
	}, [
		offset,
		filter.status,
		filter.search,
		filter.section,
		tag,
		writer,
		category,
		publishedTime,
		refetch,
	]);

	// increase the offset when user has scrolled till the last row of the table which in turn fethes the data
	const fetchMoreData = () => {
		console.log(" fetching more data increasing the offset");
		dispatch(incrementOffset());
	};

	const handleViewClick = (link) => {
		const url = `${frontendUrl}${link}`;
		window.open(url, "_blank", "noopener,noreferrer");
	};

	const onReorder = (newOrder) => {
		const prepareData = newOrder.map((order, index) => ({
			articleId: order._id,
			sectionId: subsectionIds.toString(),
			sortOrder: index,
		}));

		const finalPayload = { type: parseInt(type), articles: prepareData };

		bulkUpdateFlaggedStory(finalPayload)
			.then((res) => {
				if (res.data.status === "success") {
					toast.success("Story order updated successfully!");
					setRefetch(!refetch);
					dispatch(resetData());
					fetchData(true);
				} else {
					toast.error("Failed to update story order.");
				}
			})
			.catch((err) => {
				toast.error("Failed to update story order.");
				console.log(err);
			});
	};

	// used to delete the story
	const handleDelete = () => {
		const payload = {
			type: parseInt(type),
			level: parseInt(level),
			id: subsectionIds[0],
		};
		deleteFlaggedSubStory({ id: rowIdToDelete, data: payload })
			.then((res) => {
				if (res.data.status === "success") {
					toast.success("Story deleted successfully!");
					fetchData(true);
				} else {
					toast.error("Failed to delete story.");
				}
			})
			.catch((err) => {
				toast.error("Failed to delete story.");
				console.log(err);
			})
			.finally(() => {
				closeModal();
			});
	};

	const handleChange = ({ value, id }) => {
		updateStatus({ status: value, id })
			.then((res) => {
				console.log(res);
				if (res.data.status === "success") {
					toast.success("Story status updated successfully!");
					setRefetch(!refetch);
					fetchData(true);
				} else {
					toast.error("Failed to update story status.");
				}
			})
			.catch((err) => {
				toast.error("Failed to update story status.");
				console.log(err);
			});
	};

	const handleEditClick = (rowData) => {
		if (rowData) {
			if (rowData.type === 0) {
				navigate(`/admin/stories/edit/${rowData._id}?location=stories`);
			} else if (rowData.type === 1) {
				navigate(`/admin/edit-video-story/${rowData._id}`);
			} else if (rowData.type === 2) {
				navigate(`/admin/edit-web-story/${rowData._id}`);
			}
		}
	};

	const columns = useMemo(
		() => [
			// {
			//   accessorKey: "_id",
			//   enableSorting: false,
			//   header: () => <IoLockClosedOutline className="texgt-primary text-xl" />,
			//   cell: () => "",
			// },
			{
				accessorKey: "title",
				id: "title",
				header: () => "Story Details",
				cell: ({ row }) => {
					return (
						<div className="flex items-center gap-x-2 font-semibold">
							<img
								src={row.original?.coverImg}
								alt=""
								className="w-[80px] h-[60px] object-cover rounded"
							/>
							<div>
								<div className="line-clamp-2">{row.original?.title}</div>
								<div className="text-[#969696] text-xs font-normal flex items-center space-x-2">
									<span>{new Date(row.original?.timestamp).getHours()} hours ago,</span>
									<span>{row.original?.author?.toString()}</span>
								</div>
							</div>
						</div>
					);
				},
			},
			{
				accessorKey: "type",
				header: "Type",
				cell: ({ row }) => <div>{row.original?.type === 0 ? "Text" : "Video"}</div>,
			},
			{
				accessorKey: "category",
				header: "Category",
				cell: ({ row }) => <div className="line-clamp-3">{row.original?.category?.join(", ")}</div>,
			},

			{
				accessorKey: "tag",
				header: () => "Tags",
				size: 200,
				cell: ({ row }) => <div className="line-clamp-3">{row.original?.tag?.join(", ")}</div>,
			},
			{
				accessorKey: "status",
				header: "Status",
				cell: ({ row }) => (
					<DropdownButton
						selected={row.original.status}
						handleChange={(value) => handleChange({ value, id: row.original._id })}
					/>
				),
			},
			{
				accessorKey: "timestamp",
				header: "Action",
				cell: ({ row }) => {
					return (
						<div className="flex items-center gap-x-2">
							<RoundedIconsButton onClick={() => handleViewClick(row.original?.viewLink)}>
								<BsEye className="h-[15px] w-[15px]" />
							</RoundedIconsButton>

							<RoundedIconsButton onClick={() => handleEditClick(row.original)}>
								<FiEdit className="h-[15px] w-[15px]" />
							</RoundedIconsButton>
							<RoundedIconsButton
								onClick={() => {
									openModal(row.original._id);
								}}
							>
								<FaRegTrashAlt className="h-[15px] w-[15px]" />
							</RoundedIconsButton>
						</div>
					);
				},
			},
		],
		[]
	);

	// funtion to perfrom action on row selection in the table
	const handleRowSelectionChange = (rowIds) => {
		console.log(rowIds, " rows ids");
	};

	// used to perform actions when a row is selected
	const handleRowSelect = (rows) => {
		console.log(rows, " rows");
		setSelectedRows(rows);
		// Make API call or perform other actions with the selected rows
	};

	// used to handle the filter change on the table for the status
	const handleFilterChange = (filterName, value) => {
		console.log(filterName, value, " filter names and value");
		// Update filter state and fetch new data
	};

	return (
		<Container>
			<div className="lg:flex items-center justify-between mb-5">
				<div>
					<div className="flex items-center  text-sm">
						<Link
							to={"/admin/flagged-stories"}
							className="hover:bg-white text-blackShade rounded-full px-3 py-1 bg-transparent "
						>
							Flagged Stories
						</Link>
						<BiChevronRight className="text-xl" />
						<p className="text-fadeGray pl-2">{name || "All"}</p>
					</div>
					<BreadCrumb
						title={"Flagged Stories"}
						description={" Create, customize and manage your stories."}
					/>
				</div>
				<Button
					rounded="full"
					size="sm"
					customClasses="mt-2 lg:mt-0 pb-1.5 pt-1.5"
					onClick={() => {
						setShowSideMenu(!showSideMenu);
						dispatch(setSubCategoryFilter({ type, level }));
					}}
				>
					<BiPlus /> Add Stories
				</Button>
			</div>
			<DraggableTable
				module="flaggedStory"
				data={data}
				level={level ? parseInt(level, 10) : 0}
				isFetching={isFetching}
				isLoading={isLoading || isBulkUpdating}
				columns={columns}
				handleRowSelectionChange={handleRowSelectionChange}
				fetchMoreData={fetchMoreData}
				onReorder={onReorder}
			/>
			<Filters />
			<AddToSubMenu showSideMenu={showSideMenu} setShowSideMenu={setShowSideMenu}>
				<AddArticle
					setShowSideMenu={setShowSideMenu}
					subcategoryIds={subsectionIds}
					level={level}
					type={type}
					id={subsectionIds}
					refetch={refetch}
					setRefetch={setRefetch}
				/>
			</AddToSubMenu>
			<ConfirmationModal
				isOpen={isModalOpen}
				isLoading={isDeleting}
				toggleModal={closeModal}
				message="Are you sure you want to delete this story?"
				onConfirm={handleDelete}
			/>
		</Container>
	);
};

export default FlaggedSubStory;
