import { useCallback, useRef } from "react";
import { toast } from "react-toastify";
import { useUploadGalleryMutation } from "../../../../store/apis/storiesApi";

export const useUploader = ({ onUpload }) => {
  const [uploadFile, { isLoading }] = useUploadGalleryMutation();

  const handleFileUpload = useCallback(
    async (file) => {
      try {
        const response = await uploadFile(file).unwrap(); // Unwrap the mutation response
        if (response.status === "success") {
          const files = response.data.files.map((item) => item.url);
          onUpload(files);
        } else {
          toast.error(response.message || "Error uploading image");
        }
      } catch (error) {
        const errorMessage =
          error?.data?.error || error.message || "Something went wrong";
        console.error(errorMessage);
        toast.error(errorMessage);
      }
    },
    [uploadFile, onUpload]
  );

  return { loading: isLoading, uploadFile: handleFileUpload };
};
