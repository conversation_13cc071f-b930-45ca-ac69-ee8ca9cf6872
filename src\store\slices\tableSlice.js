import { createSlice } from "@reduxjs/toolkit";
import { incrementOffsetState } from "./sharedReducers";

const initialState = {
  tag: [],
  writer: [],
  category: [],
  publishedTime: "",
  showFilters: false,
  showMore: true,
  fetchedTagData: [],
  limit: 20,
  offset: 0,
  addFilter: false,
};

export const tableSlice = createSlice({
  name: "table",
  initialState,
  reducers: {
    // for handling the tag toggle in the filter
    toggleTag: (state, { payload }) => {
      const { name, isChecked } = payload;
      state.tag[name] = isChecked;
    },

    setTags: (state, { payload }) => {
      state.fetchedTagData = payload.tags;
      state.offset = payload.offset || state.offset;
      state.showMore = payload.showMore;
    },

    setTagData: (state, { payload }) => {
      if (!state.tag.includes(payload)) {
        state.tag.push(payload);
      }
    },

    removeTagData: (state, { payload }) => {
      state.tag = state.tag.filter((tag) => tag !== payload);
    },

    // For handling author select changes in ther filter
    setAuthors: <AUTHORS>
      state.writer = payload;
    },

    // For handling category checkbox changes in filter
    toggleCategory: (state, { payload }) => {
      const flagId = payload;
      if (state.category.includes(flagId)) {
        state.category = state.category.filter((id) => id !== flagId);
      } else {
        state.category.push(flagId);
      }
    },

    setPublishedTime: (state, { payload }) => {
      state.publishedTime = payload;
    },
    toggleFilters: (state) => {
      state.showFilters = !state.showFilters;
    },
    setAddFilter: (state, { payload }) => {
      state.addFilter = payload;
    },

    applyLocalFilterState: (state, { payload }) => {
      const { publishedTime, tags, authors, categories } = payload;

      state.publishedTime = publishedTime;
      state.tag = tags;
      state.writer = authors;
      state.category = categories;
    },

    incrementOffset: (state) => incrementOffsetState(state),
    resetTableState: (state) => initialState,
  },
});
// Action creators are generated for each case reducer function
export const {
  toggleTag,
  toggleCategory,
  setAuthors,
  setPublishedTime,
  toggleFilters,
  setTagData,
  setTags,
  removeTagData,
  incrementOffset,
  setAddFilter,
  applyLocalFilterState,
  resetTableState,
} = tableSlice.actions;

export default tableSlice.reducer;
