const MultipleFile = ({
  width = 82,
  height = 98,
  ...rest
}) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={width}
    height={height}
    viewBox={`0 0 ${width} ${height}`}
  >
    <defs>
      <path id="b" d="M12 0v76h60V0z" />
      <filter
        id="a"
        width="128.3%"
        height="122.4%"
        x="-14.2%"
        y="-8.6%"
        filterUnits="objectBoundingBox"
      >
        <feOffset dy="2" in="SourceAlpha" result="shadowOffsetOuter1" />
        <feGaussianBlur
          in="shadowOffsetOuter1"
          result="shadowBlurOuter1"
          stdDeviation="2.5"
        />
        <feColorMatrix
          in="shadowBlurOuter1"
          values="0 0 0 0 0.0862745098 0 0 0 0 0.176470588 0 0 0 0 0.239215686 0 0 0 0.1 0"
        />
      </filter>
      <path id="d" d="M6 6v76h60V6z" />
      <filter
        id="c"
        width="128.3%"
        height="122.4%"
        x="-14.2%"
        y="-8.6%"
        filterUnits="objectBoundingBox"
      >
        <feOffset dy="2" in="SourceAlpha" result="shadowOffsetOuter1" />
        <feGaussianBlur
          in="shadowOffsetOuter1"
          result="shadowBlurOuter1"
          stdDeviation="2.5"
        />
        <feColorMatrix
          in="shadowBlurOuter1"
          values="0 0 0 0 0.0862745098 0 0 0 0 0.176470588 0 0 0 0 0.239215686 0 0 0 0.1 0"
        />
      </filter>
      <path id="f" d="M0 12v76h60V12z" />
      <filter
        id="e"
        width="128.3%"
        height="122.4%"
        x="-14.2%"
        y="-8.6%"
        filterUnits="objectBoundingBox"
      >
        <feOffset dy="2" in="SourceAlpha" result="shadowOffsetOuter1" />
        <feGaussianBlur
          in="shadowOffsetOuter1"
          result="shadowBlurOuter1"
          stdDeviation="2.5"
        />
        <feColorMatrix
          in="shadowBlurOuter1"
          values="0 0 0 0 0.0862745098 0 0 0 0 0.176470588 0 0 0 0 0.239215686 0 0 0 0.1 0"
        />
      </filter>
    </defs>
    <g fill="none" fillRule="evenodd">
      <g fillRule="nonzero" transform="translate(5 3)">
        <use fill="black" filter="url(#a)" xlinkHref="#b" />
        <use fill="white" xlinkHref="#b" />
      </g>
      <g fillRule="nonzero" transform="translate(5 3)">
        <use fill="black" filter="url(#c)" xlinkHref="#d" />
        <use fill="white" xlinkHref="#d" />
      </g>
      <g fillRule="nonzero" transform="translate(5 3)">
        <use fill="black" filter="url(#e)" xlinkHref="#f" />
        <use fill="white" xlinkHref="#f" />
      </g>
      <g stroke="#4EB7F5" strokeLinecap="square" strokeWidth="2">
        <path d="M49 40H21M49 45H21M49 50H21" />
      </g>
    </g>
  </svg>
);

export default MultipleFile;
