// props description:
// className: additional styling to apply apart from the default to the input field
// containerAdjust: additional styling to apply apart from the default to the container input
// label: the text to display for the input
// name: the name of the input
// placeholder: the placeholder text to display in the input
// type: the type of the input
// startIcon: the icon to display at the start of the input
// endIcon: the icon to display at the end of the input
// required: whether the input is required or not
// errorMessage: the error message to display if the input is invalid

import React, { forwardRef, useCallback, useEffect, useState } from "react";
import { useFormContext } from "react-hook-form";
import { CiSearch } from "react-icons/ci";
import { FaChevronDown, FaSearch } from "react-icons/fa";
import { useDispatch } from "react-redux";
import { storyStatus } from "../store/slices/storiesSlice";
import { RxCross2 } from "react-icons/rx";
import { BiPlus } from "react-icons/bi";
import { FiInfo } from "react-icons/fi";
import Tippy from "@tippyjs/react";
import "tippy.js/dist/tippy.css";
import { IoInformationCircleOutline } from "react-icons/io5";
import { status } from "../utils/constants";

export const InputField = ({
	className,
	containerAdjust = null,
	label,
	name,
	placeholder = `Enter the ${label}`,
	type = "text",
	startIcon,
	endIcon,
	required = false,
	errorMessage = null,
}) => {
	const { register } = useFormContext();
	return (
		<div className={`flex flex-col w-full`} style={containerAdjust}>
			<label htmlFor={name} className="mr-2 font-medium  whitespace-nowrap">
				{label}
				{required && <sup className="text-red-500 ml-1">*</sup>}
			</label>
			<div className={`relative inline-block w-full ${className || ""}`}>
				{startIcon && (
					<div className="absolute left-0 top-1/2 transform -translate-y-1/2 flex items-center justify-center w-10 h-full pointer-events-none">
						{startIcon}
					</div>
				)}
				<input
					className={`w-full z-20 block h-10 bg-white border rounded-lg transition-colors duration-150 ease-in-out focus:outline-none focus:border-primary ${
						startIcon ? "pl-10" : ""
					} ${endIcon ? "pr-10" : ""} ${!startIcon && !endIcon ? "pl-2.5" : ""} ${
						errorMessage ? "border-red-500" : ""
					}`}
					{...register(name)}
					type={type}
					placeholder={placeholder}
				/>
				{endIcon && (
					<div className="absolute right-0 top-1/2 transform -translate-y-1/2 flex items-center justify-center w-10 h-full cursor-pointer">
						{endIcon}
					</div>
				)}
			</div>
			<div>&nbsp;</div>
			<div className="text-red-500 text-sm mt-1">{errorMessage ? <>{errorMessage}</> : null}</div>
		</div>
	);
};

// props description:
// className: additional styling to apply apart from the default to the input field
// containerAdjust: additional styling to apply apart from the default to the container input
// label: the text to display for the input
// name: the name of the input
// placeholder: the placeholder text to display in the input
// required: whether the input is required or not
// errorMessage: the error message to display if the input is invalid
// options: the options to display in the dropdown   ex: [{value: 1, label: "Option 1"}, {value: 2, label: "Option 2"}]
export const SelectField = ({
	className,
	containerAdjust = null,
	label,
	name,
	placeholder,
	required = true,
	errorMessage = null,
	options = [],
	...rest
}) => {
	const { register } = useFormContext();
	return (
		<div className="flex flex-col w-full" style={containerAdjust}>
			{label ? (
				<label htmlFor={name} className="mr-2 font-medium whitespace-nowrap">
					{label}
					{required ? <sup className="text-red-500 ml-1">*</sup> : null}
				</label>
			) : null}
			<select
				className={`w-full block h-10 bg-white border rounded-lg transition-colors duration-150 ease-in-out focus:outline-none focus:border-primary px-2.5 ${
					className || ""
				} ${errorMessage ? "border-red-500" : ""}`}
				{...register(name)}
				{...rest}
			>
				{placeholder ? <option value="">{placeholder}</option> : null}
				{options.map((option) => (
					<option key={option.value} value={option.value}>
						{option.label}
					</option>
				))}
			</select>
			<div>&nbsp;</div>
			{errorMessage ? <div className="text-red-500 text-sm mt-1">{errorMessage}</div> : null}
		</div>
	);
};
export function DebouncedInput({
	value: initialValue,
	onChange,
	debounce = 500,
	customClass = "",
	inputClass = "",
	...props
}) {
	const [value, setValue] = useState(initialValue);

	// Only update internal state when the initial value changes
	useEffect(() => {
		setValue(initialValue);
	}, [initialValue]);

	useEffect(() => {
		const timeout = setTimeout(() => {
			if (value !== initialValue) {
				onChange(value);
			}
		}, debounce);

		return () => clearTimeout(timeout);
	}, [value, onChange, debounce, initialValue]);

	return (
		<div className={`relative w-auto ${customClass}`}>
			<span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">
				<CiSearch />
			</span>
			<input
				{...props}
				className={`pl-10 w-full pr-3 h-8 border border-[#ccd0d4] outline-none focus:outline-none focus:border-primary rounded-full ${inputClass}`}
				value={value}
				onChange={(e) => setValue(e.target.value)}
			/>
		</div>
	);
}

export function SearchInput({
	value: initialValue,
	onChange,
	customClass = "",
	inputClass = "",
	...props
}) {
	const [value, setValue] = useState(initialValue);

	useEffect(() => {
		setValue(initialValue);
	}, [initialValue]);

	return (
		<div className={`relative w-auto ${customClass}`}>
			<span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">
				<CiSearch />
			</span>
			<input
				{...props}
				className={`pl-10 w-full pr-3 h-8 border border-[#ccd0d4] outline-none focus:outline-none focus:border-primary rounded-full ${inputClass}`}
				value={value}
				onChange={(e) => setValue(e.target.value)}
				onKeyDown={(e) => (e.key === "Enter" ? onChange(value) : null)}
			/>
		</div>
	);
}
export function DropdownButton({ selected = 1, handleChange = () => {} }) {
	const [isOpen, setIsOpen] = useState(false);
	const [dropdownValue, setDropdownValue] = useState(selected);
	const dispatch = useDispatch();

	const toggleDropdown = useCallback(
		(e) => {
			e.stopPropagation(); // Prevent bubbling
			setIsOpen((prev) => !prev);
		},
		[isOpen]
	);

	const handleButtonClick = (value) => {
		dispatch(storyStatus(value));
		setDropdownValue(value);
		setIsOpen(false);
		handleChange(value);
	};

	const buttonStates = [
		{ value: 1, label: "Published", bgColor: "#e6f4ff", color: "#3784f6" },
		{ value: 0, label: "Unpublished", bgColor: "#ffeff0", color: "#ff4d4f" },
		{ value: 4, label: "Scheduled", bgColor: "#FFB563", color: "#fff" },
		{ value: 3, label: "Draft", bgColor: "#dcdcdc", color: "#646464" },
	];

	const selectedButton = buttonStates.find((button) => button.value === dropdownValue);

	return (
		<div
			className="relative inline-block text-left"
			onClick={(e) => e.stopPropagation()} // Prevent bubbling to parent
		>
			<button
				onClick={toggleDropdown}
				style={{
					backgroundColor: selectedButton?.bgColor || "#e6f4ff",
					color: selectedButton?.color || "black",
				}}
				className="border-none text-white focus:outline-none font-medium rounded-md text-[12px] px-3 py-1 text-center inline-flex items-center w-32 justify-between"
				type="button"
			>
				{selectedButton?.label || "Please Select"}
				<FaChevronDown className="w-3 h-3 ml-2" color={selectedButton?.color} />
			</button>

			{isOpen && (
				<div
					className="absolute top-5 transition-all duration-200 z-10 mt-2 bg-white divide-y divide-gray-100 rounded-lg shadow w-44"
					onMouseLeave={() => setIsOpen(false)} // Close on mouse leave
				>
					<div className="py-2 text-sm text-gray-700">
						{buttonStates.map((button) => (
							<button
								key={button.value}
								onClick={() => handleButtonClick(button.value)}
								className="flex items-center px-4 py-2 hover:bg-gray-100 w-full outline-none focus:outline-none border-b"
							>
								<span
									style={{ backgroundColor: button?.color || "transparent" }}
									className="w-5 h-2.5 mr-2"
								></span>
								{button.label}
							</button>
						))}
					</div>
				</div>
			)}
		</div>
	);
}

export const CheckboxButton = ({ name, onChange, checked }) => {
	return (
		<label
			className={`inline-flex items-center px-3 py-1 rounded-full font-medium text-sm border  cursor-pointer ${
				checked ? "bg-primary border-primary" : "border-gray-500"
			}`}
		>
			<input
				type="checkbox"
				className="hidden"
				checked={checked || false}
				onChange={(e) => onChange(name, e.target.checked)}
			/>
			<span className={` ${checked ? "text-white" : "text-black"}`}>{name}</span>
			{checked ? (
				<span className="ml-2 font-bold text-[16px]">
					<RxCross2 className={checked ? "text-white" : "text-gray-700"} />
				</span>
			) : (
				<BiPlus className="ml-2 text-lg text-gray-600" />
			)}
		</label>
	);
};
export const Input = forwardRef(
	(
		{
			label,
			type = "text",
			customClass = "",
			placeholder = "",
			required = false,
			readOnly = false,
			id,
			value,
			isSlug = false,
			onDebouncedChange,
			onEnter,
			toolTip = null,
			extraArg = null,
			isTextarea = false,
			rows = 4,
			...rest
		},
		ref
	) => {
		const handleChange = (e) => {
			const newValue = e.target.value;
			onDebouncedChange(newValue); // Call the debounced callback with the new value
		};

		const handleKeyDown = (e) => {
			if (e.key === "Enter" && onEnter) {
				e.preventDefault(); // Prevent form submission
				onEnter(value); // Trigger onEnter callback
			}
		};

		return (
			<div className="flex flex-col gap-y-1 relative">
				<div className="flex items-center justify-between w-full">
					{label && (
						<label htmlFor={id} className="text-sm text-gray-600 mb-1 flex items-center">
							{label}
							{toolTip && (
								<Tippy content={toolTip} placement="right" trigger="click">
									<button type="button" className="ml-1 text-gray-400 hover:text-gray-600">
										<IoInformationCircleOutline className="text-lg text-primary" />
									</button>
								</Tippy>
							)}
							{required ? <sup className="text-red-500 text-base pt-1">*</sup> : null}
						</label>
					)}
					{extraArg && <div className="text-xs text-gray-400 mb-1">{extraArg}</div>}
				</div>

				{isTextarea ? (
					<textarea
						{...rest}
						id={id}
						ref={ref}
						value={value || ""} // Set value from Redux store
						onChange={handleChange}
						onKeyDown={handleKeyDown}
						placeholder={placeholder}
						required={required}
						rows={rows}
						className={`border text-sm border-gray-300 rounded-md px-4 py-2 w-full focus:outline-none focus:border-blue-500 ${customClass}`}
					/>
				) : (
					<div className="relative">
						<input
							{...rest}
							id={id}
							ref={ref}
							type={type}
							value={value || ""}
							onChange={handleChange}
							onKeyDown={handleKeyDown}
							placeholder={placeholder}
							required={required}
							readOnly={readOnly}
							className={`border text-sm border-gray-300 rounded-md px-4 py-2 w-full focus:outline-none focus:border-blue-500 ${customClass}`}
						/>
						{isSlug ? (
							<span className="absolute left-2 top-1/2 text-xl text-gray-400 -translate-y-1/2">
								/
							</span>
						) : null}
					</div>
				)}
			</div>
		);
	}
);

export const DropdownMenu = ({ isOpen, onSelect, trigger, children }) => {
	return (
		<div className="relative">
			{trigger}
			{isOpen && (
				<div className="absolute z-50 bg-white border border-gray-200 shadow-md rounded-md">
					{React.Children.map(children, (child) => {
						return React.cloneElement(child, {
							onClick: () => onSelect(child.props.value),
						});
					})}
				</div>
			)}
		</div>
	);
};
