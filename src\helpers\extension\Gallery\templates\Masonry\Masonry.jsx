import React from "react";
import PropTypes from "prop-types";

const Masonry = ({
  data = [],
  properties = {
    orientation: "horizontal", // or "vertical"
    height: 300,
    spacing: 5,
    columns: 3,
    ratio: "1/1", // Default aspect ratio
  },
}) => {
  const {
    orientation = "horizontal",
    height,
    spacing = 5,
    columns = 3,
    ratio = "1/1",
  } = properties;

  const isHorizontal = orientation === "horizontal";

  return (
    <div
      className={`w-full ${isHorizontal ? "flex flex-wrap" : "grid"}`}
      style={
        isHorizontal
          ? { gap: `${spacing}px` }
          : {
              gridTemplateColumns: `repeat(${columns}, 1fr)`,
              gap: `${spacing}px`,
            }
      }
    >
      {data?.map((item, index) => {
        return (
          <div
            key={`masonry-item-${index}`}
            className={`relative overflow-hidden ${
              isHorizontal ? "flex-1" : "w-full"
            }`}
            style={
              isHorizontal
                ? {
                    aspectRatio: ratio,
                    flexBasis: `calc(${100 / columns}% - ${spacing}px)`,
                  }
                : {
                    height: height,
                  }
            }
          >
            <img
              className="absolute inset-0 w-full h-full object-cover"
              src={item?.src || ""}
              alt={item?.alt || `Masonry Image ${index}`}
              onError={(e) => (e.target.style.display = "none")}
            />
          </div>
        );
      })}
    </div>
  );
};

Masonry.propTypes = {
  data: PropTypes.arrayOf(
    PropTypes.shape({
      src: PropTypes.string.isRequired,
      alt: PropTypes.string,
    })
  ),
  properties: PropTypes.shape({
    orientation: PropTypes.oneOf(["horizontal", "vertical"]),
    height: PropTypes.number,
    spacing: PropTypes.number,
    columns: PropTypes.number,
    ratio: PropTypes.string,
  }),
};

export default Masonry;
