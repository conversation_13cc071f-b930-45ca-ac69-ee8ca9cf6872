import { useState, useCallback } from "react";

export const useUploadProgress = () => {
	const [uploads, setUploads] = useState([]);
	const [isVisible, setIsVisible] = useState(false);

	// Add new upload to the list (clears previous uploads first)
	const addUpload = useCallback((files) => {
		const newUploads = files.map((file, index) => ({
			id: `${Date.now()}-${index}`,
			fileName: file.name,
			fileSize: file.size,
			progress: 0,
			status: "uploading", // 'uploading', 'completed', 'error'
			error: null,
			file: file,
		}));

		// Clear previous uploads and set only new ones
		setUploads(newUploads);
		setIsVisible(true);
		return newUploads;
	}, []);

	// Update upload progress
	const updateUploadProgress = useCallback(
		(uploadId, progress, status = "uploading", error = null) => {
			setUploads((prev) =>
				prev.map((upload) =>
					upload.id === uploadId ? { ...upload, progress, status, error } : upload
				)
			);
		},
		[]
	);

	// Update multiple uploads at once (for batch operations)
	const updateMultipleUploads = useCallback((updates) => {
		setUploads((prev) =>
			prev.map((upload) => {
				const update = updates.find((u) => u.id === upload.id);
				return update ? { ...upload, ...update } : upload;
			})
		);
	}, []);

	// Remove upload from list
	const removeUpload = useCallback((uploadId) => {
		setUploads((prev) => prev.filter((upload) => upload.id !== uploadId));
	}, []);

	// Clear all uploads
	const clearUploads = useCallback(() => {
		setUploads([]);
		setIsVisible(false);
	}, []);

	// Hide progress UI
	const hideProgress = useCallback(() => {
		setIsVisible(false);
	}, []);

	// Show progress UI
	const showProgress = useCallback(() => {
		setIsVisible(true);
	}, []);

	// Retry failed upload
	const retryUpload = useCallback((uploadId) => {
		setUploads((prev) =>
			prev.map((upload) =>
				upload.id === uploadId
					? { ...upload, progress: 0, status: "uploading", error: null }
					: upload
			)
		);
	}, []);

	// Get upload by ID
	const getUpload = useCallback(
		(uploadId) => {
			return uploads.find((upload) => upload.id === uploadId);
		},
		[uploads]
	);

	// Check if all uploads are complete
	const areAllUploadsComplete = useCallback(() => {
		return (
			uploads.length > 0 &&
			uploads.every((upload) => upload.status === "completed" || upload.status === "error")
		);
	}, [uploads]);

	// Get upload statistics
	const getUploadStats = useCallback(() => {
		const total = uploads.length;
		const completed = uploads.filter((u) => u.status === "completed").length;
		const failed = uploads.filter((u) => u.status === "error").length;
		const uploading = uploads.filter((u) => u.status === "uploading").length;

		return { total, completed, failed, uploading };
	}, [uploads]);

	return {
		uploads,
		isVisible,
		addUpload,
		updateUploadProgress,
		updateMultipleUploads,
		removeUpload,
		clearUploads,
		hideProgress,
		showProgress,
		retryUpload,
		getUpload,
		areAllUploadsComplete,
		getUploadStats,
	};
};
