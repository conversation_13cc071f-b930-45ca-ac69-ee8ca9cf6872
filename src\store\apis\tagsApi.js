import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import { apiEndpoints } from "../../utils/constants";
import { baseQuery } from "./baseConfig";

// Define a service using a base URL and expected endpoints

const baseUrl = import.meta.env.VITE_API_URL;
export const tagsApi = createApi({
  reducerPath: "tagsApi",
  baseQuery: baseQuery,
  tagTypes: ["Tags"],
  endpoints: (builder) => ({
    getTagsList: builder.query({
      query: ({ search, limit, offset }) => ({
        url: `${apiEndpoints.getTags}`,
        params: { search, limit, offset },
      }),
      providesTags: ["Tags"],
      transformResponse: (response) => {
        return {
          count: response.totalCounts,
          data: response.data,
        };
      },
      // Only have one cache entry because the arg always maps to one string
      serializeQueryArgs: ({ endpointName, queryArgs }) => {
        // Include search in the cache key so different searches have different caches
        return `${endpointName}-${queryArgs.search}`;
      },
      merge: (currentCache, newItems, { arg: { offset } }) => {
        // If offset is 0, it means either:
        // 1. This is the first request
        // 2. Search term has changed (since you'll reset offset to 0)
        // In either case, we want to replace the cache
        if (offset === 0) {
          return newItems;
        }
        // Otherwise, merge the new items with existing cache
        currentCache.data.push(...newItems.data);
        return currentCache;
      },
      forceRefetch({ currentArg, previousArg }) {
        // Refetch if any of the arguments change
        return (
          currentArg.search !== previousArg?.search ||
          currentArg.offset !== previousArg?.offset ||
          currentArg.limit !== previousArg?.limit
        );
      },
    }),

    createTag: builder.mutation({
      query: (data) => ({
        url: apiEndpoints.postTag,
        method: "POST",
        body: data,
      }),
      invalidatesTags: ["Tags"],
    }),
    deleteTag: builder.mutation({
      query: (id) => ({
        url: `${apiEndpoints.deleteTag}/${id}`,
        method: "PATCH",
      }),
      invalidatesTags: ["Tags"],
    }),

    updateTag: builder.mutation({
      query: ({ data, id }) => ({
        url: `${apiEndpoints.patchTag}/${id}`,
        method: "PATCH",
        body: data,
      }),
      invalidatesTags: ["Tags"],
    }),
    getTag: builder.query({
      query: (id) => ({
        url: `${apiEndpoints.getEditTag}/${id}`,
      }),
      transformResponse: (response) => response.data,
      providesTags: ["Tags"],
    }),
    getMetaDataTag: builder.query({
      query: () => ({
        url: `${apiEndpoints.getMetaDataTemplate}`,
        params: { modelName: "Tags" },
      }),
      transformResponse: (response) => response.data[0],
    }),
  }),
});

// Export hooks for usage in functional components, which are
// auto-generated based on the defined endpoints
export const {
  useGetTagsListQuery,
  useGetTagQuery,
  useGetMetaDataTagQuery,
  useDeleteTagMutation,
  useUpdateTagMutation,
  useCreateTagMutation,
} = tagsApi;
