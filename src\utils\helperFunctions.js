// used to get the nested selected categoried data
export const getSelectedValues = (data) => {
  const selectedValues = [];
  Object.keys(data).forEach((category) => {
    Object.keys(data[category]).forEach((optionValue) => {
      if (data[category][optionValue]) {
        selectedValues.push(optionValue);
      }
    });
  });
  return selectedValues;
};


export const cleanFilterPayload = (filter) => {
  const cleanedFilter = { ...filter };

  // Specific handling for each field
  if (cleanedFilter.status === null) delete cleanedFilter.status;
  if (cleanedFilter.search === "") delete cleanedFilter.search;

  // Keep limit and offset regardless of value
  return cleanedFilter;
};


export const generateSlugStories = (title) => {
  let slug = title
    .toString() // Convert to string
    .toLowerCase() // Convert to lowercase
    .trim() // Trim leading/trailing whitespace
    .replace(/\s+/g, "-") // Replace spaces with hyphens
    .replace(/[^\w-]+/g, "") // Remove all non-word characters
    .replace(/--+/g, "-"); // Replace multiple hyphens with a single hyphen

  return slug;
};

export const cleanFilters = (filter) => {
  const { search, ...rest } = filter; // Destructure to extract search
  return search !== "" ? filter : rest; // Include `search` only if it is not empty
};

export const preparePayload = (state) => {
  return Object.entries(state).reduce((payload, [key, value]) => {
    // Add to payload only if the value is not empty
    if (Array.isArray(value) && value.length > 0) {
      payload[key] = value; // Non-empty array
    } else if (typeof value === "string" && value.trim() !== "") {
      payload[key] = value; // Non-empty string
    }
    return payload;
  }, {});
};

export const formatDateAndTime = (isoString) => {
  const date = new Date(isoString);
  const now = new Date();

  const months = [
    "Jan",
    "Feb",
    "Mar",
    "Apr",
    "May",
    "Jun",
    "Jul",
    "Aug",
    "Sep",
    "Oct",
    "Nov",
    "Dec",
  ];
  const formattedDate = `${
    months[date.getUTCMonth()]
  } ${date.getUTCDate()}, ${date.getUTCFullYear()}`;

  const diffInMilliseconds = now - date;
  const diffInSeconds = Math.floor(diffInMilliseconds / 1000);
  const diffInMinutes = Math.floor(diffInSeconds / 60);
  const diffInHours = Math.floor(diffInMinutes / 60);
  const diffInDays = Math.floor(diffInHours / 24);

  if (diffInDays >= 10) {
    return formattedDate;
  } else {
    let timeDifference;
    if (diffInDays >= 1) {
      timeDifference = `${diffInDays} days ago`;
    } else if (diffInHours >= 1) {
      timeDifference = `${diffInHours} hours ago`;
    } else if (diffInMinutes >= 1) {
      timeDifference = `${diffInMinutes} minutes ago`;
    } else {
      timeDifference = `${diffInSeconds} seconds ago`;
    }
    return timeDifference;
  }
};

// Function to convert base64 to Blob
export function base64ToBlob(base64String) {
  // Split the base64 string into data and contentType
  const parts = base64String.split(";base64,");
  const contentType = parts[0].split(":")[1] || "image/jpeg";
  const raw = window.atob(parts[1]);

  // Convert to ArrayBuffer
  const rawLength = raw.length;
  const array = new Uint8Array(new ArrayBuffer(rawLength));

  for (let i = 0; i < rawLength; i++) {
    array[i] = raw.charCodeAt(i);
  }

  return new Blob([array], { type: contentType });
}
export function base64ToFile(base64, fileName) {
  // Extract the Base64 data and MIME type
  const [prefix, base64Data] = base64.split(",");
  const mimeType = prefix.match(/:(.*?);/)[1];

  // Decode the Base64 string
  const byteCharacters = atob(base64Data);
  const byteNumbers = new Array(byteCharacters.length);
  for (let i = 0; i < byteCharacters.length; i++) {
    byteNumbers[i] = byteCharacters.charCodeAt(i);
  }

  const byteArray = new Uint8Array(byteNumbers);

  // Create a Blob
  const blob = new Blob([byteArray], { type: mimeType });

  // Convert Blob to File (optional)
  return new File([blob], fileName, { type: mimeType });
}

export const handleViewClickInNewTab = (viewLink) => {
  window.open(viewLink, "_blank", "noopener,noreferrer");
};

export const formatDateTime = (isoString) => {
  const date = new Date(isoString);

  // Format date as "Month Day, Year"
  const dateStr = date.toLocaleDateString("en-US", {
    month: "long",
    day: "numeric",
  });

  // Format time as "HH:MM AM/PM"
  const timeStr = date.toLocaleTimeString("en-US", {
    hour: "numeric",
    minute: "2-digit",
    hour12: true,
  });

  return `for ${dateStr} at ${timeStr}`;
};

export const formatTagTitle = (tagName, title) => {
  const processedTitle = title.replace(/\[Tag Name\]/g, tagName);
  return processedTitle.trim();
};

export const formatAuthorTitle = (authorName, title) => {
  return title.replace(/\[Author Name\]/g, authorName).trim();
};
  