import { useCallback } from "react";
import { useDispatch } from "react-redux";
import { toast } from "react-toastify";
import {
	useDeleteMediaMutation,
	useUpdateMediaMutation,
	useGetMediaLibraryListMutation,
} from "../../../store/apis/mediaLibraryApi";
import {
	setMedia,
	appendMedia,
	setMediaPagination,
	toggleImageSelection,
} from "../../../store/slices/mediaLibrarySlice";

/**
 * Hook for media-specific operations
 * Handles media deletion, updating, and selection
 */
export const useMediaOperations = () => {
	const dispatch = useDispatch();
	
	// API hooks
	const [deleteMedia, { isLoading: isDeletingMedia }] = useDeleteMediaMutation();
	const [updateMedia, { isLoading: isUpdatingMedia }] = useUpdateMediaMutation();
	const [getMediaLibraryList] = useGetMediaLibraryListMutation();
	
	/**
	 * Delete a media item
	 */
	const handleDeleteMedia = useCallback(async (mediaId, currentFolder = null) => {
		try {
			const response = await deleteMedia(mediaId).unwrap();
			
			if (response.status === "success") {
				toast.success("Media deleted successfully");
				
				// Refresh media list
				await refreshMediaList(currentFolder);
				
				return true;
			} else {
				throw new Error(response.message || "Failed to delete media");
			}
		} catch (error) {
			console.error("Error deleting media:", error);
			toast.error(error.message || "Failed to delete media");
			throw error;
		}
	}, [deleteMedia]);
	
	/**
	 * Update media item
	 */
	const handleUpdateMedia = useCallback(async (mediaId, updateData) => {
		try {
			const response = await updateMedia({ mediaId, ...updateData }).unwrap();
			
			if (response.status === "success") {
				toast.success("Media updated successfully");
				return response.data;
			} else {
				throw new Error(response.message || "Failed to update media");
			}
		} catch (error) {
			console.error("Error updating media:", error);
			toast.error(error.message || "Failed to update media");
			throw error;
		}
	}, [updateMedia]);
	
	/**
	 * Refresh media list
	 */
	const refreshMediaList = useCallback(async (currentFolder = null, search = "") => {
		try {
			const response = await getMediaLibraryList({
				search: search || "",
				folder: currentFolder || "",
				includeSubfolders: false,
				limit: 20,
				offset: 0,
			}).unwrap();
			
			if (response.status === "success") {
				dispatch(setMedia(response.data || []));
				dispatch(setMediaPagination({
					offset: 20,
					hasMore: (response.data || []).length === 20,
					totalCount: response.totalCounts || 0,
				}));
			}
		} catch (error) {
			console.error("Error refreshing media:", error);
			toast.error("Failed to refresh media");
		}
	}, [dispatch, getMediaLibraryList]);
	
	/**
	 * Load more media items
	 */
	const loadMoreMedia = useCallback(async (currentFolder = null, search = "", currentOffset = 0, limit = 20) => {
		try {
			const response = await getMediaLibraryList({
				search: search || "",
				folder: currentFolder || "",
				includeSubfolders: false,
				limit: limit,
				offset: currentOffset,
			}).unwrap();
			
			if (response.status === "success") {
				const newMedia = response.data || [];
				dispatch(appendMedia(newMedia));
				dispatch(setMediaPagination({
					offset: currentOffset + limit,
					hasMore: newMedia.length === limit,
					totalCount: response.totalCounts || 0,
				}));
				
				return newMedia;
			}
		} catch (error) {
			console.error("Error loading more media:", error);
			toast.error("Failed to load more media");
			dispatch(setMediaPagination({ hasMore: false }));
			return [];
		}
	}, [dispatch, getMediaLibraryList]);
	
	/**
	 * Handle media selection
	 */
	const handleMediaSelect = useCallback((mediaItem) => {
		dispatch(toggleImageSelection({ mediaId: mediaItem._id, mediaItem }));
	}, [dispatch]);
	
	/**
	 * Bulk delete media items
	 */
	const handleBulkDeleteMedia = useCallback(async (mediaIds, currentFolder = null) => {
		const deletePromises = mediaIds.map(id => deleteMedia(id).unwrap());
		
		try {
			await Promise.all(deletePromises);
			toast.success(`Successfully deleted ${mediaIds.length} media items`);
			
			// Refresh media list
			await refreshMediaList(currentFolder);
			
			return true;
		} catch (error) {
			console.error("Error in bulk delete:", error);
			toast.error("Some media items could not be deleted");
			
			// Still refresh to show current state
			await refreshMediaList(currentFolder);
			
			return false;
		}
	}, [deleteMedia, refreshMediaList]);
	
	/**
	 * Update media tags
	 */
	const handleUpdateMediaTags = useCallback(async (mediaId, tags) => {
		try {
			const response = await updateMedia({
				mediaId,
				tags: tags,
			}).unwrap();
			
			if (response.status === "success") {
				toast.success("Media tags updated successfully");
				return response.data;
			} else {
				throw new Error(response.message || "Failed to update media tags");
			}
		} catch (error) {
			console.error("Error updating media tags:", error);
			toast.error(error.message || "Failed to update media tags");
			throw error;
		}
	}, [updateMedia]);
	
	/**
	 * Update media metadata (alt text, caption, etc.)
	 */
	const handleUpdateMediaMetadata = useCallback(async (mediaId, metadata) => {
		try {
			const response = await updateMedia({
				mediaId,
				...metadata,
			}).unwrap();
			
			if (response.status === "success") {
				toast.success("Media metadata updated successfully");
				return response.data;
			} else {
				throw new Error(response.message || "Failed to update media metadata");
			}
		} catch (error) {
			console.error("Error updating media metadata:", error);
			toast.error(error.message || "Failed to update media metadata");
			throw error;
		}
	}, [updateMedia]);
	
	/**
	 * Get media file info
	 */
	const getMediaFileInfo = useCallback(async (mediaUrl) => {
		try {
			const response = await fetch(mediaUrl, { method: "HEAD" });
			const contentType = response.headers.get("Content-Type");
			const contentLength = response.headers.get("Content-Length");
			const lastModified = response.headers.get("Last-Modified");
			
			const urlParts = mediaUrl.split("/");
			const fileName = urlParts[urlParts.length - 1];
			
			// Get image dimensions if it's an image
			let dimensions = {};
			if (contentType && contentType.startsWith("image/")) {
				dimensions = await getImageDimensions(mediaUrl);
			}
			
			return {
				name: fileName,
				type: contentType || "Unknown",
				size: contentLength ? formatBytes(+contentLength) : null,
				date: lastModified ? new Date(lastModified).toLocaleDateString() : "",
				dimensions: dimensions,
			};
		} catch (error) {
			console.error("Failed to get media file info:", error);
			return null;
		}
	}, []);
	
	/**
	 * Get image dimensions
	 */
	const getImageDimensions = useCallback((src) => {
		return new Promise((resolve) => {
			const img = new Image();
			img.onload = () => {
				resolve({
					width: img.naturalWidth,
					height: img.naturalHeight,
				});
			};
			img.onerror = () => {
				resolve({});
			};
			img.src = src;
		});
	}, []);
	
	/**
	 * Format bytes to human readable format
	 */
	const formatBytes = useCallback((bytes, decimals = 2) => {
		if (bytes === 0) return "0 Bytes";
		
		const k = 1024;
		const dm = decimals < 0 ? 0 : decimals;
		const sizes = ["Bytes", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"];
		
		const i = Math.floor(Math.log(bytes) / Math.log(k));
		
		return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + " " + sizes[i];
	}, []);
	
	return {
		// Loading states
		isDeletingMedia,
		isUpdatingMedia,
		
		// Actions
		handleDeleteMedia,
		handleUpdateMedia,
		refreshMediaList,
		loadMoreMedia,
		handleMediaSelect,
		handleBulkDeleteMedia,
		handleUpdateMediaTags,
		handleUpdateMediaMetadata,
		
		// Utility functions
		getMediaFileInfo,
		getImageDimensions,
		formatBytes,
	};
};
