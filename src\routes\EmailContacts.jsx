import React from "react";
import Container from "../parts/Container";
import BreadCrumb from "../parts/BreadCrumb";
import Button, { RoundedIconsButton } from "../parts/Button";
import { BiPlus } from "react-icons/bi";
import Table from "../parts/Table";
import { useDispatch, useSelector } from "react-redux";
import Filters from "../parts/Filters";
// import { BsEye } from "react-icons/bs";
import { FiEdit } from "react-icons/fi";
import { FaRegTrashAlt } from "react-icons/fa";
import { useNavigate, useSearchParams } from "react-router-dom";
import { toast } from "react-toastify";
import ConfirmationModal from "../parts/ConfirmationModal";
import useConfirmationModal from "../utils/useConfirmationModal";
import { formatDateAndTime } from "../utils/helperFunctions";
import {
  incrementOffset,
  setSelectedContacts,
} from "../store/slices/emailMarketingSlice";
import {
  useDeleteEmailContactMutation,
  useGetEmailContactsQuery,
} from "../store/apis/emailMarketingApi";
import Loader from "../parts/Loader";

const EmailContacts = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { isModalOpen, rowIdToDelete, openModal, closeModal } =
    useConfirmationModal();

  const {
    contacts: { offset, limit, filter },
  } = useSelector((state) => state.emailMarketing);

  const [deleteEmailContact, { isLoading: isDeleting }] =
    useDeleteEmailContactMutation();

  const { data, isLoading, isError, isFetching, error } =
    useGetEmailContactsQuery({
      search: filter.search,
      filter,
      limit,
      offset,
      status: filter.status,
    });

  // Handle error
  if (isError) {
    if (error?.status === 401) {
      toast.error("Session Expired", { toastId: "email_contacts_fetch_error" });
      navigate("/signin");
    }
  }
  // Increase the offset when user has scrolled to the bottom (dummy implementation)
  const fetchMoreData = () => {
    if (
      data?.data?.length > 0 && // Check if `data.data` has items
      !isFetching && // Ensure no ongoing fetch
      data?.data?.length < data?.count // Check if there are more items to fetch
    ) {
      dispatch(incrementOffset());
    }
    console.log(
      "fetchMoreData called - would load more data in real implementation"
    );
  };

  // Handle delete (dummy implementation)
  const handleDelete = () => {
    deleteEmailContact(rowIdToDelete)
      .then((res) => {
        if (res?.data?.status === "success") {
          toast.success(res?.data?.message ?? "Contact deleted successfully!");
        } else {
          toast.error("Failed to delete contact.");
        }
      })
      .catch((err) => {
        toast.error("Failed to delete contact.");
        console.error(err);
      })
      .finally(() => {
        closeModal();
      });
  };

  // Table columns
  const columns = [
    {
      accessorKey: "email",
      id: "email",
      size: 250,
      header: () => "Email",
      cell: ({ row }) => {
        return (
          <div className="flex items-center gap-x-2 font-semibold">
            <div>
              <div className="line-clamp-2">{row.original.email}</div>
              <div className="text-[#969696] text-xs font-normal flex items-center space-x-2">
                <span>{formatDateAndTime(row.original?.createdAt)}</span>
              </div>
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: "location",
      header: () => "Location",
      size: 150,
      minSize: 100,
      maxSize: 200,
      cell: ({ row }) => (
        <div className="line-clamp-3">{row.original?.state || "None"}</div>
      ),
    },
    {
      accessorKey: "groups",
      header: () => "Groups",
      size: 150,
      minSize: 100,
      maxSize: 200,
      cell: ({ row }) => (
        <div className="line-clamp-3">
          {row.original.groups?.map((group) => group.name).join(", ") || "None"}
        </div>
      ),
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => (
        <div
          className={`px-2 py-1 max-w-32 rounded-full text-center text-xs ${
            row.original.status === "active"
              ? "bg-green-100 text-green-800"
              : "bg-red-100 text-red-800"
          }`}
        >
          {row.original.status === "active" ? "Active" : "Unsubscribed"}
        </div>
      ),
    },
    {
      accessorKey: "actions",
      header: "Action",
      cell: ({ row }) => {
        return (
          <div className="flex items-center gap-x-2">
            <RoundedIconsButton
              onClick={() =>
                navigate(`/admin/email/contacts/edit/${row.original._id}`, {
                  state: row.original,
                })
              }
            >
              <FiEdit className="h-[15px] w-[15px]" />
            </RoundedIconsButton>

            <RoundedIconsButton
              onClick={() => {
                openModal(row.original._id);
              }}
            >
              <FaRegTrashAlt className="h-[15px] w-[15px]" />
            </RoundedIconsButton>
          </div>
        );
      },
    },
  ];

  // Handle row selection
  const handleRowSelectionChange = (rowIds) => {
    dispatch(setSelectedContacts(rowIds.selectedIds || []));
  };

  if (isDeleting) return <Loader />;
  return (
    <Container>
      <div className="lg:flex items-center justify-between mb-5">
        <div>
          <BreadCrumb
            title={"Email Contacts"}
            description={
              "Manage your newsletter subscribers and email contacts."
            }
          />
        </div>
        <div className="flex items-center gap-2">
          <Button
            rounded="full"
            size="sm"
            variant="secondary"
            customClasses="mt-2 lg:mt-0 pb-1.5 pt-1.5 bg-transparent"
            onClick={() => navigate("/admin/email/groups")}
          >
            <BiPlus /> <span>Manage Groups</span>
          </Button>
          <Button
            rounded="full"
            size="sm"
            customClasses="mt-2 lg:mt-0 pb-1.5 pt-1.5"
            onClick={() => navigate("/admin/email/contacts/create")}
          >
            <BiPlus /> <span>Add Contact</span>
          </Button>
        </div>
      </div>
      <Table
        module="emailContacts"
        data={data ? data.data : []} // Use filtered data instead of raw data
        isFetching={isFetching}
        actionColumn={5}
        isLoading={isLoading}
        columns={columns}
        enableRowSelection={true}
        enableMultiRowSelection={true}
        handleRowSelectionChange={handleRowSelectionChange}
        fetchMoreData={fetchMoreData}
        customClass={""}
      />
      <Filters />
      <ConfirmationModal
        isOpen={isModalOpen}
        isLoading={isDeleting}
        toggleModal={closeModal}
        message="Are you sure you want to delete this contact?"
        onConfirm={handleDelete}
      />
    </Container>
  );
};

export default EmailContacts;
