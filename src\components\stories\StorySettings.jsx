import React, { useState } from "react";
import BasicSettings from "./BasicSettings";
import AdvancedSettings from "./AdvancedSettings";

const tabs = [
  { label: "Basics", value: "basics" },
  { label: "Advanced", value: "advanced" },
];
const StorySettings = () => {
  const [active, setActive] = useState("basics");
  // Click handler to set the active state and make API call
  const handleTabClick = (tab) => {
    setActive(tab);
  };

  return (
    <div className="mb-5 py-5 ">
      <div className="grid grid-cols-2 -mb-px w-full border-b">
        {tabs.map((tab) => (
          <div key={tab.value} className="me-2">
            <button
              onClick={() => handleTabClick(tab.value)}
              className={`inline-block w-full px-7 h-10 border-b-2 rounded-t-lg hover:text-gray-600 transition-all duration-300 ${
                active === tab.value
                  ? "text-primary border-primary"
                  : "border-transparent"
              }`}
            >
              <h3>{tab.label}</h3>
            </button>
          </div>
        ))}
      </div>
      <div className="mt-4 px-2 scrollbar">
        {active === "basics" && <BasicSettings />}
        {active === "advanced" && <AdvancedSettings />}
      </div>
    </div>
  );
};

export default StorySettings;
