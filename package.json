{"name": "bms-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@dnd-kit/core": "^6.1.0", "@dnd-kit/modifiers": "^7.0.0", "@dnd-kit/sortable": "^8.0.0", "@dnd-kit/utilities": "^3.2.2", "@headlessui/react": "^2.2.2", "@reduxjs/toolkit": "^2.3.0", "@tanstack/react-table": "^8.20.5", "@tippyjs/react": "^4.2.6", "@tiptap/extension-character-count": "^2.14.0", "@tiptap/extension-color": "^2.5.8", "@tiptap/extension-floating-menu": "^2.10.2", "@tiptap/extension-focus": "^2.6.5", "@tiptap/extension-gapcursor": "^2.6.5", "@tiptap/extension-heading": "^2.7.4", "@tiptap/extension-highlight": "^2.5.9", "@tiptap/extension-horizontal-rule": "^2.6.5", "@tiptap/extension-image": "^2.6.0", "@tiptap/extension-link": "^2.7.4", "@tiptap/extension-placeholder": "^2.12.0", "@tiptap/extension-table": "^2.6.5", "@tiptap/extension-table-header": "^2.6.5", "@tiptap/extension-table-row": "^2.6.5", "@tiptap/extension-text-align": "^2.5.9", "@tiptap/extension-text-style": "^2.5.8", "@tiptap/extension-underline": "^2.5.9", "@tiptap/pm": "^2.5.8", "@tiptap/react": "^2.5.8", "@tiptap/starter-kit": "^2.5.8", "axios": "^1.7.9", "cropperjs": "^1.6.2", "framer-motion": "^12.19.2", "html-react-parser": "^5.2.0", "loadsh": "^0.0.4", "lodash.debounce": "^4.0.8", "react": "^18.3.1", "react-advanced-cropper": "^0.20.0", "react-beautiful-dnd": "^13.1.1", "react-dom": "^18.3.1", "react-dropzone": "^14.3.5", "react-easy-crop": "^5.2.0", "react-hook-form": "^7.53.1", "react-icons": "^5.3.0", "react-intersection-observer": "^9.13.1", "react-outside-click-handler": "^1.3.0", "react-quill": "^2.0.0", "react-redux": "^9.1.2", "react-router-dom": "^6.27.0", "react-select": "^5.8.2", "react-toastify": "^10.0.6"}, "devDependencies": {"@eslint/js": "^9.13.0", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.3", "autoprefixer": "^10.4.20", "eslint": "^9.13.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.14", "globals": "^15.11.0", "postcss": "^8.4.47", "tailwindcss": "^3.4.14", "vite": "^5.4.10"}}