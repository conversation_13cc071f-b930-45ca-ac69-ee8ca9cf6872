import React, { useEffect, useCallback, useState } from "react";
import { useSelector } from "react-redux";
import { FiX } from "react-icons/fi";
import Outside<PERSON>lickHandler from "react-outside-click-handler";

// New hooks and components
import { useMediaLibrary } from "./hooks/useMediaLibrary";
import { useFolderOperations } from "./hooks/useFolderOperations";
import { useMediaOperations } from "./hooks/useMediaOperations";
import { useSelectionManager } from "./hooks/useSelectionManager";

import FolderGrid from "./components/FolderGrid";
import MediaGrid from "./components/MediaGrid";
import BreadcrumbNavigation from "./components/BreadcrumbNavigation";
import SelectionInfo from "./components/SelectionInfo";
import SearchAndFilter from "./components/SearchAndFilter";

// Legacy components (keeping for compatibility)
import Aside from "./components/Aside";
import styles from "./MediaLibrary.module.css";
import Filter from "./components/Filter";
import { PiFolderSimplePlusThin } from "react-icons/pi";
import { MdFolder } from "react-icons/md";
import KebabMenu from "./components/KebabMenu";
import { AnimatedButtonLoader } from "../../parts/Loader";
import FileInfo from "./components/FileInfo";
import { Input } from "../../parts/FormComponents";
import Button from "../../parts/Button";
import ConfirmationModal from "../../parts/ConfirmationModal";
import useConfirmationModal from "../../utils/useConfirmationModal";
import {
	runMediaLibraryCallback,
	clearMediaLibraryCallback,
} from "../../utils/MediaLibraryManager";
import EditMediaModal from "./components/EditMediaModal";
const MediaLibrary = () => {
	// Legacy state for compatibility
	const [folderToDelete, setFolderToDelete] = useState(null);
	const [mediaToDelete, setMediaToDelete] = useState(null);
	const [showEditMedia, setShowEditMedia] = useState(false);
	const [mediaToEdit, setMediaToEdit] = useState(null);
	const [updatedMediaCache, setUpdatedMediaCache] = useState({});

	// Confirmation modal hook
	const { isModalOpen, openModal, closeModal } = useConfirmationModal();

	// New hooks for business logic
	const {
		modal,
		navigation,
		data,
		selection,
		ui,
		filters,
		pagination,
		isLoadingFolders,
		isLoadingMedia,
		closeModal: closeMediaLibraryModal,
		loadFolders,
		loadMediaList,
		handleLoadMore,
	} = useMediaLibrary();

	const {
		showCreateFolder,
		newFolderName,
		setNewFolderName,
		showCreateFolderModal,
		hideCreateFolderModal,
		handleCreateFolderSubmit,
		handleDeleteFolder,
		handleBreadcrumbClick,
		handleFolderEnter,
		isCreatingFolder,
		isDeletingFolder,
	} = useFolderOperations();

	const { handleDeleteMedia, handleUpdateMedia, refreshMediaList, isDeletingMedia } =
		useMediaOperations();

	const {
		handleFolderSelect,
		handleMediaSelect,
		handleAddToPage,
		getSelectedItems,
		isSelectionValid,
		clearAllSelections,
	} = useSelectionManager();

	// Legacy selectors for backward compatibility
	const isMultiple = modal.isMultiple;
	const fileType = modal.fileType;
	const folderPath = navigation.folderPath;
	const selectedFolders = selection.selectedFolders;
	const selectedImages = selection.selectedImages;
	const currentFolder = navigation.currentFolder;
	const folderHierarchy = navigation.folderHierarchy;
	const folders = data.folders;
	const media = data.media;
	const mediaPagination = pagination.media;

	// Load initial data when modal opens
	useEffect(() => {
		if (modal.isOpen) {
			loadFolders(currentFolder);
			loadMediaList(true);
		}
	}, [modal.isOpen, currentFolder, loadFolders, loadMediaList]);

	// Legacy function for compatibility
	const handleRefreshMediaLibrary = useCallback(async () => {
		await refreshMediaList(currentFolder, filters.search);
	}, [refreshMediaList, currentFolder, filters.search]);

	// Legacy functions for compatibility - now using hooks
	const handleAddFolderClick = () => {
		showCreateFolderModal();
	};

	const handleCreateFolder = async () => {
		const currentFolderObj = currentFolder ? folders.find((f) => f._id === currentFolder) : null;
		await handleCreateFolderSubmit(currentFolderObj);
	};

	const handleCancelCreateFolder = () => {
		hideCreateFolderModal();
	};

	const handleCloseDeleteModal = () => {
		setFolderToDelete(null);
		setMediaToDelete(null);
		closeModal();
	};

	const handleShowFolderDetails = (folder) => {
		handleFolderSelect(folder);
	};

	const getBreadcrumbPath = () => {
		let path = "Root";
		if (folderHierarchy.length > 0) {
			path += " > " + folderHierarchy.map((f) => f.name).join(" > ");
		}
		return path;
	};

	// Helper functions for selection state
	const isFolderSelected = (folderId) => {
		return selectedFolders.some((folder) => (folder._id || folder.id) === folderId);
	};

	const isImageSelected = (imageId) => {
		return selectedImages.some((image) => image._id === imageId);
	};

	const handleRenameFolder = (folderId) => {
		//Code_Change handle folder rename functionality
		console.log("Rename folder:", folderId);
	};

	// Legacy delete functions - now using hooks but keeping for modal compatibility
	const handleDeleteFolderLegacy = (folderId, folderName) => {
		setFolderToDelete({ id: folderId, name: folderName });
		openModal();
	};

	const confirmDeleteFolder = async () => {
		if (!folderToDelete) return;
		try {
			await handleDeleteFolder(folderToDelete.id);
			setFolderToDelete(null);
		} catch (error) {
			console.error("Failed to delete folder:", error);
		}
	};

	const confirmDeleteMedia = async () => {
		if (!mediaToDelete) return;
		try {
			await handleDeleteMedia(mediaToDelete._id);
			handleCloseDeleteModal();
		} catch (error) {
			console.error("Error deleting media:", error);
		}
	};

	// Image handlers - now using hooks
	const handleImageSelect = (mediaItem) => {
		handleMediaSelect(mediaItem);
	};

	const handleRenameImage = (mediaItem) => {
		const mediaToUse = updatedMediaCache[mediaItem._id] || mediaItem;
		setMediaToEdit(mediaToUse);
		setShowEditMedia(true);
	};

	const handleCloseEditModal = () => {
		setShowEditMedia(false);
		setMediaToEdit(null);
	};

	const handleUpdateMediaLegacy = async (updatedMediaItem) => {
		if (updatedMediaItem && updatedMediaItem._id) {
			setUpdatedMediaCache((prev) => ({
				...prev,
				[updatedMediaItem._id]: updatedMediaItem,
			}));
		}
		await handleUpdateMedia(updatedMediaItem);
	};

	const handleDeleteImage = (mediaItem) => {
		setMediaToDelete(mediaItem);
		openModal();
	};

	const handleAddImage = (imageId) => {
		//Code_Change handle add image to page functionality
		console.log("Add image to page:", imageId);
	};

	// Legacy Add to Page functionality - now handled by hook but keeping for compatibility
	const handleAddToPageLegacy = () => {
		if (!selectedImages || selectedImages.length === 0) {
			closeMediaLibraryModal();
			clearMediaLibraryCallback();
		} else {
			// Prepare selected files with all required details matching Add.jsx expectations
			const selectedFiles = selectedImages.map((image) => {
				const updatedImage = updatedMediaCache[image._id] || image;
				return {
					id: updatedImage._id,
					url: updatedImage.url,
					imageUrl: updatedImage.url,
					title: updatedImage.title,
					altName: updatedImage.alt || updatedImage.title,
					caption: updatedImage.caption || "",
					courtesy: updatedImage.courtesy || "",
				};
			});
			runMediaLibraryCallback(selectedFiles);
			closeMediaLibraryModal();
		}
	};

	// Check if modal should be open
	if (!modal.isOpen) {
		return null;
	}

	return (
		<>
			<div className={styles.modal}>
				<OutsideClickHandler
					onOutsideClick={() => {
						// Don't close media library if any modal is open
						if (!showCreateFolder && !isModalOpen && !showEditMedia) {
							closeMediaLibraryModal();
						}
					}}
				>
					<div className={styles.card}>
						<div className={styles.header}>
							<h4 className={styles.title}>Choose Media</h4>
							<FiX
								className={styles.closeBtn}
								onClick={() => {
									closeMediaLibraryModal();
								}}
							/>
						</div>
						<div className={styles.cardBody}>
							<Aside fileType={fileType} folderPath={folderPath} currentFolder={currentFolder} />
							<div className={styles.mainContainer}>
								<div className="grid grid-cols-12">
									<div
										className="col-span-full md:col-span-9 px-5 flex flex-col border-r"
										style={{ height: "100%" }}
									>
										{/* Fixed Header Section */}
										<div className="flex-shrink-0 z-10 pb-4">
											<Filter />
											<div className="w-full flex justify-between items-center">
												<div
													className="cursor-pointer hover:text-blue-600"
													onClick={() => handleBreadcrumbClick(-1, folderHierarchy)}
												>
													{getBreadcrumbPath()}
												</div>
												<div>
													<PiFolderSimplePlusThin
														className="text-3xl cursor-pointer hover:text-gray-600 transition-colors"
														onClick={handleAddFolderClick}
														title="Add new folder"
													/>
												</div>
											</div>
										</div>

										{/* Scrollable Content Section */}
										<div className="overflow-y-scroll h-[calc(100vh-300px)] scrollbar pt-5 pb-20 px-10 relative">
											{/* Show centered loader when either folders or media are loading */}
											{(isLoadingFolders || isLoadingMedia) && (
												<div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-75 z-10">
													<AnimatedButtonLoader />
												</div>
											)}

											{/* Folders Section */}
											{!isLoadingFolders && (
												<div className="flex flex-wrap gap-y-5 gap-x-10">
													{folders.map((folder) => (
														<div
															key={folder._id}
															className={`group relative transition-all duration-200 ${
																isFolderSelected(folder._id)
																	? "ring-[1.5px] ring-offset-4 ring-blue-500"
																	: ""
															}`}
														>
															{/* Folder Icon */}
															<div className="relative">
																<MdFolder
																	onClick={() => handleShowFolderDetails(folder)}
																	onDoubleClick={() => handleFolderEnter(folder)}
																	className={`text-6xl cursor-pointer transition-all duration-100 ease-in-out hover:opacity-70 ${
																		isFolderSelected(folder._id)
																			? "text-blue-500 fill-blue-500"
																			: "text-blue-300 fill-blue-300"
																	}`}
																/>

																{/* Kebab Menu Component */}
																<KebabMenu
																	itemId={folder._id}
																	itemType="folder"
																	onRename={handleRenameFolder}
																	onDelete={() => handleDeleteFolder(folder._id, folder.name)}
																/>
															</div>
															<div className="text-xs mt-1" title={folder.name}>
																{folder.name.length > 12
																	? folder.name.substring(0, 12) + "..."
																	: folder.name}
															</div>
														</div>
													))}
												</div>
											)}

											{/* Media Section */}
											{!isLoadingMedia && (
												<>
													<div className="flex flex-wrap gap-y-5 gap-x-10 mt-10 transition-opacity duration-1000">
														{media.map((mediaItem) => (
															<div
																key={mediaItem._id}
																className={`group relative transition-all duration-200 ${
																	isImageSelected(mediaItem._id)
																		? "ring-[1.5px] ring-offset-4 ring-blue-500"
																		: ""
																}`}
															>
																{/* Image */}
																<div className="relative">
																	<img
																		src={mediaItem.url}
																		alt={mediaItem.alt || mediaItem.title}
																		className={`w-24 h-24 object-cover cursor-pointer transition-all duration-100 ease-in-out hover:opacity-70 ${
																			isImageSelected(mediaItem._id) ? "opacity-90" : ""
																		}`}
																		onClick={() => handleImageSelect(mediaItem)}
																	/>

																	{/* Kebab Menu Component for Images */}
																	<KebabMenu
																		itemId={mediaItem._id}
																		itemType="image"
																		onRename={() => handleRenameImage(mediaItem)}
																		onDelete={() => handleDeleteImage(mediaItem)}
																		onAdd={handleAddImage}
																	/>
																</div>
																<div className="text-xs mt-1" title={mediaItem.title}>
																	{mediaItem.title.length > 12
																		? mediaItem.title.substring(0, 12) + "..."
																		: mediaItem.title}
																</div>
															</div>
														))}
													</div>

													{/* Load More Button */}
													{mediaPagination.hasMore && (
														<div className="text-center mt-8">
															<Button
																size="sm"
																variant="primary"
																rounded="full"
																onClick={handleLoadMore}
																disabled={isLoadingMedia}
															>
																{isLoadingMedia ? "Loading..." : "Load More"}
															</Button>
														</div>
													)}
												</>
											)}
										</div>
									</div>
									<div className="col-span-full md:col-span-3 px-5 mt-[30px] pb-10 overflow-y-auto h-[calc(100vh-200px)] scrollbar">
										{/* Show File Info when images are selected */}
										{selectedImages.length > 0 && (
											<FileInfo
												onRefresh={handleRefreshMediaLibrary}
												selectedMedia={selectedImages}
												isMultiple={isMultiple && selectedImages.length > 1}
											/>
										)}

										{/* Show Folder Info when folders are selected */}
										{selectedFolders.length > 0 && selectedImages.length === 0 && (
											<div>
												<div className="relative flex flex-col justify-center group cursor-move mb-6">
													<div className="w-full mx-auto aspect-square overflow-hidden bg-gray-100 relative transition-all duration-100 ease-in-out flex items-center justify-center">
														<MdFolder className="text-8xl text-blue-300" />
													</div>
													<div className="text-sm mt-4 text-center">
														{selectedFolders.length === 1
															? selectedFolders[0].name
															: `${selectedFolders.length} folders selected`}
													</div>
												</div>

												<div className="text-sm space-y-2">
													<div className="flex justify-between">
														<span className="font-medium">Type:</span>
														<span>Folder</span>
													</div>
													<div className="flex justify-between">
														<span className="font-medium">Selected:</span>
														<span>
															{selectedFolders.length} folder{selectedFolders.length > 1 ? "s" : ""}
														</span>
													</div>
												</div>
											</div>
										)}

										{/* Show default content when nothing is selected */}
										{selectedImages.length === 0 && selectedFolders.length === 0 && (
											<>
												<div>
													<img
														src="/svg/placeholder-file.svg"
														alt="Placeholder"
														className="h-32 w-full object-cover mt-[30px] mx-auto"
													/>
													<div className="text-sm mt-10">No file selected</div>
												</div>

												<div className="my-5 h-[1px] bg-gray-200"></div>
												<div>
													<h5 className="font-semibold">Actions</h5>
													<button
														onClick={handleAddFolderClick}
														className="mt-2 bg-transparent text-sm flex items-center gap-x-2 text-primary"
													>
														<PiFolderSimplePlusThin className="text-xl" />
														Create New Folder
													</button>
												</div>

												<div className="my-5 h-[1px] bg-gray-200"></div>
												<div>
													<h5 className="font-semibold">Information</h5>
													<p className="text-sm">
														Organize file types and folders to keep your media library organized.
													</p>
												</div>
											</>
										)}
									</div>
								</div>
							</div>
							{/* Add to Page Button */}
							<div className="w-full flex justify-end absolute  -bottom-3 right-2 py-5 bg-white">
								<Button
									variant="primary"
									size="sm"
									rounded="full"
									onClick={handleAddToPage}
									disabled={!selectedImages || selectedImages.length === 0}
									customClasses="!px-8"
								>
									Add to Page
								</Button>
							</div>
						</div>
					</div>

					<div style={{ zIndex: 99999999 }}>
						<ConfirmationModal
							isOpen={isModalOpen}
							isLoading={isDeletingFolder || isDeletingMedia}
							toggleModal={handleCloseDeleteModal}
							message={
								folderToDelete
									? `Are you sure you want to delete the folder "${folderToDelete.name}"? This action cannot be undone.`
									: mediaToDelete
									? `Are you sure you want to delete the media "${mediaToDelete.title}"? This action cannot be undone.`
									: "Are you sure you want to delete this item?"
							}
							onConfirm={folderToDelete ? confirmDeleteFolder : confirmDeleteMedia}
						/>
					</div>
				</OutsideClickHandler>

				{/* Create Folder Modal */}
				{showCreateFolder && (
					<div
						className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[10000]"
						onClick={(e) => {
							// Only close if clicking on the backdrop, not the modal content
							if (e.target === e.currentTarget) {
								handleCancelCreateFolder();
							}
						}}
					>
						<div
							className="bg-white rounded-lg p-6 w-96 max-w-md mx-4"
							onClick={(e) => e.stopPropagation()} // Prevent event bubbling
						>
							<h3 className="text-lg font-semibold mb-4">Create New Folder</h3>
							<div className="mb-4">
								<Input
									label="Folder Name"
									type="text"
									value={newFolderName}
									onDebouncedChange={setNewFolderName}
									onEnter={handleCreateFolder}
									placeholder="Enter folder name"
									required={true}
									customClass="focus:border-primary"
								/>
							</div>
							<div className="text-sm text-gray-600 mb-4">
								{folderHierarchy.length === 0
									? "Creating folder in root directory"
									: `Creating folder in: ${folderHierarchy.map((f) => f.name).join(" > ")}`}
							</div>
							<div className="flex justify-end space-x-3">
								<Button
									variant="secondary"
									size="sm"
									rounded="full"
									onClick={(e) => {
										e.stopPropagation();
										handleCancelCreateFolder();
									}}
									disabled={isCreatingFolder}
								>
									Cancel
								</Button>
								<Button
									variant="primary"
									size="sm"
									rounded="full"
									onClick={(e) => {
										e.stopPropagation();
										handleCreateFolder();
									}}
									disabled={!newFolderName.trim() || isCreatingFolder}
								>
									{isCreatingFolder ? "Creating..." : "Create"}
								</Button>
							</div>
						</div>
					</div>
				)}

				{/* Edit Media Modal */}
				<EditMediaModal
					isOpen={showEditMedia}
					mediaItem={mediaToEdit}
					onClose={handleCloseEditModal}
					onUpdate={handleUpdateMedia}
				/>
			</div>
		</>
	);
};

export default MediaLibrary;
