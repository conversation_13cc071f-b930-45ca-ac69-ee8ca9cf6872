.editor-nav {
  display: flex;
  height: 60px;
  padding: 0 1%;
  justify-content: space-between;
  align-items: center;
  width: 100vw;
  border-bottom: 0.5px solid rgba(0, 0, 0, 0.2);
  position: sticky;
  top: 0;
  z-index: 5;
  background-color: #fff;
}
.pn-back {
  gap: 5px;
  cursor: pointer;
  margin-right: 218px;
}
.pn-back:hover {
  opacity: 0.4;
}
.pn-back svg {
  font-size: 28px;
  opacity: 0.8;
}
.publish {
  border: none;
  background-color: var(--primary-color);
  /* outline: none; */
  color: white;
  width: 100px;
  font-size: 15px;
  height: 38px;
  border-radius: 20px;
  cursor: pointer;
}
.publish:hover {
  background-color: var(--primary-hovered-color);
}
.publish:disabled {
  background-color: rgba(0, 6, 36, 0.3);
  cursor: auto;
}
.editor-body {
  display: flex;
  height: calc(100vh - 60px);
}
.eb-left {
  height: calc(100vh - 60px);
  padding: 5px 0;
  width: 88px;
  position: relative;
  background-color: white;
  border-right: 0.5px solid rgba(0, 0, 0, 0.2);
  overflow-y: scroll;
  position: sticky;
  top: 0;
  left: 0;
  z-index: 3;
}
.eb-ai-block {
  position: absolute;
  bottom: 0px;
  left: 0;
  cursor: pointer;
  height: 85px;
  width: 100%;
  /* background-color: red; */
}
.ai-img {
  width: 60%;
  height: 60%;
}
.ai-img img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}
.eb-block {
  height: 85px;
  width: 100%;
  transition: all 0.3s ease;
  flex-direction: column;
  gap: 5px;
  cursor: pointer;
  /* background-color: rebeccapurple; */
  padding: 0 10px;
}
.eb-circle {
  width: 40px;
  height: 40px;
  transition: all 0.3s ease;
  border-radius: 50%;
  border: 0.5px solid rgba(0, 0, 0, 0.2);
}
.eb-name {
  color: rgba(0, 0, 0, 0.7);
  font-size: 13px;
  transition: all 0.3s ease;
}
.eb-block:hover .eb-circle {
  border: 0.5px solid var(--primary-color);
  background-color: var(--primary-light-color);
  color: var(--primary-color);
}
.eb-block:hover .eb-name {
  color: var(--primary-color);
}
.eb-mid {
  width: 400px;
  height: calc(100vh - 60px);
  overflow-y: auto;
  margin-left: -400px;
  border-right: 0.5px solid rgba(0, 0, 0, 0.2);
  z-index: 1;
}
.eb-mid-open {
  margin-left: 0px;
}
.eb-right {
  background-color: #eeeded;
  background-color: #f5f5f5;
  height: calc(100vh - 60px);
  overflow-y: auto;
  width: calc(100%);
}
.eb-right-open {
  width: calc(100% - 480px);
}
.feature-image-inner {
  border: 1px dashed var(--primary-color);
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.em-top {
  padding: 0 4%;
  height: 55px;
  justify-content: space-between;
  display: flex;
  font-size: 17px;
  align-items: center;
  width: 100%;
  position: sticky;
  z-index: 4;
  background-color: #fff;
  top: 0;
  border-bottom: 0.5px solid rgba(0, 0, 0, 0.2);
}
.em-close {
  height: 100%;
  width: 30px;
  cursor: pointer;
}
.em-close:hover {
  opacity: 0.4;
}
.er-top {
  position: sticky;
  top: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 60px;
  width: 100%;
  background-color: white;
  z-index: 2;
}
.editor-js-cont {
  width: 100%;
  position: relative;
  height: calc(100% - 60px);
}
.ad-blocks-cont {
  padding: 40px 30px;
  padding-top: 0;
}
.adb-block {
  margin: 40px 0;
}
.temp-parent {
  margin: 40px 0;
}
.adb-name {
  font-size: 14px;
  margin: 5px 0;
  margin-bottom: 15px;
}
.adb-elms {
  margin: 10px 0;
  display: flex;
  flex-wrap: wrap;
  gap: 20px 15px;
  justify-content: baseline;
}
.adb-elm {
  border: 0.5px solid rgba(0, 0, 0, 0.7);
  cursor: pointer;
  border-radius: 5px;
  transition: all 0.3s ease;
  height: 90px;
  padding: 0 5px;
  text-align: center;
  display: flex;
  gap: 5px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 98px;
  min-width: 98px;
}
.adb-elm:hover {
  border: 0.5px solid var(--primary-color);
  color: var(--primary-color);
}
.adb-elm-svg {
  font-size: 25px;
}
.adb-elm-name {
  font-size: 13px;
}
.adb-elm:hover g {
  fill: var(--primary-color);
}
.adb-elm:hover rect {
  fill: var(--primary-color);
}
.adb-elm:hover path {
  fill: var(--primary-color);
}
.er-wrap-center {
  display: flex;
  align-items: center;
  justify-content: center;
}
.er-wrap-items {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
}
.er-wrap-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 5px;
  transition: all 0.3s ease;
  border-radius: 4px;
  border: none;
  background: transparent;
}
.er-wrap-icon.active,
.er-wrap-icon:hover {
  background-color: var(--primary-light-color);
}
.er-wrap-items svg {
  width: 16px;
  height: 16px;
}

.bubble-menu-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 25px;
  /* padding: 5px; */
  transition: all 0.3s ease;
  border-radius: 4px;
  border: none;
  background: transparent;
}
.bubble-menu-icon.active,
.bubble-menu-icon:hover {
  background-color: var(--primary-light-color);
}
.bubble-menu-divider {
  display: block;
  width: 1px;
  height: 15px;
  margin: auto 5px;
  background-color: rgba(0, 0, 0, 0.2);
}
.er-divider {
  display: block;
  width: 1px;
  height: 15px;
  margin: auto 10px;
  background-color: rgba(0, 0, 0, 0.2);
}

.flex-story-btn {
  display: flex;
  align-items: center;
  gap: 14px;
}

.story-dots {
  height: 30px;
  width: 30px;
  border: 1px solid #e6f4ff;
  background-color: #e6f4ff;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  cursor: pointer;
  color: var(--primary-color);
}
.story-dots:disabled {
  color: rgba(22, 45, 61, 0.3);
  border-color: rgba(22, 45, 61, 0.1);
  background-color: transparent;
  cursor: auto;
}
.story-head-p {
  color: var(--primary-color);
  cursor: pointer;
  border: none;
  font-size: 16px;
  background: transparent;
}
.story-head-p:disabled {
  color: rgba(0, 0, 0, 0.2);
  cursor: auto;
}
.divider {
  width: 100%;
  border: 1px solid rgb(220, 220, 220);
}

.undo-main {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 15px;
}
.undo-logo {
  font-size: 18px;
  cursor: pointer;
  color: rgba(0, 0, 0, 0.7);
}
.undo-div {
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}
.undo-div > button.er-wrap-icon:hover {
  background-color: unset !important;
}
.undo-div > button.er-wrap-icon:disabled svg {
  background-color: unset !important;
  color: rgba(0, 0, 0, 0.2);
}

.undo-div > button.er-wrap-icon svg {
  color: rgba(0, 0, 0, 1);
}

.flex-story-btn .verti-line {
  display: block;
  height: 15px;
  width: 1px;
  background-color: rgba(0, 0, 0, 0.2);
  border: none;
}

.editor-js-title {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  margin: auto;
  padding-top: 25px;
  margin-left: auto;
  margin-right: auto;
  max-width: 50rem;
}
.editor-js-input {
  font-family: inherit;
  width: 100%;
  /* max-width: 650px; */
  border: none !important;
  font-size: 24px;
  color: rgba(0, 0, 0, 1);
  font-weight: 500;
  line-height: 1.2;
  background-color: transparent;
  outline: none;
  resize: none;
}
.editor-js-input::placeholder {
  font-size: 24px;
  color: rgba(0, 0, 0, 0.4);
  font-weight: 500;
  line-height: 1.2;
}
.editor-js-input:focus,
.editor-js-input:focus-visible {
  border: transparent !important;
}
.colot-btn-container {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 15px;
  padding: 4px 8px 8px 16px;
  flex-wrap: wrap;
}
.color-btn-box {
  width: 20px;
  height: 20px;
  outline: 0;
  position: relative;
  border: 1px solid #ccc;
  border-radius: 4px;
  transform: scale(1);
  cursor: pointer;
  transition: all 0.3s ease;
}
.color-btn-box:hover {
  opacity: 0.5;
}
.color-btn-box.selected {
  transform: scale(0.7);
}
.color-btn-box.selected::after {
  border-color: rgba(59, 130, 246, 0.35);
  transform: scale(1.45);
}
.color-btn-box::after {
  bottom: 1px;
  box-sizing: border-box;
  content: " ";
  display: block;
  height: 20px;
  outline: 0;
  position: relative;
  width: 20px;
  right: 1px;
  background-color: transparent;
  border: 1px solid transparent;
  border-radius: 4px;
}
.color-ft-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 12px 4px 12px;
}
.color-reset-btn {
  display: flex;
  font-size: 14px;
  background-color: transparent;
  text-decoration: underline;
  color: rgba(59, 130, 246, 0.75);
  border: none;
  transition: all 0.3s ease;
}
.color-reset-btn:hover {
  color: rgba(59, 130, 246, 1);
}
.color-add-btn {
  width: 18px;
  height: 18px;
  color: rgba(59, 130, 246, 0.75);
  transition: all 0.3s ease;
}
.color-add-btn:hover {
  color: rgba(59, 130, 246, 1);
}
.colot-picker-container {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 5px 10px 10px;
}
.react-colorful {
  width: 180px !important;
  height: 150px !important;
}
.react-colorful__saturation {
  border-radius: 0px !important;
}
.react-colorful__pointer {
  width: 16px !important;
  height: 16px !important;
}
.react-colorful__last-control {
  border-radius: 0px !important;
}
.react-colorful__hue {
  height: 14px !important;
}
.react-colorful__hue-pointer > * {
  background-color: #fff !important;
}
.color-input-box {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding: 5px 10px 10px;
  gap: 10px;
}
.input-label {
  color: rgba(0, 0, 0, 0.75);
  font-size: 14px;
  font-weight: 300;
}
.hex-input {
  padding: 5px 10px;
  border: 1px solid #c4e4fb;
  border-radius: 5px;
  font-size: 16px;
  font-weight: 400;
  width: 100%;
}
.color-span {
  display: block;
  min-width: 20px;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  border: 1px solid #e0e0e3;
  background-color: transparent;
}
.eb-block.active {
  background-color: transparent;
  color: white; /* Optional: Change text color */
}

.eb-circle.active {
  background-color: var(
    --primary-light-color
  ); /* Optional: Change circle color */
  color: #2684ff; /* Optional: Change icon color */
}

.eb-name.active {
  background-color: transparent;
  color: var(--primary-color); /* Optional: Change text color */
}
.error-div {
  display: flex;
}
.chips-tags-addedit{
  margin-bottom: 50px;
}

.error-div .error {
  width: 100%;
  color: red;
  font-size: 14px;
  margin-top: 10px;
}

.link-menu {
  width: 100%;
  height: 100%;
}
.link-menu-head {
  width: 100%;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 10px 8px 26px;
  border-bottom: 1px solid #dfe5eb;
}

.link-menu-main {
  width: 100%;
  padding: 24px;
}

.link-menu-footer {
  width: 100%;
  height: 66px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 10px;
  padding: 8px 10px 8px 26px;
  border-top: 1px solid #dfe5eb;
}

.link-menu-title {
  font-size: 16px;
  font-weight: 600;
}
.btn-menulink-close {
  padding: 2px;
  border: none;
  background-color: transparent;
  cursor: pointer;
}
.btn-menulink-close svg {
  width: 22px;
  height: 22px;
}
.radio-list {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 5px;
  margin-bottom: 10px;
}
.radio-list label,
.input-list label {
  font-size: 14px;
  font-weight: 300;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 12px;
}

.radio-list label input[type="radio"] {
  -webkit-appearance: none;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  outline: none;
  border: 1px solid var(--primary-color);
}

.radio-list label input[type="radio"]:before {
  content: "";
  display: flex;
  align-items: center;
  justify-content: center;
  width: 70%;
  height: 70%;
  margin: 16% auto;
  border-radius: 50%;
}

.radio-list label input[type="radio"]:checked:before {
  background: var(--primary-color);
}

.radio-list label input[type="radio"]:checked {
  border-color: var(--primary-color);
}
.input-list {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  gap: 5px;
  margin-bottom: 10px;
}
.input-list input[type="text"] {
  padding: 6px 10px;
  border: 1px solid #c4e4fb;
  border-radius: 5px;
  font-size: 16px;
  font-weight: 400;
  font-family: inherit;
  width: 100%;
}
.input-list input[type="text"]:hover {
  background-color: #eaf7ff;
}

.input-list label input[type="checkbox"] {
  -webkit-appearance: none;
  width: 16px;
  height: 16px;
  border-radius: 4px;
  outline: none;
  border: 1px solid var(--primary-color);
}
.input-list label input[type="checkbox"]:before {
  content: "";
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.input-list label input[type="checkbox"]:checked:before {
  background: var(--primary-color);
  background-image: url('data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="%23fff" stroke="%23fff" stroke-width="0" width="5" height="5"%3E%3Cpath d="m2.67 7.63 2.79 2.78 7.87-7.87 1.52 1.52-9.39 9.4-4.31-4.31 1.52-1.52z"/%3E%3C/svg%3E');
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}

.input-list label input[type="checkbox"]:checked {
  border-color: var(--primary-color);
}
.link-menu-footer .btn-primary {
  height: unset !important;
}

.link-menu-footer .btn-primary:disabled {
  background-color: rgba(0, 0, 0, 0.2) !important;
  border: none !important;
  transform: unset !important;
}

.menu-drawer {
  width: 100%;
  height: 100%;
  background-color: #fff;
}

.menu-drawer .headsec {
  position: sticky;
  position: -webkit-sticky;
  top: 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  border-bottom: 1px solid rgba(225, 225, 225, 1);
  background-color: #fff;
  z-index: 999;
}

.menu-drawer .headsec h2.heading {
  font-size: 20px;
  font-weight: 600;
  color: #000;
  margin-bottom: 0px;
}

.menu-drawer .headsec .close {
  cursor: pointer;
  width: 25px;
  height: 25px;
  color: rgba(0, 0, 0, 0.5);
}

.menu-drawer .mainsec {
  width: 100%;
  height: calc(100% - 59.5px);
  overflow-y: auto;
}

.menu-drawer .footersec {
  width: -webkit-fill-available;
  position: fixed;
  bottom: 0;
  display: flex;
  align-items: center;
  gap: 10px;
  justify-content: space-between;
  padding: 10px 20px;
  border-top: 1px solid rgba(225, 225, 225, 1);
  background-color: #fff;
}
.menu-drawer .footersec button {
  width: 100%;
}

.MuiOutlinedInput-root{
  padding: 5px !important;
}
.temp1-main{
  width: 100%;
  cursor: pointer;
  padding: 10px;
  border-radius: 16px;
  border: 2px solid rgb(220, 220, 220);
}
.temp1-img{
  width: 100%;
  height: 30%;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid rgb(220, 220, 220);
}
.temp2-img{
  width: 100%;
  height: 30%;
  display: flex;
  align-items: center;
  justify-content: center;
  border-top: 1px solid rgb(220, 220, 220);
  border-bottom: 1px solid rgb(220, 220, 220);
}
.temp3-img{
  width: 50%;
  height: 135px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-left: 1px solid rgb(220, 220, 220);
  border-bottom: 1px solid rgb(220, 220, 220);
}
.temp1-title-p{
  text-align: center;
}
.temp1-subtitle-p{
  text-align: center;
}
.temp1-title{
  margin-top: 10px;
}
.temp2-title{
  margin: 10px;
}
.temp3-title{
  height: 135px;
  width: 50%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border-bottom: 1px solid rgb(220, 220, 220);
}
.story-temp{
  text-align: center;
  margin-top: 50px;
}
.temp3-main{
  display: flex;
  align-items: center;
}
.temp-active{
  border: 2px solid #2684ff;
}
.temp1-active{
  border-bottom: 1px solid #2684ff;
}
.temp2-active{
  border-bottom: 1px solid #2684ff;
  border-top: 1px solid #2684ff;
}
.temp3-active1{
  border-bottom: 1px solid #2684ff;
}
.temp3-active2{
  border-bottom: 1px solid #2684ff;
  border-left: 1px solid #2684ff;
}