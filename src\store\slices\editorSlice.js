import { createSlice } from "@reduxjs/toolkit";

const initialState = {
  dropdowns: {
    paragraph: false,
    fontSize: false,
    align: false,
    lineHeight: false,
    size: false,
    layout: false,
    imageAlignment: false,
  },
  formatting: {
    paragraph: "Paragraph",
    fontSize: "16",
    align: "left",
    bold: false,
    italic: false,
    underline: false,
    lineHeight: "1.5",
    textColor: "#000",
    highlightColor: "#dedede",
  },
};

export const editorSlice = createSlice({
  name: "editor",
  initialState,
  reducers: {
    toggleDropdown: (state, action) => {
      // Close all other dropdowns when opening one
      Object.keys(state.dropdowns).forEach((key) => {
        state.dropdowns[key] =
          key === action.payload ? !state.dropdowns[key] : false;
      });
    },
    updateFormatting: (state, action) => {
      state.formatting = { ...state.formatting, ...action.payload };
    },
    toggleFormat: (state, action) => {
      state.formatting[action.payload] = !state.formatting[action.payload];
    },
    closeAllDropdowns: (state) => {
      Object.keys(state.dropdowns).forEach((key) => {
        state.dropdowns[key] = false;
      });
    },
    resetEditorState: () => initialState,
  },
});

export const {
  toggleDropdown,
  updateFormatting,
  toggleFormat,
  closeAllDropdowns,
  resetEditorState,
} = editorSlice.actions;
export default editorSlice.reducer;
