import React, { useState, useCallback, useRef } from "react";
import { BubbleMenu as BaseBubbleMenu } from "@tiptap/react";
import { getRenderContainer } from "../../../utils/getRenderContainer";
import { BiTrash } from "react-icons/bi";
import "../../../../styles/stories.css";
import AstroBlockSetting from "./AstroBlockSetting";

export const AstroBlockMenu = ({ editor, showAstroDrawer, appendTo }) => {
  const menuRef = useRef(null);
  const tippyInstance = useRef(null);
  
  const getReferenceClientRect = useCallback(() => {
    const renderContainer = getRenderContainer(editor, "node-astroBlock");
    const rect =
      renderContainer?.getBoundingClientRect() ||
      new DOMRect(-1000, -1000, 0, 0);

    return rect;
  }, [editor]);

  const shouldShow = useCallback(() => editor.isActive("astroBlock"), [editor]);

  const onRemoveBlock = useCallback(() => {
    editor.chain().focus().deleteNode("astroBlock").run();
  }, [editor]);

  return (
    <>
      <BaseBubbleMenu
        editor={editor}
        pluginKey="astroBlockMenu"
        shouldShow={shouldShow}
        updateDelay={0}
        tippyOptions={{
          interactive: true,
          offset: [0, 8],
          maxWidth: "100%",
          popperOptions: {
            modifiers: [{ name: "flip", enabled: false }],
          },
          getReferenceClientRect,
          onCreate: (instance) => {
            tippyInstance.current = instance;
          },
          appendTo: () => appendTo?.current || document.body,
        }}
      >
        <div className="bubble-menu" ref={menuRef}>
          <button
            className="bubble-menu-icon text-fadeGray"
            onClick={() => onRemoveBlock()}
          >
            <div className="p-1 hover:bg-gray-100 rounded-sm flex items-center gap-1">
              <BiTrash />
            </div>
          </button>
        </div>
      </BaseBubbleMenu>
      <AstroBlockSetting
        editor={editor}
        attrs={editor.getAttributes("astroBlock") || {}}
        open={showAstroDrawer}
      />
    </>
  );
};

export default AstroBlockMenu;
