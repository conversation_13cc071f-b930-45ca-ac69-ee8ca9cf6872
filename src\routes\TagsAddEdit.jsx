import React, { useEffect } from "react";
import { BiChevronRight } from "react-icons/bi";
import { Link, useLocation, useNavigate, useParams } from "react-router-dom";
import Container from "../parts/Container";
import Button from "../parts/Button";
import { useDispatch, useSelector } from "react-redux";
import {
  useCreateTagMutation,
  useGetMetaDataTagQuery,
  useGetTagQuery,
  useUpdateTagMutation,
} from "../store/apis/tagsApi";
import {
  clearErrors,
  resetState,
  setErrors,
  setTagsData,
  setTagsMetaTemplateData,
} from "../store/slices/tagsSlice";
import TagSEO from "../components/tags/TagSEO";
import TopSection from "../components/tags/TopSection";
import { toast } from "react-toastify";
import BreadCrumb from "../parts/BreadCrumb";
import { shallowEqual } from "react-redux";

// need to check for the post and put state
// figure how to change and clear the caching upon post and put request
const TagsAddEdit = ({ method }) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { id } = useParams();
  const location = useLocation();
  const {
    name,
    description,
    storiesState: { meta, ogImg, imgsrc, twitterImg, og, twitter },
  } = useSelector((state) => state.tags, shallowEqual);
  const { data, isLoading, isError, error } = useGetTagQuery(id, {
    skip: !id,
  });

  const [updateTag, { isLoading: tagLoading, isError: tagError }] =
    useUpdateTagMutation();
  const [createTag, { isLoading: tagCreateLoading, isError: tagCreateError }] =
    useCreateTagMutation();

  // displaying the error message here
  if (isError) {
    if (error.status === 401) {
      toast.error("Session Expired", { toastId: "video_edit_session_expired" });
      navigate("/signin");
    } else {
      toast.error(error.data.message, { toastId: "get_single_video_error" });
    }
  }

  const validateForm = () => {
    const newErrors = {};
    if (!name) {
      newErrors.name = "Tag name is required";
    }
    return newErrors;
  };

  // function to call on the submit tags
  const handleSubmit = () => {
    const validationErrors = validateForm();
    if (Object.keys(validationErrors).length > 0) {
      dispatch(setErrors(validationErrors));
    } else {
      dispatch(clearErrors());
      // continue with the form submission

      // const formData = new FormData();
      const payload = {
        name,
        description,
        slug: `/${meta.slug}`,
      };
      const finalMetaData = {
        title: meta.title,
        description: meta.description,
        keywords: meta.keywords,
        author: meta.author,
        canonical: meta.canonical,
        robots: meta.robots,
        _id: meta._id,
        og: {
          title: og.title,
          description: og.description,
        },
        twitter: {
          title: twitter.title,
          description: twitter.description,
          card: twitter.card === "large" ? "summary_large_image" : "summary",
        },
      };

      // Simple file type check
      // const isFile = (value) => {
      //   return typeof value === "string";
      // };

      // // Apply checks
      // if (isFile(imgsrc)) {
      //   finalMetaData.og.image = imgsrc;
      // }

      // if (isFile(ogImg)) {
      //   finalMetaData.og.image = ogImg;
      // }

      // if (isFile(twitterImg)) {
      //   finalMetaData.twitter.image = twitterImg;
      // }

      // if (!isFile(ogImg)) {
      //   formData.append("ogImg", ogImg[0]);
      // }
      // if (!isFile(twitterImg)) {
      //   formData.append("twitterImg", twitterImg[0]);
      // }

      payload.meta = finalMetaData;

      if (method === "POST") {
        createTag(payload)
          .then((res) => {
            if (res.data.status === "success") {
              toast.success("Tag Create successfully!");
              navigate("/admin/tags");
            } else {
              toast.error("Failed to create tag.");
            }
          })
          .catch((err) => {
            toast.error("Failed to create tag.");
            console.log(err);
          });
      } else if (method === "PUT") {
        updateTag({ data: payload, id: id })
          .then((res) => {
            console.log(res);
            if (res.data.status === "success") {
              toast.success("Tag updated successfully!");
              navigate("/admin/tags");
            } else {
              toast.error("Failed to update tag.");
            }
          })
          .catch((err) => {
            toast.error("Failed to update tag.");
            console.log(err);
          });
      }
    }
  };

  // clear the edit data when component unmounts
  useEffect(() => {
    if (data) {
      dispatch(setTagsData(data));
    }

    return () => {
      if (data) {
        dispatch(resetState());
      }
    };
  }, [data]);

  const handleCancel = () => navigate("/admin/tags");

  if (isLoading) {
    return <div>Loading...</div>;
  }
  return (
    <Container>
      <div className="flex w-full items-center justify-between mb-0">
        <div>
          <div className="flex items-center  text-sm">
            <Link
              to={"/admin/tags"}
              className="hover:bg-white text-blackShade rounded-full px-3 py-1 bg-transparent "
            >
              Tags
            </Link>
            <BiChevronRight className="text-xl" />
            <p className="text-fadeGray pl-2">{`${name}` || "Untitled Tag"}</p>
          </div>
          <BreadCrumb
            title={"Tags"}
            description={
              " Set the SEO and social sharing images and help readers navigate your storys"
            }
          />
        </div>
        <div className="flex items-center gap-x-3">
          <Button
            rounded="full"
            variant="secondary"
            size="sm"
            customClasses="py-[7px]"
            onClick={handleCancel}
          >
            Cancel
          </Button>
          <Button
            rounded="full"
            size="sm"
            customClasses="py-[7px]"
            onClick={handleSubmit}
          >
            {tagLoading || tagCreateLoading ? "Please Wait..." : "Save"}
          </Button>
        </div>
      </div>

      <div className="mt-4 px-2 flex gap-x-5">
        <div className="w-[60%] flex flex-col gap-y-5">
          <TopSection method={method} />
        </div>
        <div className="flex flex-col gap-y-5 w-[40%]">
          <TagSEO />
        </div>
      </div>
    </Container>
  );
};

export default TagsAddEdit;
