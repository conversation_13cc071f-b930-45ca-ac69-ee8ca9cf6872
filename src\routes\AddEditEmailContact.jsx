import React, { useEffect, useState } from "react";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { toast } from "react-toastify";
import Container from "../parts/Container";
import BreadCrumb from "../parts/BreadCrumb";
import Button from "../parts/Button";
import { Input } from "../parts/FormComponents";
import { setFetchedContactsData } from "../store/slices/emailMarketingSlice";
import {
  useCreateEmailContactMutation,
  useGetEmailGroupsQuery,
  useUpdateEmailContactMutation,
} from "../store/apis/emailMarketingApi";

const AddEditEmailContact = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { state } = useLocation();
  const initial = {
    email: "",
    firstName: "",
    lastName: "",
    status: 1,
    groups: [],
  };
  // Form state
  const [formData, setFormData] = useState(initial);

  // Get contacts and groups from Redux store
  const { data: contacts } = useSelector(
    (state) => state.emailMarketing.contacts
  );
  const { data: groups, isLoading: loadingGroups } = useGetEmailGroupsQuery();
  const [createEmailContact, { isLoading }] = useCreateEmailContactMutation();

  const [updateEmailContact, { isLoading: isUpdating }] =
    useUpdateEmailContactMutation();

  // Check if we're editing an existing contact
  const isEditing = !!id;

  // Load contact data if editing
  useEffect(() => {
    if (isEditing) {
      console.log(state);

      // TODO :contact/:id API call

      if (state) {
        setFormData({
          email: state?.email || "",
          firstName: state?.firstName || "",
          lastName: state?.lastName || "",
          status: state?.status || "active",
          groups: state?.groups?.map((g) => g.id) || [],
        });
      } else {
        // If contact not found, redirect to contacts list
        toast.error("Contact not found");
        navigate("/admin/email/contacts");
      }
    }
  }, [id, isEditing, navigate, contacts]);

  // Handle form field changes
  const handleChange = (field, value) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  // Handle group selection changes
  const handleGroupChange = (e) => {
    const value = Array.from(
      e.target.selectedOptions,
      (option) => option.value
    );

    setFormData((prev) => ({
      ...prev,
      groups: value,
    }));
  };

  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();

    // Validate form
    if (!formData.email) {
      toast.error("Email is required");
      return;
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.email)) {
      toast.error("Please enter a valid email address");
      return;
    }

    // Prepare data for API
    const contactData = {
      ...formData,
      // Convert groups to array of objects for display
      groupsData: formData.groups?.data?.map((groupId) => {
        const group = groups.find((g) => g._id === groupId);
        return {
          id: groupId,
          name: group?.name || "Unknown Group",
        };
      }),
    };

    // Log what would be sent to API
    console.log(
      `Would ${isEditing ? "update" : "create"} contact with data:`,
      contactData
    );

    // UNCOMMENT WHEN API IS READY:
    // Send data to API
    if (isEditing) {
      //   console.log("EDIT");
      updateEmailContact({ id, contactData })
        .then((res) => {
          if (res?.data?.status == "success") {
            setFormData(initial);
            toast.success(
              res?.data?.message || "Email contact updated successfully"
            );
            navigate("/admin/email/contacts");
          } else {
            toast.error(res?.error?.data?.message || "Something went wrong");
          }
        })
        .catch((err) => {
          toast.error("Something went wrong");
        });
    } else {
      createEmailContact(contactData)
        .then((res) => {
          if (res?.data?.status == "success") {
            setFormData(initial);
            toast.success(
              res?.data?.message || "Email contact created successfully"
            );
            navigate("/admin/email/contacts");
          } else {
            toast.error(res?.error?.data?.message || "Something went wrong");
          }
        })
        .catch((err) => {
          toast.error("Something went wrong");
        });
    }
  };

  return (
    <Container>
      <div className="lg:flex items-center justify-between mb-5">
        <div>
          <BreadCrumb
            title={isEditing ? "Edit Email Contact" : "Add Email Contact"}
            description={
              isEditing
                ? "Update an existing email contact"
                : "Add a new contact to your email list"
            }
          />
        </div>
      </div>

      <div className="bg-white p-6 rounded-lg shadow-sm">
        <form onSubmit={handleSubmit}>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div>
              <Input
                label="Email Address"
                value={formData.email}
                onDebouncedChange={(value) => handleChange("email", value)}
                placeholder="Enter email address"
                required={true}
                type="email"
              />
            </div>

            <div className="flex flex-col gap-y-2">
              <label className="text-sm text-gray-600 mb-1">
                Status
                <sup className="text-red-500 text-base pt-1">*</sup>
              </label>
              <select
                value={formData.status}
                onChange={(e) => handleChange("status", e.target.value)}
                required={true}
                className="border text-sm border-gray-300 rounded-md px-4 py-2 w-full focus:outline-none focus:border-blue-500"
              >
                <option value="1">Active</option>
                <option value="0">Unsubscribed</option>
              </select>
            </div>

            <div>
              <Input
                label="First Name"
                value={formData.firstName}
                onDebouncedChange={(value) => handleChange("firstName", value)}
                placeholder="Enter first name"
              />
            </div>

            <div>
              <Input
                label="Last Name"
                value={formData.lastName}
                onDebouncedChange={(value) => handleChange("lastName", value)}
                placeholder="Enter last name"
              />
            </div>

            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Groups
              </label>
              <select
                multiple
                value={formData.groups}
                onChange={handleGroupChange}
                className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm rounded-md h-32"
              >
                {groups?.data?.map((group) => (
                  <option key={group._id} value={group._id}>
                    {group.name}
                  </option>
                ))}
              </select>
              <p className="mt-1 text-xs text-gray-500">
                Hold Ctrl (or Cmd) to select multiple groups
              </p>
            </div>
          </div>

          <div className="flex justify-end gap-4">
            <Button
              type="button"
              variant="secondary"
              rounded="full"
              onClick={() => navigate("/admin/email/contacts")}
            >
              Cancel
            </Button>

            <Button
              type="submit"
              rounded="full"
              isLoading={isLoading || isUpdating}
              disabled={isLoading || isUpdating}
            >
              {isEditing ? "Update Contact" : "Create Contact"}
            </Button>
          </div>
        </form>
      </div>
    </Container>
  );
};

export default AddEditEmailContact;
