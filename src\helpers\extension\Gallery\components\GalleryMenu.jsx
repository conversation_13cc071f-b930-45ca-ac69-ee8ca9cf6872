import { BubbleMenu as BaseBubbleMenu, useEditorState } from "@tiptap/react";
import React, { useState, useCallback, useRef } from "react";
import { getRenderContainer } from "../../../utils/getRenderContainer";
import SizeSmall from "../../../../../public/svg/SizeSmall";
import SizeMedium from "../../../../../public/svg/SizeMedium";
import SizeFullWidth from "../../../../../public/svg/SizeFullWidth";
import SizeOriginal from "../../../../../public/svg/SizeOriginal";
import AlignLeft from "../../../../../public/svg/AlignLeft";
import AlignCenter from "../../../../../public/svg/AlignCenter";
import AlignRight from "../../../../../public/svg/AlignRight";
import Grid from "../../../../../public/svg/Grid";
import Masonry from "../../../../../public/svg/Masonry";
import Collage from "../../../../../public/svg/Collage";
import Thumbnails from "../../../../../public/svg/Thumbnails";
import Slideshow from "../../../../../public/svg/Slideshow";
import Panorama from "../../../../../public/svg/Panorama";
import Columns from "../../../../../public/svg/Columns";
import Slider from "../../../../../public/svg/Slider";
import "../../../../styles/stories.css";
import { FiSettings } from "react-icons/fi";
import { DropdownMenu, SelectField } from "../../../../parts/FormComponents";
import { useDispatch, useSelector } from "react-redux";
import {
  closeAllDropdowns,
  toggleDropdown,
} from "../../../../store/slices/editorSlice";
import { MdKeyboardArrowDown } from "react-icons/md";
import { BiTrash } from "react-icons/bi";
import GalleryDrawer from "./GalleryDrawer";

export const layoutIcons = {
  grid: <Grid />,
  masonry: <Masonry />,
  collage: <Collage />,
  thumbnails: <Thumbnails />,
  slideshow: <Slideshow />,
  panorama: <Panorama />,
  columns: <Columns />,
  slider: <Slider />,
};

export const imageSizeIcons = {
  50: <SizeSmall />,
  75: <SizeMedium />,
  0: <SizeFullWidth />,
  100: <SizeOriginal />,
};

export const imageAlignIcons = {
  left: <AlignLeft />,
  center: <AlignCenter />,
  right: <AlignRight />,
};

export const GalleryMenu = ({ editor, appendTo }) => {
  const menuRef = useRef(null);
  const tippyInstance = useRef(null);
  const { dropdowns } = useSelector((state) => state.editor);
  const [open, setOpen] = useState(false);
  const [caption, setCaption] = useState("");
  const [courtesy, setCourtesy] = useState("");
  const [alt, setAlt] = useState("");
  const [imageContent, setImageContent] = useState({
    layout: "grid",
    size: 100,
    imageAlign: "left",
  });
  const dispatch = useDispatch();

  const getReferenceClientRect = useCallback(() => {
    const renderContainer = getRenderContainer(editor, "node-galleryBlock");
    const rect =
      renderContainer?.getBoundingClientRect() ||
      new DOMRect(-1000, -1000, 0, 0);

    return rect;
  }, [editor]);

  const shouldShow = useCallback(() => {
    const isActive = editor.isActive("galleryBlock");
    return isActive;
  }, [editor]);

  const onImagePropChange = ({ id, value }) => {
    setImageContent({ ...imageContent, [id]: value });
  };

  const onAlignImage = useCallback(
    (alignment) => {
      editor
        .chain()
        .focus(undefined, { scrollIntoView: false })
        .setGalleryBlockAlignment(alignment)
        .run();
    },
    [editor]
  );

  const onLayoutChange = useCallback(
    (value) => {
      editor
        .chain()
        .focus(undefined, { scrollIntoView: false })
        .setGalleryBlockLayout(value)
        .run();
    },
    [editor]
  );

  const onWidthChange = useCallback(
    (value) => {
      editor
        .chain()
        .focus(undefined, { scrollIntoView: false })
        .setGalleryBlockSize(value)
        .run();
    },
    [editor]
  );

  const createTrigger = useCallback(
    (content, dropdownKey) => (
      <button
        className="p-1 hover:bg-gray-100 rounded-sm flex items-center gap-1"
        onClick={() => dispatch(toggleDropdown(dropdownKey))}
      >
        {content}
        <MdKeyboardArrowDown size={16} />
      </button>
    ),
    [dispatch]
  );

  const onRemoveBlock = useCallback(() => {
    editor.chain().focus().deleteSelection().run();
  }, [editor]);

  return (
    <>
      <BaseBubbleMenu
        editor={editor}
        pluginKey="galleryBlockMenu"
        shouldShow={shouldShow}
        updateDelay={0}
        tippyOptions={{
          interactive: true,
          offset: [0, 8],
          maxWidth: "100%",
          popperOptions: {
            modifiers: [{ name: "flip", enabled: false }],
          },
          getReferenceClientRect,
          onCreate: (instance) => {
            tippyInstance.current = instance;
          },
          appendTo: () => {
            return appendTo?.current;
          },
        }}
      >
        <div className="bubble-menu" ref={menuRef}>
          <div className="bubble-menu-icon text-fadeGray py-1 text-xs ">
            <DropdownMenu
              isOpen={dropdowns.layout}
              onSelect={(value) => {
                onLayoutChange(value);
                onImagePropChange({ id: "layout", value });
                dispatch(closeAllDropdowns());
              }}
              trigger={createTrigger(
                <button className="px-1 rounded-sm flex items-center justify-center">
                  {layoutIcons[imageContent.layout]}
                </button>,
                "layout"
              )}
            >
              <button
                value={"grid"}
                className="w-full hover:bg-blueShade px-2 py-2 whitespace-nowrap flex items-center gap-2"
              >
                <Grid /> Grid
              </button>
              <button
                value={"masonry"}
                className="w-full hover:bg-blueShade px-2 py-2 whitespace-nowrap flex items-center gap-2"
              >
                <Masonry /> Masonry
              </button>
              <button
                value={"collage"}
                className="w-full hover:bg-blueShade px-2 py-2 whitespace-nowrap flex items-center gap-2"
              >
                <Collage /> Collage
              </button>
              <button
                value={"thumbnails"}
                className="w-full hover:bg-blueShade px-2 whitespace-nowrap py-2 flex items-center gap-2"
              >
                <Thumbnails /> Thumbnails
              </button>
              <button
                value={"slideshow"}
                className="w-full hover:bg-blueShade px-2 whitespace-nowrap py-2 flex items-center gap-2"
              >
                <Slideshow /> Slideshow
              </button>
              <button
                value={"panorama"}
                className="w-full hover:bg-blueShade px-2 whitespace-nowrap py-2 flex items-center gap-2"
              >
                <Panorama /> Panorama
              </button>
              <button
                value={"columns"}
                className="w-full hover:bg-blueShade px-2 whitespace-nowrap py-2 flex items-center gap-2"
              >
                <Columns /> Columns
              </button>
              <button
                value={"slider"}
                className="w-full hover:bg-blueShade px-2 whitespace-nowrap py-2 flex items-center gap-2"
              >
                <Slider /> Slider
              </button>
            </DropdownMenu>
          </div>
          <span className="bubble-menu-divider"></span>
          <div className="bubble-menu-icon text-fadeGray py-1 text-xs ">
            <DropdownMenu
              isOpen={dropdowns.size}
              onSelect={(value) => {
                onWidthChange(parseInt(value));
                onImagePropChange({ id: "size", value });
                dispatch(closeAllDropdowns());
              }}
              trigger={createTrigger(
                <button className="px-1 rounded-sm flex items-center justify-center">
                  {imageSizeIcons[imageContent.size]}
                </button>,
                "size"
              )}
            >
              <button
                value={50}
                className="w-full hover:bg-blueShade px-2 py-2 whitespace-nowrap flex items-center gap-2"
              >
                <SizeSmall /> Small
              </button>
              <button
                value={75}
                className="w-full hover:bg-blueShade px-2 py-2 whitespace-nowrap flex items-center gap-2"
              >
                <SizeMedium /> Medium
              </button>
              <button
                value={0}
                className="w-full hover:bg-blueShade px-2 py-2 whitespace-nowrap flex items-center gap-2"
              >
                <SizeFullWidth /> Full Width
              </button>
              <button
                value={100}
                className="w-full hover:bg-blueShade px-2 whitespace-nowrap py-2 flex items-center gap-2"
              >
                <SizeOriginal /> Original Size
              </button>
            </DropdownMenu>
          </div>
          <span className="bubble-menu-divider"></span>
          <div className="bubble-menu-icon text-fadeGray py-1 text-xs ">
            {/* Dropdown Menu */}
            <DropdownMenu
              isOpen={dropdowns.imageAlignment}
              onSelect={(value) => {
                onAlignImage(value);
                onImagePropChange({ id: "imageAlign", value });
                dispatch(closeAllDropdowns());
              }}
              trigger={createTrigger(
                <button className="px-1 rounded-sm flex items-center justify-center">
                  {imageAlignIcons[imageContent.imageAlign]}
                </button>,
                "imageAlignment"
              )}
            >
              <button
                value="left"
                className="w-full hover:bg-blueShade px-2 py-2 whitespace-nowrap flex items-center gap-2"
              >
                <AlignLeft /> <span>Align left</span>
              </button>
              <button
                value="center"
                className="w-full hover:bg-blueShade px-2 py-2 whitespace-nowrap flex items-center gap-2"
              >
                <AlignCenter /> <span>Align center</span>
              </button>
              <button
                value="right"
                className="w-full hover:bg-blueShade px-2 py-2 whitespace-nowrap flex items-center gap-2"
              >
                <AlignRight /> <span>Align right</span>
              </button>
            </DropdownMenu>
          </div>
          <span className="bubble-menu-divider"></span>
          <button
            className="bubble-menu-icon text-fadeGray"
            onClick={() => setOpen(true)}
          >
            <div className="p-1 hover:bg-gray-100 rounded-sm flex items-center gap-1">
              <FiSettings />
            </div>
          </button>
          <span className="bubble-menu-divider"></span>
          <button
            className="bubble-menu-icon text-fadeGray"
            onClick={() => onRemoveBlock()}
          >
            <div className="p-1 hover:bg-gray-100 rounded-sm flex items-center gap-1">
              <BiTrash />
            </div>
          </button>
        </div>
      </BaseBubbleMenu>
      <GalleryDrawer editor={editor} open={open} setOpen={setOpen} />
    </>
  );
};

export default GalleryMenu;
