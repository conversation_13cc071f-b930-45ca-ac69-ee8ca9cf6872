import React, { useState } from "react";
import Container from "../parts/Container";
import BreadCrumb from "../parts/BreadCrumb";
import Button, { RoundedIconsButton } from "../parts/Button";
import { BiPlus } from "react-icons/bi";
import Table from "../parts/Table";
import { useDispatch, useSelector } from "react-redux";
import { FiEdit } from "react-icons/fi";
import { FaRegTrashAlt } from "react-icons/fa";
import { toast } from "react-toastify";
import ConfirmationModal from "../parts/ConfirmationModal";
import useConfirmationModal from "../utils/useConfirmationModal";
import { formatDateAndTime } from "../utils/helperFunctions";
import { setGroups } from "../store/slices/emailMarketingSlice";
import Modal from "../parts/Modal";
import { Input } from "../parts/FormComponents";
import {
  useCreateEmailGroupMutation,
  useGetEmailGroupsQuery,
  useUpdateEmailGroupMutation,
} from "../store/apis/emailMarketingApi";

const EmailGroups = () => {
  const dispatch = useDispatch();

  const { isModalOpen, rowIdToDelete, openModal, closeModal } =
    useConfirmationModal();
  const { data: groupsData, isLoading } = useGetEmailGroupsQuery();
  const [createEmailGroup, { isLoading: isCreating }] =
    useCreateEmailGroupMutation();
  const [updateEmailGroup, { isLoading: isUpdating }] =
    useUpdateEmailGroupMutation();

  // UNCOMMENT WHEN API IS READY:
  // const [deleteEmailGroup, { isLoading: isDeleting }] =
  //   useDeleteEmailGroupMutation();
  const [isDeleting, setIsDeleting] = useState(false);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [groupName, setGroupName] = useState("");
  const [groupDescription, setGroupDescription] = useState("");

  // Edit modal state
  const [showEditModal, setShowEditModal] = useState(false);
  const [editingGroup, setEditingGroup] = useState(null);
  const [editGroupName, setEditGroupName] = useState("");
  const [editGroupDescription, setEditGroupDescription] = useState("");

  // Handle edit click
  const handleEditClick = (group) => {
    setEditingGroup(group);
    setEditGroupName(group.name);
    setEditGroupDescription(group.description || "");
    setShowEditModal(true);
  };

  // Handle edit save
  const handleEditSave = () => {
    if (!editGroupName) {
      toast.error("Group name is required");
      return;
    }

    // Simulate API call
    const updatedGroup = {
      ...editingGroup,
      name: editGroupName,
      description: editGroupDescription,
    };

    updateEmailGroup({ id: editingGroup._id, groupData: updatedGroup })
      .then((res) => {
        if (res?.data?.status == "success") {
          setShowEditModal(false);
          toast.success(
            res?.data?.message || "Email contact updated successfully"
          );
        } else {
          toast.error(res?.error?.data?.message || "Something went wrong");
        }
      })
      .catch((err) => {
        toast.error("Something went wrong");
      });
  };

  // Handle delete (dummy implementation)
  const handleDelete = () => {
    setIsDeleting(true);

    // Simulate API call
    setTimeout(() => {
      // Log the ID that would be deleted
      console.log("Would delete group with ID:", rowIdToDelete);

      // Remove from local state for UI update
      const updatedGroups = groupsData.filter(
        (group) => group._id !== rowIdToDelete
      );
      dispatch(setGroups(updatedGroups));

      toast.success("Group deleted successfully!");
      setIsDeleting(false);
      closeModal();
    }, 500);

    // UNCOMMENT WHEN API IS READY:
    // deleteEmailGroup(rowIdToDelete)
    //   .then((res) => {
    //     if (res.data?.status === "success") {
    //       toast.success("Group deleted successfully!");
    //       refetch();
    //     } else {
    //       toast.error("Failed to delete group.");
    //     }
    //   })
    //   .catch((err) => {
    //     toast.error("Failed to delete group.");
    //     console.error(err);
    //   })
    //   .finally(() => {
    //     closeModal();
    //   });
  };

  // Table columns
  const columns = [
    {
      accessorKey: "name",
      id: "name",
      size: 250,
      header: () => "Group Name",
      cell: ({ row }) => {
        return (
          <div className="flex items-center gap-x-2 font-semibold">
            <div>
              <div className="line-clamp-2">{row.original.name}</div>
              <div className="text-[#969696] text-xs font-normal flex items-center space-x-2">
                <span>{formatDateAndTime(row.original?.createdAt)}</span>
              </div>
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: "description",
      header: "Description",
      minSize: 300,
      cell: ({ row }) => <div>{row.original.description}</div>,
    },
    {
      accessorKey: "contactCount",
      header: "Contacts",
      size: 100,
      cell: ({ row }) => (
        <div className="text-center">{row.original.contactCount || 0}</div>
      ),
    },
    {
      accessorKey: "actions",
      header: "Action",
      cell: ({ row }) => {
        return (
          <div className="flex items-center gap-x-2">
            <RoundedIconsButton onClick={() => handleEditClick(row.original)}>
              <FiEdit className="h-[15px] w-[15px]" />
            </RoundedIconsButton>

            {/* We can not delete group because it is depend on email contacts */}
            {/* <RoundedIconsButton
              onClick={() => {
                openModal(row.original._id);
              }}
            >
              <FaRegTrashAlt className="h-[15px] w-[15px]" />
            </RoundedIconsButton> */}
          </div>
        );
      },
    },
  ];

  // Create new group form
  const renderCreateForm = () => {
    return (
      <div className="bg-white p-5 rounded-lg border border-gray-200">
        <h2 className="text-lg font-semibold mb-4">Create New Group</h2>
        <div className="grid grid-cols-1 gap-4">
          <div>
            <Input
              label="Group Name"
              value={groupName}
              onDebouncedChange={setGroupName}
              placeholder="Enter group name"
              required={true}
            />
          </div>
          <div>
            <Input
              label="Description"
              value={groupDescription}
              onDebouncedChange={setGroupDescription}
              placeholder="Enter group description"
              isTextarea={true}
              rows={2}
            />
          </div>
        </div>
        <div className="flex justify-end mt-4 gap-2">
          <Button
            variant="secondary"
            size="sm"
            rounded="full"
            onClick={() => {
              setShowCreateForm(false);
              setGroupName("");
              setGroupDescription("");
            }}
          >
            Cancel
          </Button>
          <Button
            isLoading={isCreating}
            disabled={isCreating}
            rounded="full"
            size="sm"
            onClick={() => {
              // Handle create group logic
              if (!groupName) {
                toast.error("Group name is required");
                return;
              }
              // Create a new group with dummy data
              const newGroup = {
                name: groupName,
                description: groupDescription,
              };
              createEmailGroup(newGroup)
                .then((res) => {
                  if (res?.data?.status == "success") {
                    toast.success(
                      res?.data?.message || "Email group created successfully"
                    );
                    setShowCreateForm(false);
                    setGroupName("");
                    setGroupDescription("");
                  } else {
                    toast.error(
                      res?.error?.data?.message || "Something went wrong"
                    );
                  }
                })
                .catch((err) => {
                  toast.error("Something went wrong");
                });
            }}
          >
            Create Group
          </Button>
        </div>
      </div>
    );
  };

  return (
    <Container>
      <div className="lg:flex items-center justify-between mb-5">
        <div>
          <BreadCrumb
            title={"Email Groups"}
            description={
              "Create and manage email contact groups for targeted campaigns."
            }
          />
        </div>
        <div className="flex items-center gap-2">
          <Button
            rounded="full"
            size="sm"
            customClasses="mt-2 lg:mt-0 pb-1.5 pt-1.5"
            onClick={() => setShowCreateForm(!showCreateForm)}
          >
            <BiPlus /> <span>Create Group</span>
          </Button>
        </div>
      </div>

      {showCreateForm && renderCreateForm()}

      <Table
        module="emailGroups"
        data={groupsData?.data || []}
        actionColumn={4}
        isLoading={isLoading}
        columns={columns}
        customClass={""}
      />

      <ConfirmationModal
        isOpen={isModalOpen}
        isLoading={isDeleting}
        toggleModal={closeModal}
        message="Are you sure you want to delete this group? This will not delete the contacts in the group."
        onConfirm={handleDelete}
      />

      {/* Edit Group Modal */}
      <Modal
        isOpen={showEditModal}
        toggleModal={() => setShowEditModal(false)}
        title="Edit Email Group"
      >
        <div className="p-4">
          <div className="grid grid-cols-1 gap-4 mb-4">
            <div>
              <Input
                label="Group Name"
                value={editGroupName}
                onDebouncedChange={setEditGroupName}
                placeholder="Enter group name"
                required={true}
              />
            </div>
            <div>
              <Input
                label="Description"
                value={editGroupDescription}
                onDebouncedChange={setEditGroupDescription}
                placeholder="Enter group description"
                isTextarea={true}
                rows={3}
              />
            </div>
          </div>
          <div className="flex justify-end gap-2 mt-6">
            <Button
              variant="secondary"
              size="sm"
              rounded="full"
              isLoading={isUpdating}
              disabled={isUpdating}
              onClick={() => setShowEditModal(false)}
            >
              Cancel
            </Button>
            <Button
              isLoading={isUpdating}
              disabled={isUpdating}
              rounded="full"
              size="sm"
              onClick={handleEditSave}
            >
              Save Changes
            </Button>
          </div>
        </div>
      </Modal>
    </Container>
  );
};

export default EmailGroups;
