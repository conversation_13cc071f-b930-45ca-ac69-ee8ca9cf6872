import { fetchBaseQuery } from "@reduxjs/toolkit/query/react";

//endpoint
const backendUrl = import.meta.env.VITE_BACKENT_URL;
export const baseQuery = fetchBaseQuery({
  baseUrl: backendUrl,
  prepareHeaders: (headers, { getState }) => {
    const token = localStorage.getItem("token");
    const user = JSON.parse(localStorage.getItem("user"));
    if (token && user) {
      headers.set("login-token", "some-token");
      headers.set("authtoken", `Bearer ${token}`);
      headers.set("dbtoken", `Bearer ${user.dbToken}`);
    }
    return headers;
  },
});
