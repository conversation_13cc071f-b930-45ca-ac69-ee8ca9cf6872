import React, { useState } from "react";
import { FiArrowLeft, FiCalendar, FiClock } from "react-icons/fi";
import Button from "../../parts/Button";
import { useDispatch } from "react-redux";
import { setPublishedData } from "../../store/slices/storiesSlice";
import Outside<PERSON><PERSON><PERSON><PERSON><PERSON> from "react-outside-click-handler";
import {
  setScheduleTime,
  setStatus,
  setWebPublishedData,
} from "../../store/slices/webstorySlice";
import {
  setVideoPublishedData,
  setScheduleTime as setVideoScheduleTime,
  setStatus as setVideoStatus,
} from "../../store/slices/videoStorySlice";

const ScheduleModal = ({
  setIsPublish,
  type,
  setStatus = true,
  onClose = () => {},
}) => {
  const [selectedDate, setSelectedDate] = useState("");
  const [selectedTime, setSelectedTime] = useState("");
  const [selectedHour, setSelectedHour] = useState("");
  const dispatch = useDispatch();
  function convertToISO(dateString) {
    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) {
        throw new Error("Invalid date format");
      }
      return date.toISOString();
    } catch (error) {
      console.error(error.message);
      return null;
    }
  }

  const handleSchedulePublish = () => {
    // Convert 12-hour format to 24-hour format for the backend
    const [hour, period] = selectedHour.split(" ");
    let hour24 = parseInt(hour);

    if (period === "PM" && hour24 !== 12) {
      hour24 += 12;
    } else if (period === "AM" && hour24 === 12) {
      hour24 = 0;
    }

    const hour24String = hour24.toString().padStart(2, "0");
    const scheduledDateTime = new Date(`${selectedDate}T${hour24String}:00`);
    const isoDate = convertToISO(scheduledDateTime);

    if (setStatus) {
      if (type === "webStory") {
        dispatch(setScheduleTime({ status: 4, publishDate: isoDate }));
      } else if (type === "videoStory") {
        dispatch(setVideoScheduleTime({ status: 4, publishDate: isoDate }));
      }
    }
    onClose(isoDate);
    setIsPublish(false);
  };

  // Generate hours in 12-hour format with AM/PM
  const hours = Array.from({ length: 24 }, (_, i) => {
    const hour = i % 12 || 12; // Convert to 12-hour format
    const period = i < 12 ? "AM" : "PM";
    return `${hour} ${period}`;
  });

  return (
    <div className="fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 z-50  mt-3 bg-white border border-gray-200 shadow-md rounded-md">
      <OutsideClickHandler
        onOutsideClick={() => {
          if (!selectedDate || !selectedTime) {
            if (type === "webStory") {
              dispatch(setStatus(3));
            } else if (type === "videoStory") {
              dispatch(setVideoStatus(3));
            }
          }
          setIsPublish(false);
          onClose();
        }}
      >
        <div className="flex flex-col gap-y-5 p-10 md:min-w-96">
          <div className="flex flex-col gap-y-4">
            <div className="flex flex-col gap-y-2">
              <label className="text-sm font-medium text-gray-700">
                Select Date
              </label>
              <div className="relative">
                <input
                  type="date"
                  value={selectedDate}
                  onChange={(e) => setSelectedDate(e.target.value)}
                  className="w-full px-4 py-2 border rounded-md pr-10"
                  min={new Date().toISOString().split("T")[0]}
                />
              </div>
            </div>

            <div className="flex flex-col gap-y-2">
              <label className="text-sm font-medium text-gray-700">
                Select Time
              </label>
              <div className="relative">
                <select
                  value={selectedHour}
                  onChange={(e) => setSelectedHour(e.target.value)}
                  className="w-full px-4 py-2 border rounded-md pr-10"
                >
                  <option value="">Select hour</option>
                  {hours.map((hour) => (
                    <option key={hour} value={hour}>
                      {hour}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>

          <div className="flex items-center justify-end gap-x-4 mt-4">
            <Button
              rounded="full"
              variant="secondary"
              onClick={() => {
                setIsPublish(false);
                onClose();
              }}
              className="px-6 py-2 border border-gray-300 rounded-full hover:bg-gray-50"
            >
              Cancel
            </Button>
            <Button
              rounded="full"
              onClick={handleSchedulePublish}
              //onClick={handleSchedulePublish}   // for now just update the status of the article

              disabled={!selectedDate || !selectedHour}
              className="px-6 py-2 bg-blue-600 text-white rounded-full hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Save
            </Button>
          </div>
        </div>
      </OutsideClickHandler>
    </div>
  );
};

export default ScheduleModal;
