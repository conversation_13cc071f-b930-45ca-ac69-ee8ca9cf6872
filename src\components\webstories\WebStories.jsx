import React, { useEffect, useState } from "react";
import { BiChevronLeft, BiChevronRight, BiPlus, BiX } from "react-icons/bi";

const WebStories = ({ fetchStories, initialStories = [], onSave }) => {
  const [stories, setStories] = useState(initialStories);
  const [currentSlide, setCurrentSlide] = useState(0);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const fetchData = async () => {
      if (fetchStories) {
        setLoading(true);
        try {
          const data = await fetchStories();
          setStories(data || []);
        } catch (error) {
          console.error("Failed to fetch stories:", error);
        } finally {
          setLoading(false);
        }
      }
    };
    fetchData();
  }, [fetchStories]);

  const handleFileUpload = (event) => {
    const files = Array.from(event.target.files);
    const newStories = files.map((file) => ({
      id: Math.random().toString(36).substr(2, 9),
      image: URL.createObjectURL(file),
      title: "",
      description: "",
    }));
    setStories((prev) => [...prev, ...newStories]);
  };

  const handleTextChange = (id, field, value) => {
    setStories((prev) =>
      prev.map((story) =>
        story.id === id ? { ...story, [field]: value } : story
      )
    );
  };

  const removeStory = (id) => {
    setStories((prev) => prev.filter((story) => story.id !== id));
    if (currentSlide >= stories.length - 1) {
      setCurrentSlide(Math.max(0, stories.length - 2));
    }
  };

  const nextSlide = () => {
    setCurrentSlide((prev) => Math.min(prev + 1, stories.length - 1));
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => Math.max(prev - 1, 0));
  };

  const saveStories = async () => {
    if (onSave) {
      try {
        await onSave(stories);
        alert("Stories saved successfully!");
      } catch (error) {
        console.error("Failed to save stories:", error);
      }
    }
  };

  return (
    <div className="w-full flex gap-10 ">
      {/* Upload Section */}
      <div className="w-[60%] bg-white border border-[#c1e4fe] rounded-md mb-5">
        <div className="border-b w-full text-lg font-semibold px-5 py-5">
          Add Stories
        </div>
        <div className="p-5">
          <div
            className="flex gap-4 overflow-x-auto [&::-webkit-scrollbar]:h-2
  [&::-webkit-scrollbar-track]:bg-gray-100
  [&::-webkit-scrollbar-thumb]:bg-gray-300 pb-4"
          >
            {stories.map((story, index) => (
              <div key={story.id} className="flex-shrink-0 w-56 relative">
                <img
                  src={story.image}
                  alt={`Story ${index + 1}`}
                  className="w-full h-96 object-cover rounded-lg"
                />
                <button
                  onClick={() => removeStory(story.id)}
                  className="absolute top-2 right-2 p-1 bg-black bg-opacity-50 rounded-full hover:bg-opacity-70"
                >
                  <BiX className="w-4 h-4 text-white" />
                </button>
                <input
                  type="text"
                  placeholder="Title"
                  value={story.title}
                  onChange={(e) =>
                    handleTextChange(story.id, "title", e.target.value)
                  }
                  className="border text-sm border-gray-300 rounded-md px-4 py-2 w-full focus:outline-none focus:border-blue-500"
                />
                <textarea
                  placeholder="Description"
                  value={story.description}
                  onChange={(e) =>
                    handleTextChange(story.id, "description", e.target.value)
                  }
                  className="w-full mt-1 p-1 text-sm border rounded resize-none"
                  rows="2"
                />
              </div>
            ))}

            <label className="flex-shrink-0 w-56 h-96 border-2 border-dashed border-gray-300 rounded-lg flex flex-col items-center justify-center cursor-pointer hover:border-primary transition-colors">
              <BiPlus className="w-8 h-8 text-gray-400" />
              <span className="mt-2 text-sm text-gray-500">Add Story</span>
              <input
                type="file"
                multiple
                accept="image/*"
                className="hidden"
                onChange={handleFileUpload}
              />
            </label>
          </div>
          <button
            onClick={saveStories}
            className="mt-4 px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark"
          >
            Save Stories
          </button>
        </div>
      </div>

      {/* Preview Section */}
      <div className=" w-[40%] bg-white border border-[#c1e4fe] rounded-md mb-5">
        <div className="border-b w-full text-lg font-semibold px-5 py-5">
          Preview
        </div>
        <div className="p-5">
          {loading ? (
            <div className="text-center py-10 text-gray-500">
              Loading stories...
            </div>
          ) : stories.length > 0 ? (
            <div className="relative flex items-center justify-center">
              <button
                onClick={prevSlide}
                disabled={currentSlide === 0}
                className="absolute left-0 z-10 p-2 bg-black bg-opacity-50 rounded-full disabled:opacity-30"
              >
                <BiChevronLeft className="w-6 h-6 text-white" />
              </button>
              <div className="w-64 h-96 relative">
                <img
                  src={stories[currentSlide].image}
                  alt={`Preview ${currentSlide + 1}`}
                  className="w-full h-full object-cover rounded-lg"
                />
                <div className="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-black to-transparent text-white">
                  <h3 className="font-semibold">
                    {stories[currentSlide].title}
                  </h3>
                  <p className="text-sm mt-1">
                    {stories[currentSlide].description}
                  </p>
                </div>
              </div>
              <button
                onClick={nextSlide}
                disabled={currentSlide === stories.length - 1}
                className="absolute right-0 z-10 p-2 bg-black bg-opacity-50 rounded-full disabled:opacity-30"
              >
                <BiChevronRight className="w-6 h-6 text-white" />
              </button>
            </div>
          ) : (
            <div className="text-center py-10 text-gray-500">
              No stories to preview
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default WebStories;
