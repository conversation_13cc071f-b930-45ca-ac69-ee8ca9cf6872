import React from "react";
import { Input } from "../../parts/FormComponents";
import { useDispatch } from "react-redux";
import {
  setAuthorOgContent,
  setAuthorProfile,
} from "../../store/slices/authorsSlice";
import { useSelector } from "react-redux";

const AboutAuthors = () => {
  const dispatch = useDispatch();

  const { aboutus } = useSelector((state) => state.authors);

  const handleDataChange = ({ value, name }) => {
    dispatch(setAuthorProfile({ value, name }));
    dispatch(setAuthorOgContent({ value, name: "description" }));
  };
  return (
    <div className="border border-[#c1e4fe] rounded-md bg-white">
      <div className="border-b w-full px-5 text-lg font-semibold  py-5">
        <div>Additional Info</div>
      </div>
      <div className="px-5 py-4">
        <Input
          name="aboutus"
          customClass="!border-none !bg-transparent !focus:outline-none"
          value={aboutus}
          id="aboutus"
          placeholder="Share someting about your author "
          onDebouncedChange={(value) =>
            handleDataChange({ value, name: "aboutus" })
          }
        />
      </div>
    </div>
  );
};

export default AboutAuthors;
