import {
	flexRender,
	getCoreRowModel,
	getFilteredRowModel,
	getSortedRowModel,
	useReactTable,
} from "@tanstack/react-table";

import { useEffect, useRef, useState } from "react";
import StoriesControls from "./tableControls.jsx/StoriesControls";
import CategoriesControls from "./tableControls.jsx/CategoriesControls";
import EmailControls from "./tableControls.jsx/EmailControls";
import { BsDatabaseExclamation } from "react-icons/bs";

// Custom hook for intersection observer
export const useIntersectionObserver = (callback, options = {}) => {
	const targetRef = useRef(null);
	const observerRef = useRef(null);

	useEffect(() => {
		const target = targetRef.current;

		// Cleanup previous observer
		if (observerRef.current) {
			observerRef.current.disconnect();
		}

		// Create new observer
		observerRef.current = new IntersectionObserver(
			(entries) => {
				const [entry] = entries;
				if (entry.isIntersecting) {
					callback();
				}
			},
			{
				threshold: 0,
				rootMargin: "200px",
				...options,
			}
		);

		// Start observing
		if (target) {
			observerRef.current.observe(target);
		}

		// Cleanup on unmount
		return () => {
			if (observerRef.current) {
				observerRef.current.disconnect();
			}
		};
	}, [callback, options]);

	return targetRef;
};

export default function Table({
	handleRowSelectionChange = () => {},
	fetchMoreData = () => {},
	module = "stories",
	columns = [],
	isLoading = false,
	data = [],
	customClass = null,
	enableMultiRowSelection = true,
	enableRowSelection = true,
	isFetching = false,
	actionColumn = null,
	hasMore = true,
	showOnlyAll = false,
}) {
	const [columnFilters, setColumnFilters] = useState([]);
	const [rowSelection, setRowSelection] = useState({});
	const lastFetchTime = useRef(Date.now());

	// Throttled fetch function
	const handleFetchMore = () => {
		const now = Date.now();
		const timeSinceLastFetch = now - lastFetchTime.current;
		const THROTTLE_DELAY = 1000; // 1 second throttle

		if (timeSinceLastFetch >= THROTTLE_DELAY && !isFetching && hasMore) {
			lastFetchTime.current = now;
			fetchMoreData();
		}
	};

	// Setup intersection observer for the loading trigger
	const loadingTriggerRef = useIntersectionObserver(handleFetchMore, {
		threshold: 0,
		rootMargin: "200px",
	});

	// Helper function to get selected row IDs
	const getSelectedRowIds = (currentSelection) => {
		return data.filter((row, index) => currentSelection[index]).map((row) => row._id);
	};

	const table = useReactTable({
		data,
		columns,
		filterFns: {},
		state: {
			columnFilters,
			rowSelection,
		},
		onColumnFiltersChange: setColumnFilters,
		getCoreRowModel: getCoreRowModel(),
		getFilteredRowModel: getFilteredRowModel(),
		getSortedRowModel: getSortedRowModel(),
		enableRowSelection: enableRowSelection,
		onRowSelectionChange: (updatedRowSelection) => {
			setRowSelection(updatedRowSelection);
			const selectedIds = getSelectedRowIds(updatedRowSelection);
			const isBulkSelected =
				Object.keys(updatedRowSelection).length > 0 &&
				Object.keys(updatedRowSelection).length === data.length &&
				Object.values(updatedRowSelection).every((selected) => selected === true);
			handleRowSelectionChange({
				isBulkSelected,
				selectedIds,
			});
		},
		enableMultiRowSelection: enableMultiRowSelection,
		columnResizeMode: "onChange",
	});

	const controls = {
		stories: <StoriesControls module={module} showOnlyAll={showOnlyAll} />,
		videoStory: <StoriesControls module={module} />,
		categories: <CategoriesControls module={module} />,
		authors: <CategoriesControls module={module} />,
		flaggedStory: <CategoriesControls module={module} />,
		webStory: <StoriesControls module={module} />,
		tags: <CategoriesControls module={module} />,
		subcategories: <StoriesControls module="categories" />,
		emailContacts: <EmailControls module={module} showOnlyAll={showOnlyAll} />,
		emailGroups: <EmailControls module={module} showOnlyAll={true} />,
		emailCampaigns: <EmailControls module={module} showOnlyAll={true} />,
	};

	// Handle row click with status column exception
	const handleRowClick = (event, row) => {
		const isStatusColumn = actionColumn || event.target.closest("td")?.cellIndex === 5;
		if (!isStatusColumn) {
			row.getToggleSelectedHandler()(event);
		}
	};

	return (
		<div className="border border-[#e1e1e1] relative rounded-xl calculateHeight overflow-y-auto scrollbar bg-white overflow-x-scroll">
			{controls[module] || null}
			<table
				className={`w-full text-black h-full md:mt-0 ${customClass} text-sm bg-white relative min-h-32`}
			>
				<thead className="sticky top-[2.95rem] z-10">
					{table.getHeaderGroups().map((headerGroup) => (
						<tr key={headerGroup.id}>
							{headerGroup.headers.map((header) => (
								<th
									key={header.id}
									colSpan={header.colSpan}
									style={{ width: header.column.getSize() }}
									className="text-start font-semibold bg-[#c3e5ff] first:pl-4 last:pr-4 py-3"
								>
									{header.isPlaceholder ? null : (
										<div
											className={header.column.getCanSort() ? "cursor-pointer select-none" : ""}
											onClick={header.column.getToggleSortingHandler()}
										>
											{flexRender(header.column.columnDef.header, header.getContext())}
											{{}[header.column.getIsSorted()] ?? null}
										</div>
									)}
								</th>
							))}
						</tr>
					))}
				</thead>
				<tbody className="-z-10">
					{table.getRowModel().rows.map((row, index) => (
						<tr
							key={row.id}
							className={`${
								row.getIsSelected() ? "selected" : ""
							} hover:cursor-pointer hover:bg-blueShade transition-all duration-100`}
							onClick={(e) => handleRowClick(e, row)}
						>
							{row.getVisibleCells().map((cell) => (
								<td key={cell.id} className="first:pl-4 text-[14px]">
									{flexRender(cell.column.columnDef.cell, cell.getContext())}
								</td>
							))}
						</tr>
					))}
				</tbody>
			</table>

			{/* Loading and No Data States */}
			{!isLoading && data.length === 0 && (
				<div className="absolute text-black w-full inset-0 flex flex-col top-28 items-center justify-center z-10">
					<BsDatabaseExclamation className="text-primary text-4xl font-medium" />
					<span>No Data Found</span>
				</div>
			)}

			{/* Infinite Scroll Loading Trigger */}
			{hasMore && (
				<div ref={loadingTriggerRef} className="h-4 w-full">
					{isFetching && (
						<div className="text-center py-4">
							<div className="animate-spin rounded-full border-4 border-gray-200 border-t-primary h-8 w-8 mx-auto" />
						</div>
					)}
				</div>
			)}

			{/* Main Loading Spinner */}
			{isLoading && (
				<div className="text-center min-h-10 w-full pb-10">
					<div className="animate-spin rounded-full border-4 border-gray-200 border-t-primary h-8 w-8 mx-auto" />
				</div>
			)}
		</div>
	);
}
