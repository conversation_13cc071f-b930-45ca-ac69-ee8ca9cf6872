import React, { forwardRef } from "react";

const Button = forwardRef(
  (
    {
      children,
      type = "button",
      variant = "primary", // 'primary', 'secondary', 'outline'
      size = "md", // 'sm', 'md', 'lg'
      rounded = "md", // 'none', 'sm', 'md', 'lg', 'full'
      fullWidth = false,
      customClasses = "",
      ...props
    },
    ref
  ) => {
    // Base styles
    const baseStyles = `inline-flex items-center justify-center focus:outline-none transition duration-300 ease-in-out ${
      fullWidth ? "w-full" : ""
    }`;

    // Variant styles
    const variantStyles = {
      primary: "bg-[#3b82f6] text-white hover:bg-[#3475de]",
      secondary:
        "bg-white text-primary hover:text-white hover:bg-[#3b82f6] border border-[#3b82f6]",
      outline:
        "border border-[#3b82f6] hover:text-[#3b82f6] hover:border-[#3b82f6]",
      danger: "bg-red-600 border border-red-600 text-white hover:bg-red-700",
    };

    // Size styles
    const sizeStyles = {
      sm: "px-3 py-1.5 text-sm",
      md: "px-4 py-2 text-base",
      lg: "px-6 py-3 text-lg",
    };

    // Rounded styles
    const roundedStyles = {
      none: "rounded-none",
      sm: "rounded-sm",
      md: "rounded-md",
      lg: "rounded-lg",
      full: "rounded-full",
    };

    return (
      <button
        ref={ref}
        {...props}
        type={type}
        className={`${baseStyles} ${variantStyles[variant]} ${sizeStyles[size]} ${roundedStyles[rounded]} ${customClasses}  "disabled:opacity-50 disabled:cursor-not-allowed disabled:bg-[#0006244d] transition-colors  duration-150"
        `}
      >
        {children}
      </button>
    );
  }
);

export default Button;


export const RoundedIconsButton = ({ children, ...props }) => {
  return (
    <button
      {...props}
      className="flex items-center justify-center p-1.5 rounded-full bg-[#e6f4ff] text-primary hover:bg-primary hover:text-white"
    >
      {children}
    </button>
  );
};