import React, { useCallback, useEffect, useState } from "react";
import { BiChevronRight } from "react-icons/bi";
import { Link, useNavigate, useParams } from "react-router-dom";
import Container from "../parts/Container";
import Button from "../parts/Button";
import AddYoutube from "../components/videos/AddVideo";
import AddVideo from "../components/videos/AddVideo";
import VideoSEO from "../components/videos/VideoSEO";
import Flags from "../components/stories/Flags";
import Categories from "../components/stories/Categories";
import VideoInfo from "../components/videos/VideoInfo";
import Thumbnail from "../components/videos/Thumbnail";
import {
	useCreateVideoStoryMutation,
	useEditVideoStoryMutation,
	useGetSingleVideoStoryQuery,
} from "../store/apis/videoStoryApi";
import { useDispatch, useSelector } from "react-redux";
import {
	resetLocalVideoData,
	resetYoutubeVideoData,
	setLocalVideo,
	setVideoStoryData,
	setYoutubeStatus,
	setYoutubeVideo,
} from "../store/slices/videoStorySlice";
import { toast } from "react-toastify";
import Loader from "../parts/Loader";
import WebStoryInfo from "../components/webstories/WebStoryInfo";
import WebStorySEO from "../components/webstories/WebStorySEO";
import WebstoryTopSection from "../components/webstories/WebstoryTopSection";
import {
	clearErrors,
	resetState,
	setErrors,
	setwebStoriesData,
} from "../store/slices/webstorySlice";
import {
	useCreateWebStoryMutation,
	useEditWebStoryMutation,
	useGetSingleWebStoryQuery,
} from "../store/apis/webStoryApi";
import Calendar from "../parts/Calendar";
import { areAllKeywordsPresent } from "../components/stories/TextEditorToolbar";

const AddEditVideoStory = ({ method }) => {
	const dispatch = useDispatch();
	const navigate = useNavigate();

	const { id } = useParams();
	const {
		croppedImg,
		storiesState: {
			title,
			subcategory,
			section,
			writer,
			meta,
			ogImg,
			webStorySlides,
			twitterImg,
			twitter,
			og,
		},
		storiesState,
		errors,
		status,
		coverImg,
	} = useSelector((state) => state.webStory);
	const { data, isLoading, isError, error } = useGetSingleWebStoryQuery(id, {
		skip: !id,
	});

	// need to change here for the webstory to work
	const [createWebStory, { isLoading: createLoading, errro: createError }] =
		useCreateWebStoryMutation();
	const [editWebStory, { isLoading: editLoading, errro: editError }] = useEditWebStoryMutation();

	const [webStoryData, setWebStoryData] = useState({
		slides: [],
		selectedFiles: [],
	});
	const handleWebStoryDataChange = useCallback(
		(data) => {
			setWebStoryData(data);
		},
		[data]
	);

	// displaying the error message here
	// displaying the error message here
	if (isError) {
		if (error.status === 401) {
			toast.error("Session Expired", { toastId: "web_edit_session_expired" });
			navigate("/signin");
		} else {
			if (error.status === 404) {
				navigate(-1);
			}
			toast.error(error.data.message, { toastId: "get_single_web_error" });
		}
	}

	// clear the edit data when component unmounts
	useEffect(() => {
		if (data) {
			dispatch(setwebStoriesData(data));
		}

		return () => {
			if (data) {
				dispatch(resetState());
			}
		};
	}, [data]);

	const validateStory = (slides) => {
		const newErrors = {};
		if (!title) newErrors.title = "Title is required";
		if (!slides.length) newErrors.slides = "At least one slide is required";
		if (!slides.length) {
			toast.error("At least one slide is required", { toastId: "add_error" });
		}
		if (!writer.length) newErrors.writer = "At least one writer is required";
		if (!subcategory) newErrors.subcategory = "Category is required";
		return newErrors;
	};

	const sanitizeValue = (value) => {
		if (!value || (Array.isArray(value) && value.length === 0) || value === "") {
			return null;
		}
		return value;
	};

	const handleSave = () => {
		const { slides, coverImg } = webStoryData;
		const validationErrors = validateStory(slides);
		console.log(validationErrors, " validation error");
		if (!areAllKeywordsPresent({ title, meta })) {
			return;
		}
		if (Object.keys(validationErrors).length > 0) {
			dispatch(setErrors(validationErrors));
		} else {
			const formData = new FormData();
			dispatch(clearErrors());
			const payload = {
				status,
				subcategory: storiesState.subcategory,
				section: storiesState.section,
				writer: storiesState.writer,
				title: storiesState.title,
				publishDate: sanitizeValue(storiesState.publishDate),
				slug: `/${storiesState.meta.slug}`,
				webStorySlides: slides.map((slide, index) => ({
					title: slide.title,
					content: slide.content,
					status: 1,
					altName: slide.altName || slide.title,
					contributor: slide.contributor,
					sequence: index,
				})),
				meta: {
					...storiesState.meta,
					og: {
						title: storiesState.og.title,
						description: storiesState.og.description,
					},
					twitter: {
						title: storiesState.twitter.title,
						description: storiesState.twitter.description,
						card: storiesState.twitter.card === "large" ? "summary_large_image" : "summary",
					},
				},
			};
			// Helper function to check if a value is a string URL
			const isStringUrl = (value) => typeof value === "string";
			const isFile = (value) => {
				// Return false for empty values or arrays
				if (!value || (Array.isArray(value) && value.length === 0)) return false;

				// Check if value is a Blob or File
				if (value instanceof Blob || value instanceof File) return false;

				// Check if value is a string (URL or path)
				if (typeof value === "string") return true;

				return false;
			};

			// Handle cover image
			if (isStringUrl(coverImg)) {
				payload.coverImg = coverImg; // Add URL to the payload
			} else if (coverImg) {
				formData.append("coverImg", coverImg[0]); // Add file to FormData
			}

			// Handle Open Graph (og) image
			if (isStringUrl(ogImg)) {
				payload.meta.og.image = ogImg; // Add URL to the payload
			} else if (ogImg.length > 0) {
				formData.append("ogImg", ogImg[0]); // Add file to FormData
			}

			// Handle Twitter image
			if (isStringUrl(twitterImg)) {
				payload.meta.twitter.image = twitterImg; // Add URL to the payload
			} else if (twitterImg.length > 0) {
				formData.append("twitterImg", twitterImg[0]); // Add file to FormData
			}

			if (isFile(croppedImg) && croppedImg) {
				payload.meta.croppedImg = croppedImg;
			}

			if (
				!isFile(croppedImg) &&
				croppedImg &&
				!(Array.isArray(croppedImg) && croppedImg.length === 0)
			) {
				formData.append("croppedImg", croppedImg[0]);
			}

			if (slides.length > 0) {
				for (let i = 0; i < slides.length; i++) {
					const slide = slides[i];
					if (isStringUrl(slide.coverImg)) {
						payload.webStorySlides[i].coverImg = slide.coverImg; // Add URL to the payload
						payload.webStorySlides[i].webStoryId = slide.webStoryId ?? slide.webStoryId;
					} else if (slide.coverImg.length > 0) {
						formData.append(`coverImgSlide${i}`, slide.coverImg[0]); // Add file to FormData
					}
				}
			}

			formData.append("data", JSON.stringify(payload));

			if (method === "POST") {
				createWebStory(formData)
					.then((res) => {
						if (res.data.status === "success") {
							toast.success("Web Story Created Successfully", {
								toastId: "web_story_creation_successfull",
							});
							if (parseInt(status, 10) === 1 || parseInt(status, 10) === 4) {
								navigate("/admin/web-stories?status=all");
								window.close();
							}
						} else {
							toast.error(res.data.message, {
								toastId: "web_story_creation_fail",
							});
						}
					})
					.catch((err) => {
						console.log("error", err);
						toast.error(err.message, {
							toastId: "web_story_creation_fail1",
						});
					});
			} else {
				// console.log(...formData, " final fomr data");
				editWebStory({ formData, id })
					.then((res) => {
						if (res.data.status === "success") {
							toast.success("Web Story Updated Successfully", {
								toastId: "web_story_updation_successfull",
							});
							if (parseInt(status, 10) === 1 || parseInt(status, 10) === 4) {
								navigate("/admin/web-stories?status=all");
								window.close();
							}
						} else {
							toast.error(res.message, {
								toastId: "web_story_updation_fail",
							});
						}
					})
					.catch((err) => {
						toast.error(err.message, {
							toastId: "web_story_updation_fail1",
						});
						console.log(err);
					});
			}
		}
	};

	if (isLoading) {
		return <Loader />;
	}
	const handleCancel = () => navigate("/admin/web-stories?status=all");
	return (
		<Container>
			<div className="flex w-full items-center justify-between mb-0">
				<div>
					<div className="flex items-center text-xl lg:text-2xl font-semibold">
						<Link
							to={"/admin/web-stories?status=all"}
							className=" py-1 bg-transparent text-[#b4b4b4] font-normal transition-all duration-150 rounded-full pr-3 hover:text-black"
						>
							Web Stories
						</Link>
						<BiChevronRight className="text-3xl font-normal text-gray-400" />
						<p className="text-fadeGray pl-2">{title || "Untitled Story"}</p>
					</div>
					<p className="pl-3 mt-1 text-sm">Manage what info is shown on your web stories.</p>
				</div>
				<div className="flex items-center gap-x-3">
					<Button
						rounded="full"
						variant="secondary"
						size="sm"
						customClasses="py-[7px]"
						onClick={handleCancel}
					>
						Cancel
					</Button>
					<Button size="sm" customClasses="py-[7px]" rounded="full" onClick={handleSave}>
						{createLoading || editLoading ? "Please Wait..." : "Save"}
					</Button>
				</div>
			</div>

			<WebStoryInfo
				onDataChange={handleWebStoryDataChange}
				data={data ? data : {}}
				method={method}
			/>
			<div className="mt-4 px-2 flex gap-x-5">
				<div className="w-[60%] flex flex-col gap-y-5">
					{/* <AddVideo active={active} /> */}

					<div className=" bg-white border border-[#c1e4fe] rounded-md">
						<div className=" border-b w-full  text-lg font-semibold px-5 py-5">Categories</div>
						<div className="py-5 px-5">
							<Categories isVideo={false} type="webStory" />
						</div>
					</div>
					{/* <div className="">
            <div className="py-5 border-b w-full px-5 text-lg font-semibold ">
              Video Info
            </div>
          </div> */}
				</div>
				<div className="flex flex-col gap-y-5 w-[40%]">
					{/* <Thumbnail /> */}
					<WebStorySEO />
					<div className="bg-white rounded-md border border-[#c1e4fe]">
						<div className="border-b w-full px-5 text-lg font-semibold  py-5">Flags</div>
						<div className="px-4 py-5">
							<Flags type="webStory" />
						</div>
					</div>
				</div>
			</div>
		</Container>
	);
};

export default AddEditVideoStory;
