import React, { useState, useMemo } from "react";
import { useDropzone } from "react-dropzone";
import { MdDeleteOutline } from "react-icons/md";
import { RoundedIconsButton } from "../../../../parts/Button";
import { GoPlus } from "react-icons/go";
import { LiaExchangeAltSolid } from "react-icons/lia";

const ImageDropzone = ({
  selectedFiles,
  setSelectedFiles,
  customClasses = null,
  customHeight1 = null,
  customImgClass = null,
}) => {
  const [error, setError] = useState(null);

  const onDrop = (acceptedFiles) => {
    const maxSize = 700 * 1024;

    const validFiles = acceptedFiles.filter((file) => file.size <= maxSize);
    const oversizedFiles = acceptedFiles.filter((file) => file.size > maxSize);

    if (oversizedFiles.length > 0) {
      setError("Some files exceed the 700KB size limit and were not added.");
    } else {
      setError(null);
    }

    // Append new files to existing ones if needed
    setSelectedFiles((prevFiles) => [...prevFiles, ...validFiles]);
  };

  const {
    getRootProps,
    getInputProps,
    isFocused,
    isDragAccept,
    isDragReject,
    isDragActive,
    open,
  } = useDropzone({
    multiple: true,
    accept: { "image/*": [] },
    onDrop,
  });

  const replaceFile = (index) => {
    setSelectedFiles((prevFiles) => {
      const updatedFiles = [...prevFiles];
      updatedFiles.splice(index, 1);
      return updatedFiles;
    });
    open(); // call your file picker
  };

  // Remove a file at a specific index
  const removeFile = (index) => {
    setSelectedFiles((prevFiles) => {
      const updatedFiles = [...prevFiles];
      updatedFiles.splice(index, 1);
      return updatedFiles;
    });
  };

  const baseStyle = {
    flex: 1,
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    justifyContent: "center",
    padding: "20px",
    width: "100%",
    aspectRatio: 1,
    borderWidth: 2,
    borderRadius: 5,
    borderColor: "#3b82f6",
    backgroundColor: "#f4f7ff",
    color: "#bdbdbd",
    outline: "none",
    cursor: "pointer",
    transition: "border .24s ease-in-out",
  };

  const focusedStyle = {
    borderColor: "fc6e00",
  };

  const acceptStyle = {
    borderColor: "#00e676",
  };

  const rejectStyle = {
    borderColor: "#ff1744",
  };

  const style = useMemo(
    () => ({
      ...baseStyle,
      ...(isFocused ? focusedStyle : {}),
      ...(isDragAccept ? acceptStyle : {}),
      ...(isDragReject ? rejectStyle : {}),
    }),
    [isFocused, isDragAccept, isDragReject]
  );

  return (
    <>
      <div>
        <div {...getRootProps({ style })}>
          <div className="border border-primary border-dashed w-full h-full flex items-center justify-center">
            <input {...getInputProps()} />
            {isDragActive ? (
              <p>Drop the files here ...</p>
            ) : (
              <GoPlus className="text-4xl" />
            )}
          </div>
        </div>
        {error && <div className="text-sm text-red-400 mt-2">{error}</div>}
      </div>
      {selectedFiles && selectedFiles.length > 0 && (
        <>
          {selectedFiles.map((file, index) => {
            return (
              <div
                className="relative flex justify-center group"
                key={`gallery-image-${index}`}
              >
                <div className="hidden group-hover:block transition-all duration-200">
                  <div className="inset-0 flex justify-center h-full w-full bg-slate-600 bg-opacity-50 absolute top-0 left-0 z-20">
                    <div className="flex items-center gap-x-4 z-30">
                      <RoundedIconsButton onClick={() => replaceFile(index)}>
                        <LiaExchangeAltSolid
                          title="Replace"
                          className="text-xl"
                        />
                      </RoundedIconsButton>
                      <RoundedIconsButton onClick={() => removeFile(index)}>
                        <MdDeleteOutline className="text-xl" title="Delete" />
                      </RoundedIconsButton>
                    </div>
                  </div>
                </div>
                <div className={`${customClasses}`}>
                  <img
                    src={
                      typeof file === "string"
                        ? file // Use URL directly
                        : URL.createObjectURL(file) // For local files
                    }
                    alt="Selected file"
                    style={{
                      height: customHeight1 ? customHeight1 : "180px", // Default height
                      ...((typeof customImgClass === "object" &&
                        customImgClass) ||
                        {}), // Merge optional styles
                    }}
                  />
                </div>
              </div>
            );
          })}
        </>
      )}
    </>
  );
};

export default ImageDropzone;
