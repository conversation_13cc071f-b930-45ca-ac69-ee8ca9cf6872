import React from "react";

const Panorama = ({
  width = 20,
  height = 20,
  fontSize,
  strokeWidth = 1,
  fill = "currentColor",
  stroke,
  ...rest
}) => {
  const style = fontSize ? { fontSize } : undefined;
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 50 50"
      fill={fill}
      stroke={stroke}
      strokeWidth={strokeWidth}
      style={style}
      xmlns="http://www.w3.org/2000/svg"
      {...rest}
    >
      <path
        fill={fill}
        d="M3 2h44a1 1 0 0 1 1 1v14a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V3a1 1 0 0 1 1-1zm0 1v14h44V3H3zm0 17h44a1 1 0 0 1 1 1v7a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1v-7a1 1 0 0 1 1-1zm0 1v7h44v-7H3zm0 10h44a1 1 0 0 1 1 1v15a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V32a1 1 0 0 1 1-1zm0 1v15h44V32H3z"
      ></path>
    </svg>
  );
};

export default Panorama;
