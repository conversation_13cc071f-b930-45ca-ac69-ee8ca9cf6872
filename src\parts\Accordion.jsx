import React, { createContext, useContext, useState } from "react";
import { FiChevronDown, FiChevronUp } from "react-icons/fi";

const AccordionContext = createContext();

export const AccordionProvider = ({ children }) => {
	const [openIndex, setOpenIndex] = useState(null);

	const toggleAccordion = (index) => {
		setOpenIndex((prevIndex) => (prevIndex === index ? null : index));
	};

	return (
		<AccordionContext.Provider value={{ openIndex, toggleAccordion }}>
			{children}
		</AccordionContext.Provider>
	);
};

const useAccordion = () => useContext(AccordionContext);

function Accordion({ title, children, index, customClass = null }) {
	const { openIndex, toggleAccordion } = useAccordion();
	const isOpen = openIndex === index;

	return (
		<div className={`py-2 px-2 md:px-4 ${customClass}`}>
			<h2>
				<button
					type="button"
					className="grid grid-cols-12 w-full text-left font-semibold py-2 focus:outline-none acti:outline-none transition-colors duration-700"
					onClick={() => toggleAccordion(index)}
					aria-expanded={isOpen}
				>
					<span className="col-span-11">{title}</span>
					<div className="col-span-1 transition-all duration-300">
						{isOpen ? (
							<FiChevronUp className="text-primary text-xl" />
						) : (
							<FiChevronDown className="text-primary text-xl" />
						)}
					</div>
				</button>
			</h2>
			{isOpen && (
				<div className="text-sm text-slate-600 py-2 transition-opacity duration-300 ease-in-out">
					{children}
				</div>
			)}
		</div>
	);
}

export default Accordion;
