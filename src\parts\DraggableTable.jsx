import React, { useState, useMemo, useEffect, useRef } from "react";
import {
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
} from "@dnd-kit/core";
import { restrictToVerticalAxis } from "@dnd-kit/modifiers";
import {
  arrayMove,
  sortableKeyboardCoordinates,
  SortableContext,
  useSortable,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { MdDragIndicator } from "react-icons/md";
import StoriesControls from "./tableControls.jsx/StoriesControls";
import Loader from "./Loader";
import { BsDatabaseExclamation } from "react-icons/bs";
import { resetFilters } from "../store/slices/flaggedStoriesSlice";
import { useDispatch } from "react-redux";

// DragHandle Component
const DragHandle = ({ rowId }) => {
  const { attributes, listeners } = useSortable({
    id: rowId,
  });

  return (
    <div {...attributes} {...listeners} className="cursor-grab">
      <MdDragIndicator className="text-primary text-3xl" />
    </div>
  );
};


const DraggableRow = ({ row, handleRowClick }) => {
  const { transform, transition, setNodeRef, isDragging } = useSortable({
    id: row.original._id,
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.8 : 1,
    backgroundColor: isDragging ? "#f3f4f6" : "transparent",
    position: "relative",
  };

  return (
    <tr
      ref={setNodeRef}
      style={style}
      className={`${
        row.getIsSelected() ? "selected" : ""
      } hover:cursor-pointer hover:bg-blueShade transition-all duration-100}
      onClick={(e) => handleRowClick(e, row)`}
    >
      {row.getVisibleCells().map((cell) => (
        <td key={cell.id} className="first:pl-4">
          {flexRender(cell.column.columnDef.cell, cell.getContext())}
        </td>
      ))}
    </tr>
  );
};
const DraggableTable = ({
  handleRowSelectionChange = () => {},
  fetchMoreData = () => {},
  module = "stories",
  columns = [],
  handleRowClick = () => {},
  isLoading = false,
  data = [],
  customClass = null,
  enableMultiRowSelection = true,
  enableRowSelection = true,
  isFetching = false,
  onReorder = () => {},
  level = 1, // Pass level as a prop
}) => {
  const [tableData, setTableData] = useState(data);
  const [columnFilters, setColumnFilters] = useState([]);
  const [rowSelection, setRowSelection] = useState({});
  const lastRowRef = useRef(null);
  const observerRef = useRef(null);

  useEffect(() => {
    setTableData(data);
  }, [data]);

  useEffect(() => {
    const options = {
      root: null,
      rootMargin: "20px",
      threshold: 0.5,
    };

    const handleIntersect = (entries) => {
      const [entry] = entries;
      if (entry.isIntersecting && !isFetching && data.length > 0) {
        fetchMoreData();
      }
    };

    observerRef.current = new IntersectionObserver(handleIntersect, options);

    if (lastRowRef.current) {
      observerRef.current.observe(lastRowRef.current);
    }

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [isFetching, data.length]);

  const dataIds = useMemo(() => tableData.map((item) => item._id), [tableData]);

  // Enhanced columns with drag handle, conditionally add drag column
  const enhancedColumns = useMemo(() => {
    if (level === 2) {
      return [
        {
          id: "drag",
          size: 60,
          cell: ({ row }) => <DragHandle rowId={row.original._id} />,
        },
        ...columns,
      ];
    }
    return columns;
  }, [columns, level]);

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  const handleDragEnd = (event) => {
    const { active, over } = event;
    if (active.id !== over?.id) {
      setTableData((items) => {
        const oldIndex = items.findIndex((item) => item._id === active.id);
        const newIndex = items.findIndex((item) => item._id === over.id);
        const newOrder = arrayMove(items, oldIndex, newIndex);
        onReorder(newOrder);
        return newOrder;
      });
    }
  };

  const table = useReactTable({
    data: tableData,
    columns: enhancedColumns,
    state: {
      columnFilters,
      rowSelection,
    },
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getSortedRowModel: getSortedRowModel(),
    enableRowSelection,
    onRowSelectionChange: (updatedSelection) => {
      setRowSelection(updatedSelection);
      handleRowSelectionChange(updatedSelection);
    },
    enableMultiRowSelection,
    getRowId: (row) => row._id,
  });

  return (
    <div
      className={`border border-[#e1e1e1] relative calculateHeight overflow-y-auto scrollbar bg-white overflow-x-scroll`}
    >
      {level === 2 ? (
        <DndContext
          sensors={sensors}
          collisionDetection={closestCenter}
          modifiers={[restrictToVerticalAxis]}
          onDragEnd={handleDragEnd}
        >
          <SortableContext
            items={dataIds}
            strategy={verticalListSortingStrategy}
          >
            {renderTable()}
          </SortableContext>
        </DndContext>
      ) : (
        renderTable()
      )}
    </div>
  );

  function renderTable() {
    return (
      <>
        <StoriesControls module="flaggedStory" />
        <table
          className={`w-full text-black h-full md:mt-0 ${customClass} text-sm bg-white min-h-32`}
        >
          <thead className="sticky z-10 top-[2.95rem]">
            {table.getHeaderGroups().map((headerGroup) => (
              <tr key={headerGroup.id} className="border-b">
                {headerGroup.headers.map((header) => (
                  <th
                    key={header.id}
                    className="text-start font-semibold bg-[#c3e5ff] first:pl-4 last:pr-4 py-3"
                  >
                    {header.isPlaceholder
                      ? null
                      : flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                  </th>
                ))}
              </tr>
            ))}
          </thead>
          <tbody className="-z-10">
            {table.getRowModel().rows.map((row, index) => (
              <DraggableRow
                key={row.original._id}
                row={row}
                handleRowClick={handleRowClick}
                ref={
                  index === table.getRowModel().rows.length - 1
                    ? lastRowRef
                    : null
                }
              />
            ))}
            {!isLoading && data.length === 0 ? (
              <div className="absolute text-black w-full inset-0 flex flex-col top-28 items-center justify-center z-10">
                <BsDatabaseExclamation className="text-primary text-4xl font-medium" />
                <span>No Data Found</span>
              </div>
            ) : null}
            {!isLoading && isFetching && (
              <tr>
                <td colSpan="full" className="text-center">
                  Loading more...
                </td>
              </tr>
            )}
          </tbody>
        </table>
        {isLoading && (
          <div className="text-center min-h-10 w-full pb-10">
            <div
              className={`animate-spin rounded-full border-4 border-gray-200 border-t-primary h-8 w-8 mx-auto`}
            />
          </div>
        )}
      </>
    );
  }
};

export default DraggableTable;
