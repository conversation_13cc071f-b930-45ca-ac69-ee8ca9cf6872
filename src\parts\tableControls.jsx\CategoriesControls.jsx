import React from "react";
import { DebouncedInput } from "../FormComponents";
import { useDispatch, useSelector } from "react-redux";
import { storiesInputFilter } from "../../store/slices/storiesSlice";
import { categoriesInputFilter } from "../../store/slices/categoriesSlice";
import { setAuthorsInutFilter } from "../../store/slices/authorsSlice";
import { flaggedStoryInputFilter } from "../../store/slices/flaggedStoriesSlice";
import { setTagsInputFilter } from "../../store/slices/tagsSlice";

const CategoriesControls = ({ module }) => {
  const dispatch = useDispatch();
  const {
    filter: { search },
  } = useSelector((state) => state[module]);
  // Click handler to set the active state and make API call
  return (
    <div className="px-5 gap-3 md:gap-0 flex flex-col md:flex-row pb-5 pt-3 md:pb-2 bg-white items-center w-full justify-between sticky top-0 z-20">
      <DebouncedInput
        customClass="!w-1/2"
        type="text"
        value={search ?? ""}
        onChange={(value) => {
          if (module === "categories" || module === "subcategories") {
            dispatch(categoriesInputFilter(value));
          }
          if (module === "authors") {
            dispatch(setAuthorsInutFilter(value));
          }
          if (module === "flaggedStory") {
            dispatch(flaggedStoryInputFilter(value));
          }
          if (module === "tags") {
            dispatch(setTagsInputFilter(value));
          }
        }}
        placeholder="Search..."
        className="border shadow rounded w-full md:w-auto"
      />
    </div>
  );
};

export default CategoriesControls;
