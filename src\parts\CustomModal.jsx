import { useEffect } from "react";
import { BiX } from "react-icons/bi";

const CustomModal = ({
  isOpen,
  onClose,
  children,
  title,
  size = "md", // sm, md, lg, xl
}) => {
  // Lock body scroll when modal is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "unset";
    }
    return () => {
      document.body.style.overflow = "unset";
    };
  }, [isOpen]);

  if (!isOpen) return null;

  // Size classes map
  const sizeClasses = {
    sm: "max-w-md",
    md: "max-w-lg",
    lg: "max-w-2xl",
    xl: "max-w-5xl",
  };

  return (
    <>
      {/* Backdrop */}
      <div className="fixed inset-0 bg-black bg-opacity-50 z-50">
        {/* Modal Container */}
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
          {/* Modal Content */}
          <div
            className={`${sizeClasses[size]} w-full bg-white rounded-lg shadow-xl`}
          >
            {/* Header */}
            <div className="flex items-center justify-between p-4 border-b">
              <h2 className="text-xl font-semibold">{title || ""}</h2>
              <button
                onClick={onClose}
                className="p-1 rounded-full hover:bg-gray-100 transition-colors"
              >
                <BiX className="w-5 h-5" />
              </button>
            </div>

            {/* Body */}
            <div className="p-4 max-h-[calc(100vh-12rem)] overflow-y-auto">
              {children}
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default CustomModal;
