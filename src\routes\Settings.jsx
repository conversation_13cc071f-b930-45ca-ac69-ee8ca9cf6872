import { useCallback, useState } from "react";
import { Form<PERSON>rovider, useForm } from "react-hook-form";
import { InputField } from "../parts/FormComponents";
import { IoMdLock } from "react-icons/io";
import Button from "../parts/Button";
import { toast } from "react-toastify";
import { useSelector } from "react-redux";
import { useResetPasswordMutation } from "../store/apis/authorsApi";
import { FiEye, FiEyeOff } from "react-icons/fi";
import Container from "../parts/Container";

const Settings = () => {
	const { user } = useSelector((state) => state.user);
	const [resetPassword, { isLoading }] = useResetPasswordMutation();

	// Check if user is an author - check multiple possible fields
	const isAuthor =
		user?.role === "author" ||
		user?.userType === "author" ||
		user?.type === "author" ||
		user?.accountType === "author" ||
		// If user has author-specific fields, they're likely an author
		(user?.firstname && user?.lastname) ||
		// Check if user object has typical author fields
		user?.aboutus !== undefined;

	// Password visibility states
	const [currentPasswordShow, setCurrentPasswordShow] = useState(false);
	const [newPasswordShow, setNewPasswordShow] = useState(false);
	const [confirmPasswordShow, setConfirmPasswordShow] = useState(false);

	// Validation error state
	const [validationError, setValidationError] = useState("");

	const {
		register,
		handleSubmit,
		reset,
		getFieldState,
		getValues,
		setValue,
		control,
		watch,
		formState: { errors },
	} = useForm({
		mode: "onSubmit",
	});

	// Password validation function
	const validatePassword = (password) => {
		const minLength = password.length >= 6;
		const hasUpperCase = /[A-Z]/.test(password);
		const hasLowerCase = /[a-z]/.test(password);
		const hasNumber = /\d/.test(password);
		const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);

		return {
			isValid: minLength && hasUpperCase && hasLowerCase && hasNumber && hasSpecialChar,
			errors: {
				minLength,
				hasUpperCase,
				hasLowerCase,
				hasNumber,
				hasSpecialChar,
			},
		};
	};

	const onSubmit = (payload) => {
		const { currentPassword, newPassword, confirmPassword } = payload;

		// Clear previous validation error
		setValidationError("");

		// Validation
		if (!currentPassword) {
			setValidationError("Please enter your current password");
			return;
		}

		if (!newPassword) {
			setValidationError("Please enter a new password");
			return;
		}

		if (!confirmPassword) {
			setValidationError("Please confirm your new password");
			return;
		}

		// Password validation
		const passwordValidation = validatePassword(newPassword);
		if (!passwordValidation.isValid) {
			setValidationError("Password does not meet requirements");
			return;
		}

		if (newPassword !== confirmPassword) {
			setValidationError("New password and confirm password do not match");
			return;
		}

		if (currentPassword === newPassword) {
			setValidationError("New password must be different from current password");
			return;
		}

		// API call
		resetPassword({
			email: user.email,
			oldPassword: currentPassword,
			newPassword,
		})
			.unwrap()
			.then(() => {
				toast.success("Password updated successfully");
				reset(); // Reset form
				setValidationError(""); // Clear any validation errors
			})
			.catch((err) => {
				setValidationError(err?.data?.message || "Failed to update password");
			});
	};

	const handleCurrentPasswordToggle = useCallback(() => {
		setCurrentPasswordShow(!currentPasswordShow);
	}, [currentPasswordShow]);

	const handleNewPasswordToggle = useCallback(() => {
		setNewPasswordShow(!newPasswordShow);
	}, [newPasswordShow]);

	const handleConfirmPasswordToggle = useCallback(() => {
		setConfirmPasswordShow(!confirmPasswordShow);
	}, [confirmPasswordShow]);

	return (
		<Container>
			<div className="flex justify-center items-center min-h-[90vh] w-full">
				<div className="flex flex-col gap-y-3 w-full md:w-3/4 lg:w-3/5 2xl:w-[60%] p-10 rounded-xl bg-white">
					<div>
						<h2 className="text-lg font-bold text-center">Settings</h2>
						<p className="text-center text-sm text-gray-600 mt-2">Update your password</p>
					</div>
					{/* User Info */}
					<div className="mb-4 p-4 bg-gray-50 rounded-lg">
						<h3 className="font-semibold text-sm text-gray-700 mb-2">Account Information</h3>
						<p className="text-sm text-gray-600">
							<span className="font-medium">Name:</span> {user?.firstname || user?.name || "N/A"}{" "}
							{user?.lastname || ""}
						</p>
						<p className="text-sm text-gray-600">
							<span className="font-medium">Email:</span> {user?.email || "N/A"}
						</p>
						<p className="text-sm text-gray-600">
							<span className="font-medium">Role:</span> {isAuthor ? "Author" : "Admin"}
						</p>
					</div>
					<>
						<FormProvider
							getFieldState={getFieldState}
							getValues={getValues}
							setValue={setValue}
							watch={watch}
							register={register}
							errors={errors}
							control={control}
						>
							<form className="" onSubmit={handleSubmit(onSubmit)}>
								{/* Current Password */}
								<InputField
									name="currentPassword"
									type={currentPasswordShow ? "text" : "password"}
									startIcon={<IoMdLock className="text-xl text-fadeGray" />}
									placeholder="Enter your current password"
									endIcon={
										currentPasswordShow ? (
											<FiEyeOff onClick={handleCurrentPasswordToggle} className="cursor-pointer" />
										) : (
											<FiEye onClick={handleCurrentPasswordToggle} className="cursor-pointer" />
										)
									}
								/>
								{/* New Password */}
								<InputField
									name="newPassword"
									type={newPasswordShow ? "text" : "password"}
									startIcon={<IoMdLock className="text-xl text-fadeGray" />}
									placeholder="Enter your new password"
									endIcon={
										newPasswordShow ? (
											<FiEyeOff onClick={handleNewPasswordToggle} className="cursor-pointer" />
										) : (
											<FiEye onClick={handleNewPasswordToggle} className="cursor-pointer" />
										)
									}
								/>

								{/* Confirm Password */}
								<InputField
									name="confirmPassword"
									type={confirmPasswordShow ? "text" : "password"}
									startIcon={<IoMdLock className="text-xl text-fadeGray" />}
									placeholder="Confirm your new password"
									endIcon={
										confirmPasswordShow ? (
											<FiEyeOff onClick={handleConfirmPasswordToggle} className="cursor-pointer" />
										) : (
											<FiEye onClick={handleConfirmPasswordToggle} className="cursor-pointer" />
										)
									}
								/>
								<Button type="submit" customClasses="w-full" disabled={isLoading}>
									{isLoading ? "Updating..." : "Update Password"}
								</Button>

								{/* Validation Error Display */}
								{validationError && <p className="text-sm text-red-600">{validationError}</p>}
							</form>

							{/* Password Requirements */}
							<div className="mb-4 p-3 bg-gray-50 rounded-lg text-xs">
								<p className="font-semibold mb-2 text-gray-700">Password Requirements:</p>
								<div className="space-y-1">
									<p>✓ At least 6 characters</p>
									<p>✓ One uppercase letter</p>
									<p>✓ One lowercase letter</p>
									<p>✓ One number</p>
									<p>✓ One special character</p>
								</div>
							</div>
						</FormProvider>
					</>
				</div>
			</div>
		</Container>
	);
};

export default Settings;
