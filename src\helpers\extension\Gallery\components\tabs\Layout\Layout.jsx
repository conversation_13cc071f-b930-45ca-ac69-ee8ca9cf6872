import React, { useCallback } from "react";
import Grid from "../../../../../../../public/svg/Grid";
import Masonry from "../../../../../../../public/svg/Masonry";
import Collage from "../../../../../../../public/svg/Collage";
import Thumbnails from "../../../../../../../public/svg/Thumbnails";
import Slideshow from "../../../../../../../public/svg/Slideshow";
import Panorama from "../../../../../../../public/svg/Panorama";
import Columns from "../../../../../../../public/svg/Columns";
import Slider from "../../../../../../../public/svg/Slider";
import Thumbnail from "./Thumbnail";
import Properties from "./Properties";

const Layout = ({ editor, layout = "grid", properties = {} }) => {
  const layoutData = [
    {
      title: "Grid",
      value: "grid",
      icon: <Grid width={"60%"} height={"60%"} strokeWidth={0.5} />,
    },
    {
      title: "Masonry",
      value: "masonry",
      icon: <Masonry width={"60%"} height={"60%"} strokeWidth={0.5} />,
    },
    {
      title: "Collage",
      value: "collage",
      icon: <Collage width={"60%"} height={"60%"} strokeWidth={0.5} />,
    },
    {
      title: "Thumbnails",
      value: "thumbnails",
      icon: <Thumbnails width={"60%"} height={"60%"} strokeWidth={0.5} />,
    },
    {
      title: "Slideshow",
      value: "slideshow",
      icon: <Slideshow width={"60%"} height={"60%"} strokeWidth={0.5} />,
    },
    {
      title: "Panorama",
      value: "panorama",
      icon: <Panorama width={"60%"} height={"60%"} strokeWidth={0.5} />,
    },
    {
      title: "Columns",
      value: "columns",
      icon: <Columns width={"60%"} height={"60%"} strokeWidth={0.5} />,
    },
    {
      title: "Slider",
      value: "slider",
      icon: <Slider width={"60%"} height={"60%"} strokeWidth={0.5} />,
    },
  ];
  const onChangeLayout = useCallback(
    (value) => {
      editor.chain().setGalleryBlockLayout(value).run();
    },
    [editor, layout]
  );
  return (
    <div className="overflow-y-auto overflow-x-hidden">
      <div className="px-4 py-6">
        <div className="flex flex-col">
          <div className="grid grid-cols-4 gap-5">
            {layoutData?.map((item, index) => {
              return (
                <Thumbnail
                  key={`layout-${index}`}
                  index={index}
                  title={item?.title || ""}
                  icon={item?.icon || null}
                  selected={layout === item?.value}
                  onClick={() => onChangeLayout(item?.value)}
                />
              );
            })}
          </div>
        </div>
      </div>
      <div className="border-b my-2"></div>
      <Properties editor={editor} layout={layout} properties={properties} />
    </div>
  );
};

export default Layout;
