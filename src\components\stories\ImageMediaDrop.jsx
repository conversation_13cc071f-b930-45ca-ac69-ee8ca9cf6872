import React, { useCallback, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { MdDeleteOutline } from "react-icons/md";
import { RoundedIconsButton } from "../../parts/Button";
import { GoPlus } from "react-icons/go";
import { LiaExchangeAltSolid } from "react-icons/lia";
import { FiEdit } from "react-icons/fi";
import { openMediaLibrary, resetMedia, setFolderPath } from "../../store/slices/mediaLibrarySlice";
import { setMediaLibraryCallback } from "../../utils/MediaLibraryManager";
import { setSettingsData } from "../../store/slices/storiesSlice";

const ImageMediaDrop = ({
	selectedFiles,
	setSelectedFiles,
	label = true,
	customHeight = null,
	customClasses = null,
	customHeight1 = null,
	isEdit = false,
	onEditClick = () => {},
	customImgClass = null,
	folderPath = "article/",
}) => {
	const dispatch = useDispatch();
	const onMediaSet = useCallback(
		(file) => {
			if (file.length === 0) return;
			setSelectedFiles(file?.[0]?.imageUrl || "");
			dispatch(setSettingsData({ name: "caption", value: file?.[0]?.caption || "" }));
			dispatch(setSettingsData({ name: "altName", value: file?.[0]?.altName || "" }));
			dispatch(resetMedia());
		},
		[setSelectedFiles, dispatch]
	);

	const handleClick = () => {
		setMediaLibraryCallback((file) => {
			onMediaSet(file);
		});
		dispatch(openMediaLibrary());
		dispatch(setFolderPath(folderPath));
	};

	const replaceFile = () => {
		handleClick();
	};

	const removeFile = () => {
		setSelectedFiles([]);
		dispatch(resetMedia());
	};

	const baseStyle = {
		flex: 1,
		display: "flex",
		flexDirection: "column",
		alignItems: "center",
		justifyContent: "center",
		padding: "20px",
		height: customHeight ? customHeight : "180px",
		borderWidth: 2,
		borderRadius: 5,
		borderColor: "#3b82f6",
		backgroundColor: "#f4f7ff",
		color: "#bdbdbd",
		outline: "none",
		cursor: "pointer",
		transition: "border .24s ease-in-out",
	};

	return (
		<>
			{label ? <div className="text-sm text-fadeGray mb-3">Featured Image</div> : null}
			{selectedFiles && selectedFiles.length > 0 ? (
				<div className="relative flex justify-center group ">
					<div className="hidden group-hover:block transition-all duration-200">
						<div className="inset-0 flex justify-center h-full w-full bg-slate-600 bg-opacity-50 absolute top-0 left-0 z-20">
							<div className="flex items-center gap-x-4 z-30">
								<RoundedIconsButton onClick={replaceFile}>
									<LiaExchangeAltSolid title="Replace" className="text-xl" />
								</RoundedIconsButton>
								<RoundedIconsButton onClick={removeFile}>
									<MdDeleteOutline className="text-xl" title="Delete" />
								</RoundedIconsButton>
								{isEdit ? (
									<RoundedIconsButton onClick={onEditClick}>
										<FiEdit className="text-lg" />
									</RoundedIconsButton>
								) : null}
							</div>
						</div>
					</div>
					<div className={`${customClasses}`}>
						<img
							src={selectedFiles}
							alt="Selected file"
							style={{
								height: customHeight1 ? customHeight1 : "180px", // Default height
								...((typeof customImgClass === "object" && customImgClass) || {}),
							}}
						/>
					</div>
				</div>
			) : (
				<div style={baseStyle} onClick={handleClick}>
					<div className="border border-primary border-dashed w-full h-full flex items-center justify-center">
						<GoPlus className="text-4xl" />
					</div>
				</div>
			)}
			{/* {error && <div className="text-sm text-red-400 mt-2">{error}</div>} */}
		</>
	);
};

export default ImageMediaDrop;
