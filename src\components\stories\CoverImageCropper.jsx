import React, { useCallback, useEffect, useMemo } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  setCoverImgTransformData,
  setEditCoverImg,
} from "../../store/slices/storiesSlice";

import {
  setCoverImgTransformData as setVideoCoverImgTransformData,
  setEditCoverImg as setVideoEditCoverImg,
} from "../../store/slices/videoStorySlice";

import {
  setCoverImgTransformData as setWebStoryCoverImgTransformData,
  setEditCoverImg as setWebStoryEditCoverImg,
} from "../../store/slices/webstorySlice";
import CustomModal from "../../parts/CustomModal";
import ImageCropper from "./ImageCropper";

const CoverImageCropper = ({ type }) => {
  const dispatch = useDispatch();
  const {
    storiesState: { coverImg },
    editCoverImg,
  } = useSelector((state) => state[type]);

  const { coverImg: webStoryCoverImg } = useSelector((state) => state.webStory);

  const finalCoverImg = type === "stories" ? coverImg : webStoryCoverImg;

  // Create object URL once and memoize it
  const imageUrl = useMemo(() => {
    if (typeof finalCoverImg === "string") return finalCoverImg;
    if (finalCoverImg?.length > 0) return URL.createObjectURL(finalCoverImg[0]);
    return "";
  }, [finalCoverImg]);

  // Cleanup object URL when component unmounts or coverImg changes
  useEffect(() => {
    return () => {
      if (imageUrl && imageUrl.startsWith("blob:")) {
        URL.revokeObjectURL(imageUrl);
      }
    };
  }, [imageUrl]);

  const handleCropComplete = useCallback((data) => {
    if (type === "stories") {
      dispatch(setCoverImgTransformData(data));
    } else if (type === "videoStory") {
      dispatch(setVideoCoverImgTransformData(data));
    } else if (type === "webStory") {
      dispatch(setWebStoryCoverImgTransformData(data));
    }
  }, []);

  return (
    <div>
      <CustomModal
        isOpen={editCoverImg}
        onClose={() => {
          if (type === "stories") {
            dispatch(setEditCoverImg(false));
          } else if (type === "videoStory") {
            dispatch(setVideoEditCoverImg(false));
          } else if (type === "webStory") {
            dispatch(setWebStoryEditCoverImg(false));
          }
        }}
        title="Edit Image"
        size="xl"
      >
        <ImageCropper
          image={imageUrl}
          type={type}
          onCropComplete={handleCropComplete}
        />
      </CustomModal>
    </div>
  );
};

export default CoverImageCropper;
