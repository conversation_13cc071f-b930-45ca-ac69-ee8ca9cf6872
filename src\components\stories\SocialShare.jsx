import React, { useEffect, useState } from "react";
import { Input } from "../../parts/FormComponents";
import {
  setImage,
  setOgContent,
  setXContent,
} from "../../store/slices/storiesSlice";
import { useDispatch, useSelector } from "react-redux";
import { RxCross2 } from "react-icons/rx";
import ToggleSwitch from "../../parts/ToggleSwitch";
import ImageDrop from "./ImageDrop";
import Accordion, { AccordionProvider } from "../../parts/Accordion";
import { FiInfo } from "react-icons/fi";
import Tippy from "@tippyjs/react";
import { IoInformationCircleOutline } from "react-icons/io5";
import { folderPath, storyType } from "../../utils/constants";
import {
  setVideoImage,
  setVideoOgContent,
  setVideoXContent,
} from "../../store/slices/videoStorySlice";
import {
  setAuthorImage,
  setAuthorOgContent,
  setAuthorXContent,
} from "../../store/slices/authorsSlice";
import {
  setTagImage,
  setTagOgContent,
  setTagXContent,
} from "../../store/slices/tagsSlice";
import {
  setWebStoryImage,
  setWebStoryOgContent,
  setWebStoryXContent,
} from "../../store/slices/webstorySlice";
import ImageMediaDrop from "./ImageMediaDrop";

const SocialShare = ({ type = "stories" }) => {
  const dispatch = useDispatch();
  const {
    storiesState: { og, twitter, ogImg, twitterImg },
  } = useSelector((state) => state[storyType[type]]);
  const [selectedFiles, setSelectedFiles] = useState({
    ogImg: ogImg,
    twitterImg: twitterImg,
  });

  const handleDataChange = ({ name, value }) => {
    if (type === "stories") {
      dispatch(setOgContent({ name, value }));
    }
    if (type === "videoStory") {
      dispatch(setVideoOgContent({ name, value }));
    }
    if (type === "authors") {
      dispatch(setAuthorOgContent({ name, value }));
    }
    if (type === "tags") {
      dispatch(setTagOgContent({ name, value }));
    }
    if (type === "webStory") {
      dispatch(setWebStoryOgContent({ name, value }));
    }
  };

  const handleTwitterChanges = ({ name, value }) => {
    if (type === "stories") {
      dispatch(setXContent({ name, value }));
    }
    if (type === "videoStory") {
      dispatch(setVideoXContent({ name, value }));
    }
    if (type === "authors") {
      dispatch(setAuthorXContent({ name, value }));
    }
    if (type === "tags") {
      dispatch(setTagXContent({ name, value }));
    }
    if (type === "webStory") {
      dispatch(setWebStoryXContent({ name, value }));
    }
  };

  const handleFileChange = (file, type) => {
    setSelectedFiles((prevFiles) => ({
      ...prevFiles,
      [type]: file,
    }));
  };

  useEffect(() => {
    if (selectedFiles.ogImg) {
      if (type === "stories") {
        dispatch(setImage({ name: "ogImg", value: selectedFiles.ogImg }));
      }
      if (type === "videoStory") {
        dispatch(setVideoImage({ name: "ogImg", value: selectedFiles.ogImg }));
      }
      if (type === "authors") {
        dispatch(setAuthorImage({ name: "ogImg", value: selectedFiles.ogImg }));
      }
      if (type === "tags") {
        dispatch(setTagImage({ name: "ogImg", value: selectedFiles.ogImg }));
      }
      if (type === "webStory") {
        dispatch(
          setWebStoryImage({ name: "ogImg", value: selectedFiles.ogImg })
        );
      }
    }
    if (selectedFiles.twitterImg) {
      if (type === "stories") {
        dispatch(
          setImage({ name: "twitterImg", value: selectedFiles.twitterImg })
        );
      }
      if (type === "videoStory") {
        dispatch(
          setVideoImage({
            name: "twitterImg",
            value: selectedFiles.twitterImg,
          })
        );
      }
      if (type === "authors") {
        dispatch(
          setAuthorImage({
            name: "twitterImg",
            value: selectedFiles.twitterImg,
          })
        );
      }
      if (type === "tags") {
        dispatch(
          setTagImage({ name: "twitterImg", value: selectedFiles.twitterImg })
        );
      }
      if (type === "webStory") {
        dispatch(
          setWebStoryImage({
            name: "twitterImg",
            value: selectedFiles.twitterImg,
          })
        );
      }
    }
  }, [selectedFiles.ogImg, selectedFiles.twitterImg, dispatch]);

  return (
    <div className="flex flex-col gap-y-4 text-sm text-fadeGray">
      <div className="flex flex-col gap-4">
        <div>
          <div className="font-semibold">Social share</div>
          <p>
            Open graph (og) tags are used by social networks like Facebook &
            Pinterest to display text and an image when this page is shared.
          </p>
        </div>
        <div>
          <p>Preview on social</p>
          <p>When will changes show live?</p>
        </div>
      </div>

      {/* Image upload components for ogImg and twitterImg */}
      <div>
        <ImageMediaDrop
          label={false}
          selectedFiles={selectedFiles.ogImg}
          setSelectedFiles={(file) => handleFileChange(file, "ogImg")}
          folderPath={folderPath.stories}
        />
      </div>

      <Input
        label="og:title"
        name="title"
        value={og.title}
        id="title"
        placeholder="Add the page's title"
        onDebouncedChange={(value) =>
          handleDataChange({ value, name: "title" })
        }
        toolTip="Search engines may show a different title"
      />

      <Input
        label="og:description"
        name="description"
        value={og.description}
        id="description"
        placeholder="Add a description about this page"
        onDebouncedChange={(value) =>
          handleDataChange({ value, name: "description" })
        }
        toolTip="Search engines may show a different description"
      />

      <AccordionProvider>
        <div className="border-t border-b">
          <Accordion
            title={
              <div className="flex items-center gap-x-2">
                X Settings{" "}
                <Tippy
                  content={"Preview on X"}
                  placement="right"
                  trigger="click"
                >
                  <button
                    type="button"
                    className="ml-1 text-gray-400 hover:text-gray-600"
                  >
                    <IoInformationCircleOutline className="text-lg text-primary" />
                  </button>
                </Tippy>
              </div>
            }
            index={1}
          >
            <div className="flex flex-col gap-y-4">
              <div>
                <p>Preview on X</p>
                <p>When will changes show live?</p>
                <div>
                  <ImageMediaDrop
                    selectedFiles={selectedFiles.twitterImg}
                    setSelectedFiles={(file) =>
                      handleFileChange(file, "twitterImg")
                    }
                    folderPath={folderPath.stories}
                  />
                </div>
              </div>
              <div>
                <p className="mb-1">Select Card Size:</p>
                <div className="flex items-center gap-x-2">
                  <input
                    type="radio"
                    value={"large"}
                    name={"card"}
                    id={"large"}
                    className="w-4 h-4"
                    checked={twitter.card === "large"}
                    onClick={() =>
                      handleTwitterChanges({ value: "large", name: "card" })
                    }
                  />
                  <label htmlFor={"large"}>Large</label>
                </div>
                <div className="flex items-center gap-x-2">
                  <input
                    type="radio"
                    value={"small"}
                    name={"card"}
                    checked={twitter.card === "small"}
                    id={"small"}
                    className="w-4 h-4"
                    onClick={() =>
                      handleTwitterChanges({ value: "small", name: "card" })
                    }
                  />
                  <label htmlFor={"small"}>Small</label>
                </div>
              </div>
              <Input
                label="x:title"
                name="title"
                id="title"
                value={twitter.title}
                placeholder="Add the page's title"
                onDebouncedChange={(value) =>
                  handleTwitterChanges({ value, name: "title" })
                }
                toolTip="Search engines may show a different title"
              />
              <Input
                label="x:description"
                name="description"
                id="description"
                value={twitter.description}
                placeholder="Add a description about this page"
                onDebouncedChange={(value) =>
                  handleTwitterChanges({
                    value,
                    name: "description",
                  })
                }
                toolTip="Search engines may show a different description"
              />
            </div>
          </Accordion>
        </div>
      </AccordionProvider>
    </div>
  );
};

export default SocialShare;
