import { useSelector } from "react-redux";
import Button from "../../parts/Button";

const buttonColors = {
  PUBLISHED: "bg-[#e6f4ff] text-[#3475de]",
  UNPUBLISHED: "bg-[#ffeff0] text-[#ff4d4f]",
  SCHEDULED: "bg-[#4972EB] text-[#fff]",
  DRAFT: "bg-[#dcdcdc] text-[#646464]",
};

const StoryCard = ({
  title,
  author,
  timestamp,
  status = "PUBLISHED",
  imageUrl,
  onEdit,
  onPreview,
  previewUrl = null,
}) => {
  const { user } = useSelector((state) => state.user);
  return (
    <div className="relative w-full group rounded-lg overflow-hidden border transition-all duration-300 bg-white">
      {/* Status Badge */}
      <div className="absolute top-1 left-0 z-20">
        <span
          className={`${buttonColors[status]} px-3 py-1 text-xs font-medium `}
        >
          {status}
        </span>
      </div>

      {/* Image Container */}
      <div className="relative w-full h-40">
        {/* Background Image */}
        <img
          src={imageUrl}
          alt={title}
          className="object-cover w-full h-full text-xs"
        />

        {/* Overlay on Hover */}
        {/* {isHovered && ( */}
        <div className="absolute group-hover:flex hidden inset-0 backdrop-blur-sm bg-black bg-opacity-50 flex-col items-center justify-center gap-2 transition-opacity duration-300">
          <Button
            size="sm"
            rounded="full"
            customClasses="py-2 px-4"
            onClick={onEdit}
          >
            Edit Story
          </Button>
          <a
            href={`${user.clientLink}${previewUrl}`}
            onClick={onPreview}
            target="_blank"
            className="text-white hover:underline hover:cursor-pointer"
          >
            Preview
          </a>
        </div>
        {/* )} */}
      </div>

      {/* Content */}
      <div className="bg-white p-4">
        <h2 className=" mb-2 line-clamp-2">{title}</h2>
        <div className="text-gray-500 text-xs">
          {timestamp} | {author}
        </div>
      </div>
    </div>
  );
};

export default StoryCard;
