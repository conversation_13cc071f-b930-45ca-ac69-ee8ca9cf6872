import { useEffect, useRef, useState } from "react";
import {
  FiChevronLeft,
  FiChevronRight,
  FiEdit2,
  FiMoreVertical,
  FiTrash2,
} from "react-icons/fi";
import { Input } from "../../parts/FormComponents";
import ReactQuill from "react-quill";
import "react-quill/dist/quill.snow.css";
import parse from "html-react-parser";
import { LiaExchangeAltSolid } from "react-icons/lia";
import SlideReorder from "./SlideReorder";
import { useSelector } from "react-redux";
import { formatDateTime } from "../../utils/helperFunctions";

// Slide Preview component
const SlidePreview = ({
  slides,
  onUpdateSlide,
  handleEditSlide,
  currentIndex,
  setCurrentIndex,
}) => {
  const descriptionRef = useRef(null);
  const [editMode, setEditMode] = useState({
    title: false,
    content: false,
    contributor: false,
    altName: false,
  });

  const {
    storiesState: { publishDate },
  } = useSelector((state) => state.webStory);

  const handlePrevious = () => {
    setCurrentIndex((prev) => (prev > 0 ? prev - 1 : slides.length - 1));
  };

  const handleNext = () => {
    setCurrentIndex((prev) => (prev < slides.length - 1 ? prev + 1 : 0));
  };

  const handleDelete = (slideId) => {
    onUpdateSlide(slides.filter((slide) => slide._id !== slideId));
    if (currentIndex >= slides.length - 1) {
      setCurrentIndex(slides.length - 2);
    }
  };

  const handleImageChange = (e, index) => {
    const file = e.target.files[0];
    if (file) {
      const updatedSlides = slides.map((slide, idx) =>
        idx === index ? { ...slide, image: [file] } : slide
      );
      onUpdateSlide(updatedSlides);
    }
  };

  const handleEdit = (field, value) => {
    const updatedSlides = slides.map((slide, idx) =>
      idx === currentIndex ? { ...slide, [field]: value } : slide
    );
    onUpdateSlide(updatedSlides);
  };

  const currentSlide = slides[currentIndex];
  useEffect(() => {
    // Ensure the currentIndex is within bounds
    if (currentIndex >= slides.length) {
      setCurrentIndex(slides.length - 1);
    }
  }, [slides]);

  console.log(currentSlide, " currenslide");

  if (!currentSlide) return null;

  return (
    <div className="relative min-h-[600px] flex flex-col justify-center bg-white rounded-lg text-fadeGray">
      <div className="flex items-center justify-between p-2">
        <button
          onClick={handlePrevious}
          className="p-2 bg-white rounded-full focus:outline-none"
        >
          <FiChevronLeft size={24} />
        </button>

        <div className="flex-1 mx-4 group">
          <div className="relative h-[500px] bg-white rounded-lg shadow-md overflow-hidden">
            <div className="absolute top-4 right-4 z-10 hidden group-hover:block">
              <div className="flex items-center gap-x-2 transition-all duration-200 ease-in">
                <button
                  onClick={() => handleDelete(currentSlide._id)}
                  className="p-2 bg-red-500 text-white rounded-full"
                >
                  <FiTrash2 />
                </button>
                <button
                  className="p-2 bg-gray-200 rounded-full"
                  onClick={() => handleEditSlide(currentIndex)}
                >
                  <FiEdit2 />
                </button>
                <button
                  className="p-2 bg-gray-200 rounded-full"
                  onClick={() =>
                    document
                      .getElementById(`image-input-${currentIndex}`)
                      .click()
                  }
                >
                  <LiaExchangeAltSolid />
                </button>
                <input
                  id={`image-input-${currentIndex}`}
                  type="file"
                  accept="image/*"
                  className="hidden"
                  onChange={(e) => handleImageChange(e, currentIndex)}
                />
              </div>
            </div>

            <img
              src={
                typeof currentSlide.coverImg === "string" &&
                currentSlide.coverImg !== undefined
                  ? currentSlide.coverImg
                  : URL.createObjectURL(currentSlide.coverImg[0])
              }
              // alt={currentSlide.altName}
              className="absolute top-0 left-0 w-full h-full object-cover"
            />

            <div className="absolute bottom-0 left-0 w-full bg-gradient-to-t from-black to-transparent p-4 text-white z-10">
              {editMode.title ? (
                <Input
                  value={currentSlide.title}
                  onDebouncedChange={(value) => handleEdit("title", value)}
                  onBlur={() =>
                    setEditMode((prev) => ({ ...prev, title: false }))
                  }
                  autoFocus
                  customClass="bg-transparent text-white !border-white"
                />
              ) : (
                <h3
                  className="text-xl font-bold mb-2 cursor-pointer"
                  onClick={() =>
                    setEditMode((prev) => ({ ...prev, title: true }))
                  }
                >
                  {currentSlide.title}
                </h3>
              )}
              {editMode.content ? (
                <div
                  contentEditable={true}
                  ref={descriptionRef}
                  className="border border-white text-white rounded-md px-4 py-2 w-full focus:outline-none focus:mt-2"
                  onInput={() => {}}
                  onBlur={() => {
                    handleEdit("content", descriptionRef.current.innerHTML);
                    setEditMode((prev) => ({ ...prev, content: false }));
                  }}
                >
                  {parse(currentSlide.content)}
                </div>
              ) : (
                <div
                  className="cursor-pointer text-sm"
                  onClick={() =>
                    setEditMode((prev) => ({ ...prev, content: true }))
                  }
                  dangerouslySetInnerHTML={{ __html: currentSlide.content }}
                />
              )}
              {publishDate &&
              !Array.isArray(publishDate) &&
              publishDate !== "" ? (
                <small className="text-[11px]">
                  {formatDateTime(publishDate).replace(/for/gi, "")}
                </small>
              ) : null}
            </div>
          </div>
        </div>

        <button
          onClick={handleNext}
          className="p-2 bg-white rounded-full focus:outline-none"
        >
          <FiChevronRight size={24} />
        </button>
      </div>

      <div className="flex justify-center pb-4">
        {slides.map((_, idx) => (
          <button
            key={idx}
            className={`w-2 h-2 rounded-full mx-1 ${
              idx === currentIndex ? "bg-primary" : "bg-gray-300"
            }`}
            onClick={() => setCurrentIndex(idx)}
          />
        ))}
      </div>
      {slides.length > 0 ? (
        <SlideReorder
          slides={slides}
          onReorder={(newSlides) => {
            onUpdateSlide(newSlides);
            setCurrentIndex(0);
          }}
        />
      ) : null}
    </div>
  );
};

export default SlidePreview;
