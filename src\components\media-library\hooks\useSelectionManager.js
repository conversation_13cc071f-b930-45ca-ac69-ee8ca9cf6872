import { useCallback } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
	toggleFolderSelection,
	toggleImageSelection,
	clearSelection,
	setSelectedFolders,
	setSelectedImages,
} from "../../../store/slices/mediaLibrarySlice";
import { runMediaLibraryCallback } from "../../../utils/MediaLibraryManager";

/**
 * Hook for managing selections in media library
 * Handles folder and media selection logic
 */
export const useSelectionManager = () => {
	const dispatch = useDispatch();
	
	// Selectors
	const selection = useSelector((state) => state.mediaLibrary.selection);
	const modal = useSelector((state) => state.mediaLibrary.modal);
	
	/**
	 * Check if a folder is selected
	 */
	const isFolderSelected = useCallback((folderId) => {
		return selection.selectedFolders.some(folder => 
			(folder._id || folder.id) === folderId
		);
	}, [selection.selectedFolders]);
	
	/**
	 * Check if a media item is selected
	 */
	const isMediaSelected = useCallback((mediaId) => {
		return selection.selectedImages.some(media => media._id === mediaId);
	}, [selection.selectedImages]);
	
	/**
	 * Handle folder selection
	 */
	const handleFolderSelect = useCallback((folder) => {
		dispatch(toggleFolderSelection(folder));
	}, [dispatch]);
	
	/**
	 * Handle media selection
	 */
	const handleMediaSelect = useCallback((mediaItem) => {
		dispatch(toggleImageSelection({ mediaId: mediaItem._id, mediaItem }));
	}, [dispatch]);
	
	/**
	 * Clear all selections
	 */
	const clearAllSelections = useCallback(() => {
		dispatch(clearSelection());
	}, [dispatch]);
	
	/**
	 * Select multiple folders
	 */
	const selectMultipleFolders = useCallback((folders) => {
		dispatch(setSelectedFolders(folders));
	}, [dispatch]);
	
	/**
	 * Select multiple media items
	 */
	const selectMultipleMedia = useCallback((mediaItems) => {
		dispatch(setSelectedImages(mediaItems));
	}, [dispatch]);
	
	/**
	 * Get selected items count
	 */
	const getSelectedCount = useCallback(() => {
		return selection.selectedFolders.length + selection.selectedImages.length;
	}, [selection.selectedFolders.length, selection.selectedImages.length]);
	
	/**
	 * Get selected items for display
	 */
	const getSelectedItems = useCallback(() => {
		return {
			folders: selection.selectedFolders,
			media: selection.selectedImages,
			total: getSelectedCount(),
			hasSelection: getSelectedCount() > 0,
		};
	}, [selection.selectedFolders, selection.selectedImages, getSelectedCount]);
	
	/**
	 * Check if selection is valid for current mode
	 */
	const isSelectionValid = useCallback(() => {
		const totalSelected = getSelectedCount();
		
		if (totalSelected === 0) {
			return false;
		}
		
		if (!modal.isMultiple && totalSelected > 1) {
			return false;
		}
		
		return true;
	}, [getSelectedCount, modal.isMultiple]);
	
	/**
	 * Handle "Add to Page" action
	 */
	const handleAddToPage = useCallback(() => {
		if (!isSelectionValid()) {
			return false;
		}
		
		const selectedItems = getSelectedItems();
		
		// For media items, prepare the data for the callback
		if (selectedItems.media.length > 0) {
			const mediaData = selectedItems.media.map(item => ({
				altName: item.alt || item.title || "",
				imageUrl: item.url || "",
				caption: item.caption || "",
				courtesy: item.courtesy || "",
				_id: item._id,
				title: item.title || "",
			}));
			
			// Run the callback with media data
			if (modal.isMultiple) {
				runMediaLibraryCallback(mediaData);
			} else {
				runMediaLibraryCallback(mediaData[0]);
			}
			
			return true;
		}
		
		// For folders, prepare folder data
		if (selectedItems.folders.length > 0) {
			const folderData = selectedItems.folders.map(folder => ({
				_id: folder._id || folder.id,
				name: folder.name,
				path: folder.path,
				level: folder.level,
			}));
			
			if (modal.isMultiple) {
				runMediaLibraryCallback(folderData);
			} else {
				runMediaLibraryCallback(folderData[0]);
			}
			
			return true;
		}
		
		return false;
	}, [isSelectionValid, getSelectedItems, modal.isMultiple]);
	
	/**
	 * Select all visible items
	 */
	const selectAllVisible = useCallback((visibleFolders = [], visibleMedia = []) => {
		if (modal.isMultiple) {
			// In multiple mode, select all visible items
			const allFolders = [...selection.selectedFolders];
			const allMedia = [...selection.selectedImages];
			
			// Add folders that aren't already selected
			visibleFolders.forEach(folder => {
				if (!isFolderSelected(folder._id || folder.id)) {
					allFolders.push(folder);
				}
			});
			
			// Add media that aren't already selected
			visibleMedia.forEach(media => {
				if (!isMediaSelected(media._id)) {
					allMedia.push(media);
				}
			});
			
			dispatch(setSelectedFolders(allFolders));
			dispatch(setSelectedImages(allMedia));
		}
	}, [dispatch, modal.isMultiple, selection.selectedFolders, selection.selectedImages, isFolderSelected, isMediaSelected]);
	
	/**
	 * Deselect all visible items
	 */
	const deselectAllVisible = useCallback((visibleFolders = [], visibleMedia = []) => {
		const visibleFolderIds = visibleFolders.map(f => f._id || f.id);
		const visibleMediaIds = visibleMedia.map(m => m._id);
		
		const remainingFolders = selection.selectedFolders.filter(folder => 
			!visibleFolderIds.includes(folder._id || folder.id)
		);
		
		const remainingMedia = selection.selectedImages.filter(media => 
			!visibleMediaIds.includes(media._id)
		);
		
		dispatch(setSelectedFolders(remainingFolders));
		dispatch(setSelectedImages(remainingMedia));
	}, [dispatch, selection.selectedFolders, selection.selectedImages]);
	
	/**
	 * Toggle select all visible items
	 */
	const toggleSelectAllVisible = useCallback((visibleFolders = [], visibleMedia = []) => {
		const visibleFolderIds = visibleFolders.map(f => f._id || f.id);
		const visibleMediaIds = visibleMedia.map(m => m._id);
		
		// Check if all visible items are selected
		const allFoldersSelected = visibleFolderIds.every(id => isFolderSelected(id));
		const allMediaSelected = visibleMediaIds.every(id => isMediaSelected(id));
		const allVisible = visibleFolders.length + visibleMedia.length;
		const allSelected = allFoldersSelected && allMediaSelected && allVisible > 0;
		
		if (allSelected) {
			deselectAllVisible(visibleFolders, visibleMedia);
		} else {
			selectAllVisible(visibleFolders, visibleMedia);
		}
	}, [isFolderSelected, isMediaSelected, selectAllVisible, deselectAllVisible]);
	
	/**
	 * Get selection summary text
	 */
	const getSelectionSummary = useCallback(() => {
		const { folders, media, total } = getSelectedItems();
		
		if (total === 0) {
			return "No items selected";
		}
		
		if (folders.length > 0 && media.length > 0) {
			return `${folders.length} folder${folders.length > 1 ? 's' : ''} and ${media.length} media item${media.length > 1 ? 's' : ''} selected`;
		}
		
		if (folders.length > 0) {
			return `${folders.length} folder${folders.length > 1 ? 's' : ''} selected`;
		}
		
		if (media.length > 0) {
			return `${media.length} media item${media.length > 1 ? 's' : ''} selected`;
		}
		
		return "";
	}, [getSelectedItems]);
	
	return {
		// State
		selection,
		
		// Checkers
		isFolderSelected,
		isMediaSelected,
		isSelectionValid,
		
		// Actions
		handleFolderSelect,
		handleMediaSelect,
		clearAllSelections,
		selectMultipleFolders,
		selectMultipleMedia,
		handleAddToPage,
		
		// Bulk actions
		selectAllVisible,
		deselectAllVisible,
		toggleSelectAllVisible,
		
		// Getters
		getSelectedCount,
		getSelectedItems,
		getSelectionSummary,
	};
};
