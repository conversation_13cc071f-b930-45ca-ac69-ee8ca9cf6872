import React, { useEffect, useState, useCallback } from "react";
import { useDispatch } from "react-redux";
import Button from "../../../../../../parts/Button";
import { FiRefreshCcw, FiTrash2 } from "react-icons/fi";
import ImageSlider from "./ImageSlider";
import {
  openMediaLibrary,
  setFolderPath,
  setMultiple,
} from "../../../../../../store/slices/mediaLibrarySlice";
import { folderPath } from "../../../../../../utils/constants";
import { setMediaLibraryCallback } from "../../../../../../utils/MediaLibraryManager";

const Edit = ({ editor, data = [], current, setCurrent }) => {
  const dispatch = useDispatch();
  const [formState, setFormState] = useState({
    caption: "",
    courtesy: "",
    alt: "",
  });

  useEffect(() => {
    const currentData = data[current] || {};
    setFormState({
      caption: currentData.caption || "",
      courtesy: currentData.courtesy || "",
      alt: currentData.alt || "",
    });
  }, [current]);

  const updateEditorData = (field, value) => {
    const updatedData = data.map((item, index) =>
      index === current ? { ...item, [field]: value } : item
    );

    editor.chain().setGalleryBlock({ data: updatedData }).run();
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;

    setFormState((prev) => ({
      ...prev,
      [name]: value,
    }));

    updateEditorData(name, value);
  };

  const handleDelete = () => {
    if (!data || data.length === 0) return;
    const updatedData = data.filter((_, index) => index !== current);
    editor.chain().setGalleryBlock({ data: updatedData }).run();
    if (updatedData.length === 0) {
      setCurrent(null);
    } else if (current >= updatedData.length) {
      setCurrent(updatedData.length - 1);
    } else {
      setCurrent(current);
    }
  };

  const onUpload = useCallback(
    (urls) => {
      if (!urls || urls.length === 0) return;

      const updatedData = [...data];
      updatedData[current] = {
        ...updatedData[current],
        src: urls[0],
      };

      editor.chain().setGalleryBlock({ data: updatedData }).run();
    },
    [data, current, editor]
  );

  const handleReplace = () => {
    setMediaLibraryCallback((file) => {
      onUpload(file);
    });
    dispatch(setMultiple(false));
    dispatch(setFolderPath(folderPath.galleryBlock));
    dispatch(openMediaLibrary());
  };

  const renderInput = (label, name, placeholder) => (
    <div className="flex flex-col gap-y-2">
      <label htmlFor={name} className="text-base">
        {label}
      </label>
      <input
        id={name}
        name={name}
        type="text"
        className="border text-sm border-gray-300 rounded-md px-4 py-2 w-full focus:outline-none focus:border-blue-500"
        value={formState[name]}
        onChange={handleInputChange}
        placeholder={placeholder}
      />
    </div>
  );

  return (
    <div className="px-4 py-6 overflow-y-auto overflow-x-hidden">
      <div className="flex flex-col gap-y-4">
        {/* Image slider */}
        <ImageSlider
          data={data}
          current={current}
          setCurrent={setCurrent}
        />

        {/* Action buttons */}
        <div className="flex justify-between items-center">
          <Button
            variant="secondary"
            size="sm"
            rounded="full"
            className="btn-primary-outline"
            onClick={handleReplace}
          >
            <FiRefreshCcw className="mr-1" /> Replace
          </Button>
          <Button
            variant="secondary"
            size="sm"
            rounded="full"
            className="btn-primary-outline"
            onClick={handleDelete}
          >
            <FiTrash2 className="mr-1" /> Delete
          </Button>
        </div>
        {/* Form fields */}
        {renderInput("Caption", "caption", "Add a caption here")}
        {renderInput("Courtesy", "courtesy", "Add a courtesy here")}
        {renderInput(
          "Alt name",
          "alt",
          "e.g. A cat sleeping on a white blanket"
        )}
      </div>
    </div>
  );
};

export default Edit;
