import { useState, useCallback } from "react";

const useConfirmationModal = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [rowIdToDelete, setRowIdToDelete] = useState(null);

  const openModal = useCallback((rowId) => {
    setIsModalOpen(true);
    setRowIdToDelete(rowId);
  }, []);

  const closeModal = useCallback(() => {
    setIsModalOpen(false);
    setRowIdToDelete(null);
  }, []);

  return {
    isModalOpen,
    rowIdToDelete,
    openModal,
    closeModal,
  };
};

export default useConfirmationModal;
