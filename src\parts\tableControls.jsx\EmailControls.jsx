import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useLocation, useNavigate, useSearchParams } from "react-router-dom";
import {
  contactsInputFilter,
  setFiltersStatus,
} from "../../store/slices/emailMarketingSlice";
import { DebouncedInput } from "../FormComponents";
import Button from "../Button";
import { CiFilter } from "react-icons/ci";
import { toggleFilters } from "../../store/slices/tableSlice";

const tabs = [
  { label: "All", value: "all", actualVal: null },
  { label: "Active", value: "active", actualVal: "active" },
  { label: "Unsubscribed", value: "unsubscribed", actualVal: "unsubscribed" },
];

const tabsMappedVal = { all: null, active: 1, unsubscribed: 0 };

const EmailControls = ({ module = "emailContacts", showOnlyAll }) => {
  const [searchParams] = useSearchParams();
  let filteredTab;
  const filterStatus = searchParams.get("status");
  if (filterStatus) {
    filteredTab = tabs.find((t) => t.value === filterStatus);
  } else {
    filteredTab = { value: "all" };
  }

  const [active, setActive] = useState(filteredTab.value);
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation();
  const { filter } = useSelector((state) => state.emailMarketing.contacts);

  // Update active tab when URL changes
  useEffect(() => {
    setActive(filteredTab.value);
  }, [filteredTab]);

  const handleTabClick = (value) => {
    setActive(value);
    const searchParams = new URLSearchParams(location.search);
    searchParams.set("status", value);
    dispatch(setFiltersStatus(tabsMappedVal[value]));

    navigate(`${location.pathname}?${searchParams.toString()}`);
  };

  const handleFilterClick = () => {
    dispatch(toggleFilters());
  };

  const finalTab = showOnlyAll ? [tabs[0]] : tabs;

  return (
    <div className="px-5 gap-3 md:gap-0 flex flex-col md:flex-row py-1 bg-white items-center w-full justify-between sticky top-0 z-20">
      <div className="font-medium text-center">
        <ul className="flex flex-wrap -mb-px">
          {finalTab.map((tab) => (
            <li key={tab.value} className="me-2">
              <button
                onClick={() => handleTabClick(tab.value)}
                className={`inline-block px-4 h-10 border-b-2 rounded-t-lg hover:text-gray-600 transition-all duration-300 ${
                  active === tab.value
                    ? "text-primary border-primary"
                    : "border-transparent"
                }`}
              >
                <h3>{tab.label}</h3>
              </button>
            </li>
          ))}
        </ul>
      </div>
      <div className="flex gap-x-10 items-center">
        <DebouncedInput
          type="text"
          value={filter.search ?? ""}
          onChange={(value) => {
            dispatch(contactsInputFilter(value));
          }}
          placeholder="Search..."
          className="border shadow rounded w-full md:w-auto h-8"
        />
        {/* <Button
					variant="secondary"
					rounded="full"
					customClasses="flex items-center gap-2 mt-3 md:mt-0 h-8"
					onClick={handleFilterClick}
				>
					<CiFilter />
					<span>Filter</span>
				</Button> */}
      </div>
    </div>
  );
};

export default EmailControls;
