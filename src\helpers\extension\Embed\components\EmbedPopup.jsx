import React, { useState, useCallback } from "react";
import <PERSON><PERSON>lick<PERSON><PERSON><PERSON> from "react-outside-click-handler";
import "../../../../styles/embedpopup.css";
import { useDispatch } from "react-redux";
import { setShowEmbed } from "../../../../store/slices/storiesSlice";

const EmbedPopup = ({ editor, getPos }) => {
  const [value, setValue] = useState("");
  const dispatch = useDispatch();

  const handleSave = useCallback(() => {
    if (value) {
      editor.chain().setEmbed({ embed: value }).focus().run();
    }
  }, [value, editor]);

  const isValidUrl = (url) => {
    const urlPattern = new RegExp(
      "^(https?:\\/\\/)?" + // protocol
        "((([a-z\\d]([a-z\\d-]*[a-z\\d])*)\\.?)+[a-z]{2,}|" + // domain name
        "((\\d{1,3}\\.){3}\\d{1,3}))" + // OR ip (v4) address
        "(\\:\\d+)?(\\/[-a-z\\d%_.~+]*)*" + // port and path
        "(\\?[;&a-z\\d%_.~+=-]*)?" + // query string
        "(\\#[-a-z\\d_]*)?$",
      "i"
    );
    return !!urlPattern.test(url);
  };

  return (
    <div className={"modal"}>
      <OutsideClickHandler
        onOutsideClick={() => {
          dispatch(setShowEmbed(false));
        }}
      >
        <div className={"card"}>
          <div className={"cardBody"}>
            <div className={"contentSec"}>
              <h4>Embed</h4>
              <p className={"subheading"}>
                Paste a URL from YouTube, Instagram, Twitter, or Facebook
              </p>
              <input
                type="text"
                className="border text-sm border-gray-300 rounded-md px-4 py-2 w-full focus:outline-none focus:border-blue-500"
                placeholder="e.g. www.youtube.com/example"
                value={value}
                onChange={(e) => setValue(e.target.value)}
                aria-label="Embed URL"
              />
              {!isValidUrl(value) && value.length > 0 && (
                <span className={"errorMessage"}>Please enter a valid URL</span>
              )}
            </div>
          </div>
          <div className={"footer"}>
            <button
              className={"btnPrimaryOutline"}
              onClick={() => {
                setValue("");
                dispatch(setShowEmbed(false));
              }}
            >
              Cancel
            </button>
            <button
              className={"btnPrimary"}
              onClick={() => {
                if (isValidUrl(value)) {
                  handleSave();
                  dispatch(setShowEmbed(false));
                }
              }}
              disabled={!isValidUrl(value)}
            >
              Save
            </button>
          </div>
        </div>
      </OutsideClickHandler>
    </div>
  );
};

export default EmbedPopup;
