import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import { apiEndpoints } from "../../utils/constants";
import { baseQuery } from "./baseConfig";

// Define a service using a base URL and expected endpoints

const baseUrl = import.meta.env.VITE_API_URL;
export const webStoryApi = createApi({
  reducerPath: "webStoryApi",
  baseQuery: baseQuery,
  tagTypes: ["WebStoryData"],
  endpoints: (builder) => ({
    getSingleWebStory: builder.query({
      query: (id) => ({
        url: `${apiEndpoints.getSingleWebStory}/${id}`,
      }),
      transformResponse: (response) => response.data,
      providesTags: ["WebStoryData"],
    }),

    createWebStory: builder.mutation({
      query: (formData) => ({
        url: apiEndpoints.postWebStory,
        method: "POST",
        body: formData,
      }),
      invalidatesTags: ["WebStoryData"],
    }),
    editWebStory: builder.mutation({
      query: ({ formData, id }) => ({
        url: `${apiEndpoints.patchWebStory}/${id}`,
        method: "PATCH",
        body: formData,
      }),
      invalidatesTags: ["WebStoryData"],
    }),

    updateStatus: builder.mutation({
      query: ({ id, status, publishDate = null }) => ({
        url: `${apiEndpoints.updatedWebStoryStatus}/${id}`,
        method: "PATCH",
        body: { data: JSON.stringify({ status, publishDate }) },
      }),
    }),

    deleteWebStory: builder.mutation({
      query: (id) => ({
        url: `${apiEndpoints.deleteWebStory}/${id}`,
        method: "PATCH",
      }),
    }),
  }),
});

// Export hooks for usage in functional components, which are
// auto-generated based on the defined endpoints
export const {
  useGetSingleWebStoryQuery,
  useEditWebStoryMutation,
  useCreateWebStoryMutation,
  useDeleteWebStoryMutation,
  useUpdateStatusMutation,
} = webStoryApi;
