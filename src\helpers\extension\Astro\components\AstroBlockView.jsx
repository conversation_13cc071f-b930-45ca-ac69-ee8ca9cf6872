import React from "react";
import { <PERSON>de<PERSON>iew<PERSON>rapper, NodeViewContent } from "@tiptap/react";
import ImageUploader from "./ImageUploader";
import { MdDeleteOutline, MdSettings } from "react-icons/md";
import { RoundedIconsButton } from "../../../../parts/Button";
import { LiaExchangeAltSolid } from "react-icons/lia";
import { setMediaLibraryCallback } from "../../../../utils/MediaLibraryManager";
import { useDispatch } from "react-redux";
import {
  openMediaLibrary,
  resetMedia,
  setFolderPath,
} from "../../../../store/slices/mediaLibrarySlice";
import { folderPath } from "../../../../utils/constants";
import { setShowAstro } from "../../../../store/slices/storiesSlice";

const AstroBlockView = ({ editor, node }) => {
  const dispatch = useDispatch();
  const { image } = node.attrs;

  const handleUploadClick = () => {
    setMediaLibraryCallback((file) => {
      if (file.length === 0) return;
      editor.chain().setAstroImage(file[0]).run();
      dispatch(resetMedia());
    });
    dispatch(openMediaLibrary());
    dispatch(setFolderPath(folderPath.imageBlock));
  };

  const replaceFile = () => {
    setMediaLibraryCallback((file) => {
      if (file.length === 0) return;
      editor.chain().setAstroImage(file[0]).run();
    });
    dispatch(openMediaLibrary());
    dispatch(setFolderPath(folderPath.imageBlock));
  };
  const removeFile = () => {
    editor.chain().setAstroImage("").run();
    dispatch(resetMedia());
  };

  return (
    <NodeViewWrapper className="astro-widget">
      <div className="astro-widget-container">
        <div className="astro-widget-text">
          <NodeViewContent as="div" className="astro-widget-editor-content" />
        </div>

        {image ? (
          <div className="relative flex justify-center group">
            <div className="absolute inset-0 flex justify-center items-center bg-slate-600 bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity duration-300 ease-in-out z-20">
              <div className="flex items-center gap-x-4 z-30">
                <RoundedIconsButton onClick={replaceFile}>
                  <LiaExchangeAltSolid title="Replace" className="text-xl" />
                </RoundedIconsButton>
                <RoundedIconsButton onClick={removeFile}>
                  <MdDeleteOutline className="text-xl" title="Delete" />
                </RoundedIconsButton>
                <RoundedIconsButton
                  onClick={() => dispatch(setShowAstro(true))}
                >
                  <MdSettings className="text-xl" title="Setting" />
                </RoundedIconsButton>
              </div>
            </div>

            {/* Image */}
            <div className="astro-widget-image">
              <img src={image} alt="Astro Widget" />
            </div>
          </div>
        ) : (
          <div className="astro-widget-uploader">
            <ImageUploader
              editor={editor}
              handleUploadClick={handleUploadClick}
            />
          </div>
        )}
      </div>
    </NodeViewWrapper>
  );
};

export default AstroBlockView;
