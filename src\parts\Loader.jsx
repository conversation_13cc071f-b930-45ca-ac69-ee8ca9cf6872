import React from "react";

const Loader = ({ size = "lg", text = "Loading...", customClass = "" }) => {
	const sizeClasses = {
		sm: "h-4 w-4",
		md: "h-8 w-8",
		lg: "h-12 w-12",
	};

	return (
		<div
			className={`flex flex-col items-center w-full h-full z-50 justify-center gap-3 inset-0 bg-opacity-65 bg-slate-900 ${customClass} absolute top-1/2 left-1/2 -translate-y-1/2 -translate-x-1/2 `}
		>
			<div
				className={`animate-spin rounded-full  text-white border-4 border-gray-200 border-t-primary ${sizeClasses[size]}`}
			/>
			<span className="text-white">{text}</span>
		</div>
	);
};

export default Loader;

export const FullScreenLoader = ({ size = "lg", text = "Loading...", customClass = "" }) => {
	const sizeClasses = {
		sm: "h-4 w-4",
		md: "h-8 w-8",
		lg: "h-12 w-12",
	};

	return (
		<div
			className={`flex flex-col items-center z-50 justify-center gap-3 inset-0 bg-opacity-65 bg-slate-900 ${customClass} fixed top-1/2 left-1/2 h-screen w-screen -translate-y-1/2 -translate-x-1/2 `}
		>
			<div
				className={`animate-spin rounded-full  text-white border-4 border-gray-200 border-t-primary ${sizeClasses[size]}`}
			/>
			<span className="text-white">{text}</span>
		</div>
	);
};

export const AnimatedButtonLoader = () => {
	return (
		<div className="text-center  w-full">
			<div
				className={`animate-spin rounded-full border-2 border-gray-200 border-t-primary h-6 w-6 mx-auto`}
			/>
		</div>
	);
};
