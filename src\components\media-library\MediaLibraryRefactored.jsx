import React, { useEffect, useCallback } from "react";
import { useMediaLibrary } from "./hooks/useMediaLibrary";
import { useFolderOperations } from "./hooks/useFolderOperations";
import { useMediaOperations } from "./hooks/useMediaOperations";
import { useSelectionManager } from "./hooks/useSelectionManager";

// Components
import FolderGrid from "./components/FolderGrid";
import MediaGrid from "./components/MediaGrid";
import BreadcrumbNavigation from "./components/BreadcrumbNavigation";
import SelectionInfo from "./components/SelectionInfo";
import UploadArea from "./components/UploadArea";
import SearchAndFilter from "./components/SearchAndFilter";
import FileInfo from "./components/FileInfo";
import AnimatedButtonLoader from "../common/AnimatedButtonLoader";

/**
 * Refactored Media Library Component
 * Uses hooks for business logic and specialized components for UI
 */
const MediaLibraryRefactored = () => {
	// Hooks
	const {
		modal,
		navigation,
		data,
		selection,
		ui,
		filters,
		pagination,
		upload,
		isLoadingFolders,
		isLoadingMedia,
		openModal,
		closeModal,
		loadFolders,
		loadMediaList,
		handleLoadMore,
	} = useMediaLibrary();
	
	const {
		handleBreadcrumbClick,
		handleFolderEnter,
		showCreateFolderModal,
		hideCreateFolderModal,
		handleCreateFolderSubmit,
		showCreateFolder,
		newFolderName,
		setNewFolderName,
	} = useFolderOperations();
	
	const {
		refreshMediaList,
	} = useMediaOperations();
	
	const {
		handleFolderSelect,
		handleMediaSelect,
		handleAddToPage,
		getSelectedItems,
		isSelectionValid,
	} = useSelectionManager();
	
	// Load initial data
	useEffect(() => {
		if (modal.isOpen) {
			loadFolders(navigation.currentFolder);
			loadMediaList(true);
		}
	}, [modal.isOpen, navigation.currentFolder, loadFolders, loadMediaList]);
	
	// Handle search and filter changes
	const handleSearchChange = useCallback((search) => {
		loadMediaList(true);
	}, [loadMediaList]);
	
	const handleFilterChange = useCallback((filterData) => {
		loadMediaList(true);
	}, [loadMediaList]);
	
	// Handle upload completion
	const handleUploadComplete = useCallback(() => {
		refreshMediaList(navigation.currentFolder, filters.search);
	}, [refreshMediaList, navigation.currentFolder, filters.search]);
	
	// Handle folder navigation
	const handleFolderNavigation = useCallback((folder) => {
		handleFolderEnter(folder);
	}, [handleFolderEnter]);
	
	// Handle breadcrumb navigation
	const handleBreadcrumbNavigation = useCallback((index) => {
		handleBreadcrumbClick(index, navigation.folderHierarchy);
	}, [handleBreadcrumbClick, navigation.folderHierarchy]);
	
	// Get selected media for FileInfo component
	const selectedMedia = selection.selectedImages.length > 0 ? selection.selectedImages[0] : null;
	
	if (!modal.isOpen) {
		return null;
	}
	
	return (
		<div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
			<div className="bg-white rounded-lg shadow-xl w-full max-w-7xl h-full max-h-[90vh] flex flex-col">
				{/* Header */}
				<div className="flex items-center justify-between p-4 border-b border-gray-200">
					<h2 className="text-xl font-semibold text-gray-800">
						Media Library
					</h2>
					<button
						onClick={closeModal}
						className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
					>
						<svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
						</svg>
					</button>
				</div>
				
				{/* Main Content */}
				<div className="flex-1 flex overflow-hidden">
					{/* Left Sidebar - Upload Area */}
					<div className="w-80 border-r border-gray-200 p-4 overflow-y-auto">
						<div className="space-y-4">
							{/* Upload Area */}
							<UploadArea
								currentFolder={navigation.currentFolder ? 
									data.folders.find(f => f._id === navigation.currentFolder) : null
								}
								onUploadComplete={handleUploadComplete}
							/>
							
							{/* Create Folder */}
							<div className="space-y-2">
								<button
									onClick={showCreateFolderModal}
									className="w-full px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors"
								>
									Create Folder
								</button>
								
								{showCreateFolder && (
									<div className="space-y-2">
										<input
											type="text"
											placeholder="Folder name"
											value={newFolderName}
											onChange={(e) => setNewFolderName(e.target.value)}
											className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
											onKeyPress={(e) => {
												if (e.key === 'Enter') {
													handleCreateFolderSubmit(
														navigation.currentFolder ? 
															data.folders.find(f => f._id === navigation.currentFolder) : null
													);
												}
											}}
										/>
										<div className="flex space-x-2">
											<button
												onClick={() => handleCreateFolderSubmit(
													navigation.currentFolder ? 
														data.folders.find(f => f._id === navigation.currentFolder) : null
												)}
												className="flex-1 px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors text-sm"
											>
												Create
											</button>
											<button
												onClick={hideCreateFolderModal}
												className="flex-1 px-3 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors text-sm"
											>
												Cancel
											</button>
										</div>
									</div>
								)}
							</div>
						</div>
					</div>
					
					{/* Center - Media Grid */}
					<div className="flex-1 flex flex-col overflow-hidden">
						{/* Search and Navigation */}
						<div className="p-4 border-b border-gray-200 space-y-3">
							{/* Breadcrumb Navigation */}
							<BreadcrumbNavigation
								folderHierarchy={navigation.folderHierarchy}
								onNavigate={handleBreadcrumbNavigation}
							/>
							
							{/* Search and Filter */}
							<SearchAndFilter
								onSearchChange={handleSearchChange}
								onFilterChange={handleFilterChange}
							/>
							
							{/* Selection Info */}
							<SelectionInfo
								onAddToPage={handleAddToPage}
								showAddToPage={isSelectionValid()}
							/>
						</div>
						
						{/* Content Area */}
						<div className="flex-1 p-4 overflow-y-auto">
							{(isLoadingFolders || isLoadingMedia) ? (
								<div className="flex items-center justify-center h-64">
									<AnimatedButtonLoader />
								</div>
							) : (
								<div className="space-y-6">
									{/* Folders */}
									{data.folders.length > 0 && (
										<div>
											<h3 className="text-lg font-medium text-gray-800 mb-3">
												Folders
											</h3>
											<FolderGrid
												folders={data.folders}
												onFolderEnter={handleFolderNavigation}
												onFolderSelect={handleFolderSelect}
											/>
										</div>
									)}
									
									{/* Media */}
									{data.media.length > 0 && (
										<div>
											<h3 className="text-lg font-medium text-gray-800 mb-3">
												Media ({pagination.media.totalCount || data.media.length})
											</h3>
											<MediaGrid
												media={data.media}
												onMediaSelect={handleMediaSelect}
											/>
											
											{/* Load More Button */}
											{pagination.media.hasMore && (
												<div className="text-center mt-6">
													<button
														onClick={handleLoadMore}
														disabled={isLoadingMedia}
														className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 transition-colors"
													>
														{isLoadingMedia ? "Loading..." : "Load More"}
													</button>
												</div>
											)}
										</div>
									)}
									
									{/* Empty State */}
									{data.folders.length === 0 && data.media.length === 0 && (
										<div className="text-center py-12">
											<svg className="mx-auto w-16 h-16 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
												<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
											</svg>
											<h3 className="text-lg font-medium text-gray-700 mb-2">
												No files or folders
											</h3>
											<p className="text-gray-500">
												Upload some files or create a folder to get started
											</p>
										</div>
									)}
								</div>
							)}
						</div>
					</div>
					
					{/* Right Sidebar - File Info */}
					<div className="w-80 border-l border-gray-200 overflow-y-auto">
						<FileInfo selectedMedia={selectedMedia} />
					</div>
				</div>
				
				{/* Footer */}
				<div className="p-4 border-t border-gray-200 flex items-center justify-between">
					<div className="text-sm text-gray-500">
						{getSelectedItems().total > 0 && (
							<span>{getSelectedItems().total} item(s) selected</span>
						)}
					</div>
					
					<div className="flex space-x-3">
						<button
							onClick={closeModal}
							className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
						>
							Cancel
						</button>
						
						<button
							onClick={handleAddToPage}
							disabled={!isSelectionValid()}
							className={`px-4 py-2 rounded-md transition-colors ${
								isSelectionValid()
									? "bg-blue-600 text-white hover:bg-blue-700"
									: "bg-gray-300 text-gray-500 cursor-not-allowed"
							}`}
						>
							Add to Page
						</button>
					</div>
				</div>
			</div>
		</div>
	);
};

export default MediaLibraryRefactored;
