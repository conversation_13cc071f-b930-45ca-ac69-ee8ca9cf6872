import { createSlice } from "@reduxjs/toolkit";

const initialState = {
	isOpen: false,
	isMultiple: false,
	folderPath: "media/",
	fileType: "",
	selectedMedia: [],
	selectedFolders: [], // Add selected folders array
	selectedImages: [], // Add selected images array
	filters: {
		type: null, // image, video, etc.
		search: "",
	},
	// New folder navigation state
	currentFolder: null, // Current folder ID (null for root)
	folderHierarchy: [], // Array of folder objects for breadcrumb navigation
	folders: [], // List of folders in current directory
	media: [], // List of media in current directory
	// Pagination state
	folderPagination: {
		limit: 20,
		offset: 0,
		hasMore: true,
		totalCount: 0,
	},
	mediaPagination: {
		limit: 20,
		offset: 0,
		hasMore: true,
		totalCount: 0,
	},
};

const mediaLibrarySlice = createSlice({
	name: "mediaLibrary",
	initialState,
	reducers: {
		openMediaLibrary: (state) => {
			state.isOpen = true;
		},
		closeMediaLibrary: (state) => {
			state.isOpen = initialState.isOpen;
			state.folderPath = initialState.folderPath;
		},
		setFolderPath: (state, action) => {
			state.folderPath = action.payload;
		},
		setMultiple: (state, action) => {
			state.isMultiple = action.payload;
		},
		setFileType: (state, action) => {
			state.fileType = action.payload;
		},
		setSelectedMedia: (state, action) => {
			state.selectedMedia = action.payload;
		},
		setSelectedFolders: (state, action) => {
			state.selectedFolders = action.payload;
		},
		toggleFolderSelection: (state, action) => {
			const folderData = action.payload; // Now expects full folder object
			const folderId = folderData._id || folderData.id;
			const exists = state.selectedFolders.some((folder) => (folder._id || folder.id) === folderId);

			// Clear image selections when selecting folders
			state.selectedImages = [];

			if (state.isMultiple) {
				if (exists) {
					state.selectedFolders = state.selectedFolders.filter(
						(folder) => (folder._id || folder.id) !== folderId
					);
				} else {
					state.selectedFolders.push({
						_id: folderId,
						id: folderId,
						name: folderData.name,
						path: folderData.path,
						level: folderData.level,
					});
				}
			} else {
				state.selectedFolders = exists
					? []
					: [
							{
								_id: folderId,
								id: folderId,
								name: folderData.name,
								path: folderData.path,
								level: folderData.level,
							},
					  ];
			}
		},
		setSelectedImages: (state, action) => {
			state.selectedImages = action.payload;
		},
		toggleImageSelection: (state, action) => {
			const { mediaId, mediaItem } = action.payload;
			const exists = state.selectedImages.some((image) => image._id === mediaId);

			// Clear folder selections when selecting images
			state.selectedFolders = [];

			if (state.isMultiple) {
				if (exists) {
					state.selectedImages = state.selectedImages.filter((image) => image._id !== mediaId);
				} else {
					state.selectedImages.push(mediaItem);
				}
			} else {
				state.selectedImages = exists ? [] : [mediaItem];
			}
		},
		setSearchFilter: (state, action) => {
			state.filters.search = action.payload;
		},
		setTypeFilter: (state, action) => {
			state.filters.type = action.payload;
		},
		resetMedia: (state) => {
			state.selectedMedia = initialState.selectedMedia;
			state.selectedFolders = initialState.selectedFolders;
			state.selectedImages = initialState.selectedImages;
			state.isMultiple = initialState.isMultiple;
			state.folderPath = initialState.folderPath;
		},
		resetFilters: (state) => {
			state.filters = initialState.filters;
		},
		// New folder navigation reducers
		setCurrentFolder: (state, action) => {
			state.currentFolder = action.payload;
		},
		setFolderHierarchy: (state, action) => {
			state.folderHierarchy = action.payload;
		},
		addToFolderHierarchy: (state, action) => {
			state.folderHierarchy.push(action.payload);
		},
		removeFromFolderHierarchy: (state, action) => {
			const index = action.payload;
			state.folderHierarchy = state.folderHierarchy.slice(0, index + 1);
		},
		setFolders: (state, action) => {
			state.folders = action.payload;
		},
		setMedia: (state, action) => {
			state.media = action.payload;
		},
		appendMedia: (state, action) => {
			state.media = [...state.media, ...action.payload];
		},
		// Pagination reducers
		setFolderPagination: (state, action) => {
			state.folderPagination = { ...state.folderPagination, ...action.payload };
		},
		setMediaPagination: (state, action) => {
			state.mediaPagination = { ...state.mediaPagination, ...action.payload };
		},
		resetMediaPagination: (state) => {
			state.mediaPagination = initialState.mediaPagination;
		},
		// Navigation helper
		navigateToFolder: (state, action) => {
			const { folder, isRoot = false } = action.payload;
			if (isRoot) {
				state.currentFolder = null;
				state.folderHierarchy = [];
			} else {
				state.currentFolder = folder._id;
				// Add to hierarchy if not already present
				const existingIndex = state.folderHierarchy.findIndex((f) => f._id === folder._id);
				if (existingIndex === -1) {
					state.folderHierarchy.push(folder);
				} else {
					// Navigate back to this folder, remove everything after it
					state.folderHierarchy = state.folderHierarchy.slice(0, existingIndex + 1);
				}
			}
			// Reset pagination when navigating
			state.mediaPagination = initialState.mediaPagination;
			state.media = [];
		},
	},
});

export const {
	openMediaLibrary,
	closeMediaLibrary,
	setFolderPath,
	setMultiple,
	setFileType,
	setSelectedMedia,
	setSelectedFolders,
	toggleFolderSelection,
	setSelectedImages,
	toggleImageSelection,
	setSearchFilter,
	setTypeFilter,
	resetMedia,
	resetFilters,
	// New folder navigation actions
	setCurrentFolder,
	setFolderHierarchy,
	addToFolderHierarchy,
	removeFromFolderHierarchy,
	setFolders,
	setMedia,
	appendMedia,
	setFolderPagination,
	setMediaPagination,
	resetMediaPagination,
	navigateToFolder,
} = mediaLibrarySlice.actions;

export default mediaLibrarySlice.reducer;
