import { createSlice } from "@reduxjs/toolkit";

const initialState = {
	// Modal state
	modal: {
		isOpen: false,
		isMultiple: false,
		fileType: "",
		callback: null,
	},

	// Navigation state
	navigation: {
		currentFolder: null, // Current folder ID (null for root)
		folderHierarchy: [], // Array of folder objects for breadcrumb navigation
		folderPath: "media/",
	},

	// Data state
	data: {
		folders: [], // List of folders in current directory
		media: [], // List of media in current directory
	},

	// Selection state
	selection: {
		selectedFolders: [], // Selected folder objects
		selectedImages: [], // Selected media objects
		selectedMedia: [], // Legacy - for backward compatibility
	},

	// UI state
	ui: {
		showCreateFolder: false,
		showEditMedia: false,
		activeView: "grid", // 'grid' | 'list'
	},

	// Filter state
	filters: {
		type: null, // image, video, etc.
		search: "",
		tags: [],
	},

	// Pagination state
	pagination: {
		folders: {
			limit: 20,
			offset: 0,
			hasMore: true,
			totalCount: 0,
		},
		media: {
			limit: 20,
			offset: 0,
			hasMore: true,
			totalCount: 0,
		},
	},

	// Upload state
	upload: {
		progress: [],
		isUploading: false,
	},
};

const mediaLibrarySlice = createSlice({
	name: "mediaLibrary",
	initialState,
	reducers: {
		// Modal actions
		openMediaLibrary: (state, action) => {
			state.modal.isOpen = true;
			if (action.payload) {
				const { isMultiple, fileType, callback } = action.payload;
				state.modal.isMultiple = isMultiple || false;
				state.modal.fileType = fileType || "";
				state.modal.callback = callback || null;
			}
		},
		closeMediaLibrary: (state) => {
			state.modal = initialState.modal;
			state.selection = initialState.selection;
			state.ui = initialState.ui;
		},
		setModalConfig: (state, action) => {
			state.modal = { ...state.modal, ...action.payload };
		},
		setMultiple: (state, action) => {
			state.modal.isMultiple = action.payload;
		},

		// Navigation actions
		setCurrentFolder: (state, action) => {
			state.navigation.currentFolder = action.payload;
		},
		setFolderPath: (state, action) => {
			state.navigation.folderPath = action.payload;
		},
		setFolderHierarchy: (state, action) => {
			state.navigation.folderHierarchy = action.payload;
		},
		addToFolderHierarchy: (state, action) => {
			state.navigation.folderHierarchy.push(action.payload);
		},
		removeFromFolderHierarchy: (state, action) => {
			const index = action.payload;
			state.navigation.folderHierarchy = state.navigation.folderHierarchy.slice(0, index + 1);
		},
		navigateToFolder: (state, action) => {
			const { folder, isRoot = false } = action.payload;
			if (isRoot) {
				state.navigation.currentFolder = null;
				state.navigation.folderHierarchy = [];
			} else {
				state.navigation.currentFolder = folder._id;
				const existingIndex = state.navigation.folderHierarchy.findIndex(
					(f) => f._id === folder._id
				);
				if (existingIndex === -1) {
					state.navigation.folderHierarchy.push(folder);
				} else {
					state.navigation.folderHierarchy = state.navigation.folderHierarchy.slice(
						0,
						existingIndex + 1
					);
				}
			}
			// Reset pagination and media when navigating
			state.pagination.media = initialState.pagination.media;
			state.data.media = [];
		},

		// Data actions
		setFolders: (state, action) => {
			state.data.folders = action.payload;
		},
		setMedia: (state, action) => {
			state.data.media = action.payload;
		},
		appendMedia: (state, action) => {
			state.data.media = [...state.data.media, ...action.payload];
		},

		// Selection actions
		setSelectedFolders: (state, action) => {
			state.selection.selectedFolders = action.payload;
			// Legacy support
			state.selection.selectedMedia = action.payload;
		},
		setSelectedImages: (state, action) => {
			state.selection.selectedImages = action.payload;
			// Legacy support
			state.selection.selectedMedia = action.payload;
		},
		setSelectedMedia: (state, action) => {
			state.selection.selectedMedia = action.payload;
		},
		toggleFolderSelection: (state, action) => {
			const folderData = action.payload;
			const folderId = folderData._id || folderData.id;
			const exists = state.selection.selectedFolders.some(
				(folder) => (folder._id || folder.id) === folderId
			);

			// Clear image selections when selecting folders (exclusive selection)
			state.selection.selectedImages = [];

			if (state.modal.isMultiple) {
				if (exists) {
					state.selection.selectedFolders = state.selection.selectedFolders.filter(
						(folder) => (folder._id || folder.id) !== folderId
					);
				} else {
					state.selection.selectedFolders.push(folderData);
				}
			} else {
				state.selection.selectedFolders = exists ? [] : [folderData];
			}
			// Update legacy selectedMedia
			state.selection.selectedMedia = state.selection.selectedFolders;
		},
		toggleImageSelection: (state, action) => {
			const { mediaId, mediaItem } = action.payload;
			const exists = state.selection.selectedImages.some((image) => image._id === mediaId);

			// Clear folder selections when selecting images (exclusive selection)
			state.selection.selectedFolders = [];

			if (state.modal.isMultiple) {
				if (exists) {
					state.selection.selectedImages = state.selection.selectedImages.filter(
						(image) => image._id !== mediaId
					);
				} else {
					state.selection.selectedImages.push(mediaItem);
				}
			} else {
				state.selection.selectedImages = exists ? [] : [mediaItem];
			}
			// Update legacy selectedMedia
			state.selection.selectedMedia = state.selection.selectedImages;
		},
		clearSelection: (state) => {
			state.selection = initialState.selection;
		},

		// Filter actions
		setSearchFilter: (state, action) => {
			state.filters.search = action.payload;
		},
		setTypeFilter: (state, action) => {
			state.filters.type = action.payload;
		},
		setTagsFilter: (state, action) => {
			state.filters.tags = action.payload;
		},
		resetFilters: (state) => {
			state.filters = initialState.filters;
		},

		// UI actions
		setShowCreateFolder: (state, action) => {
			state.ui.showCreateFolder = action.payload;
		},
		setShowEditMedia: (state, action) => {
			state.ui.showEditMedia = action.payload;
		},
		setActiveView: (state, action) => {
			state.ui.activeView = action.payload;
		},

		// Pagination actions
		setFolderPagination: (state, action) => {
			state.pagination.folders = { ...state.pagination.folders, ...action.payload };
		},
		setMediaPagination: (state, action) => {
			state.pagination.media = { ...state.pagination.media, ...action.payload };
		},
		resetFolderPagination: (state) => {
			state.pagination.folders = initialState.pagination.folders;
		},
		resetMediaPagination: (state) => {
			state.pagination.media = initialState.pagination.media;
		},

		// Upload actions
		setUploadProgress: (state, action) => {
			state.upload.progress = action.payload;
		},
		addUploadProgress: (state, action) => {
			state.upload.progress.push(action.payload);
		},
		updateUploadProgress: (state, action) => {
			const { id, progress } = action.payload;
			const index = state.upload.progress.findIndex((item) => item.id === id);
			if (index !== -1) {
				state.upload.progress[index] = { ...state.upload.progress[index], ...progress };
			}
		},
		removeUploadProgress: (state, action) => {
			const id = action.payload;
			state.upload.progress = state.upload.progress.filter((item) => item.id !== id);
		},
		setIsUploading: (state, action) => {
			state.upload.isUploading = action.payload;
		},

		// Reset actions
		resetMedia: (state) => {
			state.selection = initialState.selection;
			state.modal.isMultiple = initialState.modal.isMultiple;
			state.navigation.folderPath = initialState.navigation.folderPath;
		},
		resetAll: (state) => {
			return initialState;
		},
	},
});

export const {
	// Modal actions
	openMediaLibrary,
	closeMediaLibrary,
	setModalConfig,
	setMultiple,

	// Navigation actions
	setCurrentFolder,
	setFolderPath,
	setFolderHierarchy,
	addToFolderHierarchy,
	removeFromFolderHierarchy,
	navigateToFolder,

	// Data actions
	setFolders,
	setMedia,
	appendMedia,

	// Selection actions
	setSelectedFolders,
	setSelectedImages,
	setSelectedMedia,
	toggleFolderSelection,
	toggleImageSelection,
	clearSelection,

	// Filter actions
	setSearchFilter,
	setTypeFilter,
	setTagsFilter,
	resetFilters,

	// UI actions
	setShowCreateFolder,
	setShowEditMedia,
	setActiveView,

	// Pagination actions
	setFolderPagination,
	setMediaPagination,
	resetFolderPagination,
	resetMediaPagination,

	// Upload actions
	setUploadProgress,
	addUploadProgress,
	updateUploadProgress,
	removeUploadProgress,
	setIsUploading,

	// Reset actions
	resetMedia,
	resetAll,
} = mediaLibrarySlice.actions;

export default mediaLibrarySlice.reducer;
