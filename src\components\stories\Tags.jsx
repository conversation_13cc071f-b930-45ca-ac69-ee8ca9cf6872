import React, { useEffect, useState, useCallback, useRef } from "react";
import { shallowEqual, useDispatch, useSelector } from "react-redux";
import { useGetTagsQuery, usePostTagMutation } from "../../store/apis/storiesApi";
import {
	setTags,
	resetTags,
	incrementOffset,
	setTagData,
	removeTagData,
} from "../../store/slices/storiesSlice";
import Button from "../../parts/Button";
import { RxCross2 } from "react-icons/rx";
import { useGetMetaDataTagQuery } from "../../store/apis/tagsApi";
import { formatTagTitle } from "../../utils/helperFunctions";
import { toast } from "react-toastify";
const Tags = () => {
	const dispatch = useDispatch();
	const {
		fetchedTagData,
		search,
		showMore,
		offset,
		limit,
		storiesState: { tags },
	} = useSelector((state) => state.stories);

	const [postTag] = usePostTagMutation();
	const [searchInput, setSearchInput] = useState(search || "");
	const [debouncedSearch, setDebouncedSearch] = useState(search || "");
	const [isSearching, setIsSearching] = useState(false);
	const debounceTimerRef = useRef(null);

	// Debounce function for search input
	const debounce = useCallback((func, delay) => {
		return function (...args) {
			if (debounceTimerRef.current) {
				clearTimeout(debounceTimerRef.current);
			}
			debounceTimerRef.current = setTimeout(() => {
				func(...args);
			}, delay);
		};
	}, []);

	// Fetch tags from API based on debounced search and offset
	const { data: fetchedTags, isFetching } = useGetTagsQuery({
		search: debouncedSearch,
		limit,
		offset,
	});

	const { data: metaTagTemplateData } = useGetMetaDataTagQuery();

	// Show more tags when "Show More" button is clicked
	const handleShowMore = () => {
		dispatch(incrementOffset());
	};

	// Update search input immediately but debounce the API call
	const handleSearchChange = (e) => {
		const value = e.target.value;
		setSearchInput(value);

		// Show searching indicator
		if (value !== debouncedSearch) {
			setIsSearching(true);
		}

		// Debounce the search update and API call
		debounce(() => {
			setDebouncedSearch(value);
			dispatch(resetTags());
			setIsSearching(false);
		}, 500)();
	};

	// Create a new tag and add it to fetchedTagData
	const handleCreateTag = async () => {
		// If search input is empty, don't create a tag
		if (!searchInput.trim()) {
			toast.error("Tag name cannot be empty");
			return;
		}

		const payload = {
			name: searchInput,
			meta: {
				title: formatTagTitle(searchInput, metaTagTemplateData?.title),
				description: formatTagTitle(searchInput, metaTagTemplateData?.description),
				keywords: [searchInput],
				author: searchInput,
				og: {
					title: formatTagTitle(searchInput, metaTagTemplateData?.title),
					description: formatTagTitle(searchInput, metaTagTemplateData?.description),
				},
				twitter: {
					title: formatTagTitle(searchInput, metaTagTemplateData?.title),
					description: formatTagTitle(searchInput, metaTagTemplateData?.description),
				},
			},
		};
		try {
			const newTag = await postTag(payload).unwrap();
			console.log(newTag, " newtAg");
			if (newTag.data.status === "failure") {
				toast.error("Tag Already Exists.");
				return;
			}
			dispatch(
				setTags({
					tags: [...fetchedTagData, newTag],
					showMore,
				})
			);
			dispatch(setTagData(newTag._id)); // Add the new tag to the story
			setSearchInput(""); // Clear the input
		} catch (error) {
			if (error.data.status === "failure") {
				toast.error("Tag Already Exists.");
				return;
			}
			console.error("Failed to create tag", error);
		}
	};

	// Add tag to the story
	const handleAddTag = (tag) => {
		if (!tags.includes(tag._id)) {
			dispatch(setTagData(tag._id));
		}
	};

	// Remove tag from the story
	const handleDeleteTag = (tag) => {
		dispatch(removeTagData(tag._id));
	};

	return (
		<div className="py-5">
			<h3>Assign story to one or more tags for readers to find them.</h3>

			{/* Search Input */}
			<div className="flex items-center gap-2 mt-4">
				<div className="relative w-full">
					<input
						type="text"
						placeholder="Search or Create a new tag"
						value={searchInput}
						onChange={handleSearchChange}
						className="border text-sm border-gray-300 rounded-md px-4 py-2 w-full focus:outline-none focus:border-blue-500"
					/>
					{isSearching && (
						<div className="absolute right-3 top-1/2 transform -translate-y-1/2">
							<div className="animate-spin rounded-full h-4 w-4 border-2 border-gray-300 border-t-blue-500"></div>
						</div>
					)}
				</div>
				{searchInput && (
					<Button onClick={handleCreateTag} rounded="full" customClasses={"whitespace-nowrap"}>
						Create Tag
					</Button>
				)}
			</div>

			{/* Tags Display */}
			<div className="flex flex-wrap gap-2 mt-4">
				{fetchedTags
					? fetchedTags.data.map((tag) => (
							<button
								key={tag._id}
								onClick={() => (tags.includes(tag._id) ? handleDeleteTag(tag) : handleAddTag(tag))}
								className={`px-3 py-1 border rounded-full group flex items-center justify-between ${
									tags.includes(tag._id) ? "bg-blue-500 text-white" : "bg-gray-100 text-black"
								}`}
							>
								{tag.name}
								<RxCross2
									className={`text-[16px] ml-2 ${tags.includes(tag._id) ? "" : "hidden"}`}
								/>
							</button>
					  ))
					: null}
			</div>

			{/* Show More Button */}
			{fetchedTags?.data?.length < fetchedTags?.count &&
				(isFetching ? (
					<div className="text-center min-h-10 w-full pb-10">
						<div className="animate-spin rounded-full border-4 border-gray-200 border-t-primary h-8 w-8 mx-auto" />
					</div>
				) : (
					<button className="text-primary mt-3" onClick={handleShowMore}>
						{fetchedTags?.data?.length < fetchedTags?.count ? "Show More..." : ""}
					</button>
				))}
		</div>
	);
};
export default Tags;
