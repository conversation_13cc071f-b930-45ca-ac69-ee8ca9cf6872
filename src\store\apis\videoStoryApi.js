import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import { apiEndpoints } from "../../utils/constants";
import { baseQuery } from "./baseConfig";

// Define a service using a base URL and expected endpoints

const baseUrl = import.meta.env.VITE_API_URL;
export const videoStoryApi = createApi({
  reducerPath: "videoStoryApi",
  baseQuery: baseQuery,
  tagTypes: ["VideoStoryData"],
  endpoints: (builder) => ({
    getSingleVideoStory: builder.query({
      query: (id) => ({
        url: `${apiEndpoints.getSingleVideo}/${id}`,
      }),
      transformResponse: (response) => response.data,
      providesTags: ["VideoStoryData"],
    }),

    createVideoStory: builder.mutation({
      query: (formData) => ({
        url: apiEndpoints.postVideoStory,
        method: "POST",
        body: formData,
      }),
      invalidatesTags: ["VideoStoryData"],
    }),
    editVideoStory: builder.mutation({
      query: ({ formData, id }) => ({
        url: `${apiEndpoints.patchVideoStory}/${id}`,
        method: "PATCH",
        body: formData,
      }),
      invalidatesTags: ["VideoStoryData"],
    }),

    updateStatus: builder.mutation({
      query: ({ id, payload }) => ({
        url: `${apiEndpoints.updateVideoStoryStatus}/${id}`,
        method: "PUT",
        body: payload,
      }),
    }),

    deleteVideoStory: builder.mutation({
      query: (id) => ({
        url: `${apiEndpoints.deleteVideoStory}/${id}`,
        method: "PUT",
        body: { status: 2 },
      }),
    }),
  }),
});

// Export hooks for usage in functional components, which are
// auto-generated based on the defined endpoints
export const {
  useGetSingleVideoStoryQuery,
  useEditVideoStoryMutation,
  useCreateVideoStoryMutation,
  useDeleteVideoStoryMutation,
  useUpdateStatusMutation,
} = videoStoryApi;
