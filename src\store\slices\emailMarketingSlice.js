import { createSlice } from "@reduxjs/toolkit";

// Dummy data for testing UI without API
export const dummyGroups = [
  {
    _id: "1",
    name: "Newsletter Subscribers",
    description: "All users who subscribed to our newsletter",
    contactCount: 3,
    createdAt: "2023-02-10T08:30:00Z",
  },
  {
    _id: "2",
    name: "VIP Customers",
    description: "Our most valuable customers",
    contactCount: 2,
    createdAt: "2023-03-15T09:45:00Z",
  },
  {
    _id: "3",
    name: "Blog Readers",
    description: "Users who regularly read our blog",
    contactCount: 0,
    createdAt: "2023-04-20T14:20:00Z",
  },
];

export const dummyTemplates = [
  {
    id: "1",
    name: "Welcome Email",
    description: "Template for welcoming new subscribers",
    thumbnail: "https://via.placeholder.com/300x200?text=Welcome+Template",
    content:
      "<h1>Welcome to our Newsletter!</h1><p>Thank you for subscribing to our newsletter. We're excited to have you on board!</p>",
  },
  {
    id: "2",
    name: "Monthly Newsletter",
    description: "Standard monthly newsletter template",
    thumbnail: "https://via.placeholder.com/300x200?text=Newsletter+Template",
    content:
      "<h1>Monthly Newsletter</h1><p>Here are the latest updates from our company...</p>",
  },
  {
    id: "3",
    name: "Product Announcement",
    description: "Template for announcing new products",
    thumbnail: "https://via.placeholder.com/300x200?text=Product+Announcement",
    content:
      "<h1>New Product Announcement</h1><p>We're excited to introduce our latest product...</p>",
  },
];

export const dummyCampaigns = [
  {
    _id: "1",
    subject: "Welcome to Our Newsletter",
    status: "sent",
    recipientCount: 150,
    openCount: 95,
    openRate: 63,
    clickCount: 45,
    clickRate: 30,
    sentAt: "2023-06-15T10:30:00Z",
  },
  {
    _id: "2",
    subject: "July Newsletter: Summer Updates",
    status: "sent",
    recipientCount: 200,
    openCount: 120,
    openRate: 60,
    clickCount: 80,
    clickRate: 40,
    sentAt: "2023-07-01T09:15:00Z",
  },
  {
    _id: "3",
    subject: "Special Offer: 25% Off This Weekend",
    status: "scheduled",
    recipientCount: 180,
    scheduledAt: "2023-08-10T08:00:00Z",
  },
  {
    _id: "4",
    subject: "Product Launch Announcement",
    status: "draft",
    recipientCount: 0,
  },
];

export const dummyAnalytics = {
  totalSent: 350,
  totalOpened: 215,
  totalClicked: 125,
  averageOpenRate: 61,
  campaigns: dummyCampaigns,
};

const initialState = {
  // Contacts state
  contacts: {
    data: [], // Initialize with dummy data
    isLoading: false,
    error: null,
    limit: 20,
    offset: 0,
    hasMore: false, // Set to false since we're using dummy data
    filter: {
      search: "",
      status: null,
    },
    selectedContacts: [],
  },

  // Groups state
  groups: {
    data: [...dummyGroups], // Initialize with dummy data
    isLoading: false,
    error: null,
  },

  // Templates state
  templates: {
    data: [...dummyTemplates], // Initialize with dummy data
    isLoading: false,
    error: null,
    selectedTemplate: dummyTemplates[0], // Select first template by default
  },

  // Campaign state
  campaign: {
    subject: "",
    content: "",
    from: "<EMAIL>",
    sendAs: "Your Company Name",
    recipients: {
      type: "all", // all, group, individual
      groupId: null,
      individuals: [],
    },
    templateId: null,
    scheduledDate: null,
    status: "draft", // draft, scheduled, sent
  },

  // Analytics state
  analytics: {
    data: dummyAnalytics, // Initialize with dummy analytics data
    isLoading: false,
    error: null,
  },

  // UI state
  ui: {
    activeTab: "contacts",
    showContactsFilter: false,
  },

  // Errors
  errors: {},
};

// Helper functions
const setFetchedContactsDataState = (state, payload) => {
  if (payload.reset) {
    state.contacts.data = payload.data;
  } else {
    state.contacts.data = [...state.contacts.data, ...payload.data];
  }
  state.contacts.hasMore = payload.hasMore;
  state.contacts.isLoading = false;
};

const incrementOffsetState = (state) => {
  state.contacts.offset += state.contacts.limit;
};

const resetFiltersState = (state) => {
  state.contacts.filter = {
    search: "",
    status: null,
  };
  state.contacts.offset = 0;
  state.contacts.data = [];
};

const contactsInputFilterState = (state, payload) => {
  state.contacts.filter.search = payload;
  state.contacts.offset = 0;
  state.contacts.data = [];
};

export const emailMarketingSlice = createSlice({
  name: "emailMarketing",
  initialState,
  reducers: {
    // Contacts actions
    setFetchedContactsData: (state, { payload }) =>
      setFetchedContactsDataState(state, payload),

    incrementOffset: (state) => incrementOffsetState(state),

    resetFilters: (state) => resetFiltersState(state),

    setFiltersStatus: (state, { payload }) => {
      state.contacts.filter.status = payload;
    },

    contactsInputFilter: (state, { payload }) =>
      contactsInputFilterState(state, payload),

    setSelectedContacts: (state, { payload }) => {
      state.contacts.selectedContacts = payload;
    },

    // Groups actions
    setGroups: (state, { payload }) => {
      state.groups.data = payload;
      state.groups.isLoading = false;
    },

    addGroup: (state, { payload }) => {
      state.groups.data.push(payload);
    },

    updateGroup: (state, { payload }) => {
      const index = state.groups.data.findIndex(
        (group) => group.id === payload.id
      );
      if (index !== -1) {
        state.groups.data[index] = payload;
      }
    },

    deleteGroup: (state, { payload }) => {
      state.groups.data = state.groups.data.filter(
        (group) => group.id !== payload
      );
    },

    // Templates actions
    setTemplates: (state, { payload }) => {
      state.templates.data = payload;
      state.templates.isLoading = false;
    },

    setSelectedTemplate: (state, { payload }) => {
      state.templates.selectedTemplate = payload;
      state.campaign.templateId = payload?.id || null;
    },

    // Campaign actions
    setCampaignField: (state, { payload }) => {
      const { field, value } = payload;
      state.campaign[field] = value;
    },

    setRecipientType: (state, { payload }) => {
      state.campaign.recipients.type = payload;
      // Reset other recipient fields based on type
      if (payload === "all") {
        state.campaign.recipients.groupId = null;
        state.campaign.recipients.individuals = [];
      } else if (payload === "group") {
        state.campaign.recipients.individuals = [];
      } else if (payload === "individual") {
        state.campaign.recipients.groupId = null;
      }
    },

    setRecipientGroup: (state, { payload }) => {
      state.campaign.recipients.groupId = payload;
    },

    setRecipientIndividuals: (state, { payload }) => {
      state.campaign.recipients.individuals = payload;
    },

    resetCampaign: (state) => {
      state.campaign = initialState.campaign;
    },

    // Analytics actions
    setAnalyticsData: (state, { payload }) => {
      state.analytics.data = payload;
      state.analytics.isLoading = false;
    },

    // UI actions
    setActiveTab: (state, { payload }) => {
      state.ui.activeTab = payload;
    },

    toggleContactsFilter: (state) => {
      state.ui.showContactsFilter = !state.ui.showContactsFilter;
    },

    // Error actions
    setErrors: (state, { payload }) => {
      state.errors = payload;
    },

    clearErrors: (state) => {
      state.errors = {};
    },
  },
});

// Action creators are generated for each case reducer function
export const {
  // Contacts actions
  setFetchedContactsData,
  incrementOffset,
  resetFilters,
  setFiltersStatus,
  contactsInputFilter,
  setSelectedContacts,

  // Groups actions
  setGroups,
  addGroup,
  updateGroup,
  deleteGroup,

  // Templates actions
  setTemplates,
  setSelectedTemplate,

  // Campaign actions
  setCampaignField,
  setRecipientType,
  setRecipientGroup,
  setRecipientIndividuals,
  resetCampaign,

  // Analytics actions
  setAnalyticsData,

  // UI actions
  setActiveTab,
  toggleContactsFilter,

  // Error actions
  setErrors,
  clearErrors,
} = emailMarketingSlice.actions;

export default emailMarketingSlice.reducer;
