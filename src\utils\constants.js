// status for the stories
export const status = {
	1: "PUBLISHED",
	0: "UNPUBLISHED",
	3: "DRAFT",
	4: "SCHEDULED",
};

const stautsConstants = [null, 0, 1, 2, 3, 4];

// here the api lists are defined for the application
// all the api endpoints names starts with the method name to execute for the api call
export const apiEndpoints = {
	// get methods
	getHomeList: "/api/article/home-list",
	getFlags: "/api/page/get-flags",
	getTags: "api/tag/list",
	getSingleVideo: "/api/video/edit",
	getVideosList: "/api/writer/list",
	getAuthorsList: "/api/writer/list",
	getCategoriesList: "/api/categories/list",
	getSingleCategoriAllList: "api/article/all-list",
	getSubCategoryList: "api/subcategories/list",
	getEditAuthor: "api/writer/edit",
	getEditTag: "api/tag/edit",
	getSingleStory: "/api/article/edit",
	getSingleWebStory: "/api/web-story/get-one",
	getMetaDataTemplate: "api/meta-data-template/fetch",
	getEmailContacts: "/api/email-marketing/contacts",
	getEmailGroups: "/api/email-marketing/groups",
	getEmailTemplates: "/api/email-marketing/templates",
	getEmailCampaigns: "/api/email-marketing/campaigns",
	getEmailAnalytics: "/api/email-marketing/analytics",
	getMediaList: "api/article/fetch-images",

	// post methods
	postSignin: "/signin",
	authorSignin: "/api/writer/signin",
	postHighlightSubCategory: "/api/article/find-highlighted-article",
	postArticlesList: "/api/article/list",
	postVideoStoriesList: "/api/video/list",
	postCategories: "/api/categories/get-category",
	postFlaggedStoriesList: "/api/page/list",
	postFlaggedStoryAllList: "/api/article/all-list",
	postVideoStory: "/api/video/save",
	postTag: "/api/tag/save",
	postAuthor: "/api/writer/save",
	postCatSubArticle: "api/article/non-subcat-article",
	postImage: "api/editor/image",
	postVideo: "api/editor/video",
	postGallery: "api/editor/gallery",
	postWebStory: "/api/web-story/save",
	postEmailContact: "/api/email-marketing/contacts",
	postEmailGroup: "/api/email-marketing/groups",
	postEmailCampaign: "/api/email-marketing/campaigns",
	postScheduleEmailCampaign: "/api/email-marketing/campaigns/schedule",
	postMedia: "/api/editor/upload-media",
	postFolder: "/api/folder/create",
	postFolderList: "/api/folder/get-list",
	postMediaLibraryList: "/api/media-library/get-list",
	postMediaLibraryCreate: "/api/media-library/create",

	// PUT methods
	deleteStory: "/api/article/update-status",
	deleteVideoStory: "/api/video/update-status",
	deleteWebStory: "/api/web-story/delete",
	deleteTag: "/api/tag/delete",
	deleteAuthor: "api/writer/delete",
	deleteEmailContact: "/api/email-marketing/contacts",
	deleteEmailGroup: "/api/email-marketing/groups",
	updateStoryStatus: "/api/article/update-status",
	updateVideoStoryStatus: "/api/video/update-status",
	updatedSubCategoryArticle: "/api/article/add-subcat-article",

	//Patch methods
	patchTag: "/api/tag/update",
	pathAuthor: "/api/writer/update",
	patchFlaggedStory: "/api/article/delete-subcat-article",
	patchBulkUpdateFlaggedStory: "/api/article/bulk-update",
	patchSubFlaggedStory: "/api/page/update-status",
	patchTextStory: "api/article/update/",
	patchVideoStory: "/api/video/update",
	patchWebStory: "/api/web-story/update",
	patchEmailContact: "/api/email-marketing/contacts",
	patchEmailGroup: "/api/email-marketing/groups",
	patchMedia: "/api/media-library/update",
	deleteFolder: "/api/folder/delete",
	updatedWebStoryStatus: "/api/web-story/update",
};

export const folderPath = {
	stories: "article/",
	imageBlock: "editor-images/",
	videoBlock: "editor-videos/",
	galleryBlock: "editor-gallery/",
	category: "category/",
	subcategory: "subcategory/",
	admin: "admin/",
	section: "section/",
	video: "video/",
	webStories: "web-story/",
	webStoriesSlide: "web-story/slide/",
	writer: "writer/",
};

// used mainly for the redux store to select the state from the particular slice
export const storyType = {
	stories: "stories",
	videoStory: "videoStory",
	authors: "authors",
	tags: "tags",
	webStory: "webStory",
	emailMarketing: "emailMarketing",
};

export const storyTypeNumbers = {
	stories: 0,
	videoStory: 1,
	webStory: 3,
};
