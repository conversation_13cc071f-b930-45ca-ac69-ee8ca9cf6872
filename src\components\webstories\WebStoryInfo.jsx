import React, { useEffect, useState } from "react";
import ReactQuill from "react-quill";
import "react-quill/dist/quill.snow.css";
import { useDispatch, useSelector } from "react-redux";
import { RxCross2 } from "react-icons/rx";
import Button from "../../parts/Button";
import { Input } from "../../parts/FormComponents";
import ImageDrop from "../stories/ImageDrop";
import SlidePreview from "./SlidePreview";
import Modal from "./Modal";
import WebstoryTopSection from "./WebstoryTopSection";
import { setCoverImg, setEditCoverImg } from "../../store/slices/webstorySlice";

const WebStoryInfo = ({ onDataChange, data, method }) => {
  const dispatch = useDispatch();
  const {
    coverImg,
    storiesState: { webStorySlides },
  } = useSelector((state) => state.webStory);
  const [currentSlideData, setCurrentSlideData] = useState({
    title: "",
    altName: "",
    content: "",
    contributor: [],
    coverImg: [],
  });
  const [slides, setSlides] = useState(webStorySlides || []);
  const [newContributor, setNewContributor] = useState("");
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [currentPreviewIndex, setCurrentPreviewIndex] = useState(0);

  const [editingSlideIndex, setEditingSlideIndex] = useState(null);
  const [validationErrors, setValidationErrors] = useState({
    title: "",
    content: "",
    coverImg: "",
    altName: "",
  });

  const validateSlideData = () => {
    const errors = {
      title: "",
      content: "",
      coverImg: "",
      altName: "",
    };
    let isValid = true;

    if (!currentSlideData.title.trim()) {
      errors.title = "Title is required";
      isValid = false;
    }

    if (!currentSlideData.altName.trim()) {
      errors.altName = "Alt name is required";
      isValid = false;
    }

    if (!currentSlideData.content.trim()) {
      errors.content = "Description is required";
      isValid = false;
    }

    if (!currentSlideData.coverImg.length) {
      errors.coverImg = "Cover image is required";
      isValid = false;
    }

    setValidationErrors(errors);
    return isValid;
  };

  const handleEditSlide = (index) => {
    setEditingSlideIndex(index);
    setCurrentSlideData(slides[index]);
    setValidationErrors({
      title: "",
      content: "",
      coverImg: "",
      altName: "",
    });
    setIsModalOpen(true);
  };

  useEffect(() => {
    if (webStorySlides?.length > 0) {
      setSlides(webStorySlides);
    }
  }, [webStorySlides]);

  const handleSaveSlide = () => {
    if (!validateSlideData()) return;

    const updatedSlides = slides.map((slide, index) =>
      index === editingSlideIndex ? currentSlideData : slide
    );
    setSlides(updatedSlides);
    // Clear form and validation errors
    setCurrentSlideData({
      title: "",
      altName: "",
      content: "",
      contributor: [],
      coverImg: [],
    });

    setIsModalOpen(false);
  };

  const handleDataChange = (name, value) => {
    setCurrentSlideData((prev) => ({
      ...prev,
      [name]: value,
    }));
    // Clear validation error when field is updated
    if (validationErrors[name]) {
      setValidationErrors((prev) => ({
        ...prev,
        [name]: "",
      }));
    }
  };

  const handleContributorAdd = () => {
    if (newContributor.trim()) {
      setCurrentSlideData((prev) => ({
        ...prev,
        contributor: [...prev.contributor, newContributor.trim()],
      }));
      setNewContributor("");
    }
  };

  const handleRemoveContributor = (contributorToRemove) => {
    setCurrentSlideData((prev) => ({
      ...prev,
      contributor: prev.contributor.filter((c) => c !== contributorToRemove),
    }));
  };

  const handleAddSlide = () => {
    if (!validateSlideData()) return;
    const newSlide = { ...currentSlideData, _id: Date.now().toString() };
    const newSlides = [...slides, newSlide];
    setSlides(newSlides);

    // Notify SlidePreview about the new slide index
    if (setCurrentPreviewIndex) {
      setCurrentPreviewIndex(newSlides.length - 1); // Focus on the latest slide
    }

    if (
      slides.length === 0 &&
      newSlide.coverImg.length > 0 &&
      coverImg.length == 0
    ) {
      dispatch(setCoverImg(newSlide.coverImg));
    }

    // Clear form and validation errors
    setCurrentSlideData({
      title: "",
      altName: "",
      content: "",
      contributor: [],
      coverImg: [],
    });

    setValidationErrors({
      title: "",
      content: "",
      coverImg: "",
      altName: "",
    });
  };

  useEffect(() => {
    onDataChange({ slides, coverImg });
  }, [slides, coverImg]);

  return (
    <div>
      <WebstoryTopSection />
      <div className="mt-4 flex gap-x-5 px-2">
        <div className="relative min-h-[600px] h-fit border border-[#c1e4fe] rounded-md bg-white w-[60%]">
          <div className="border-b w-full px-5 text-lg font-semibold py-5">
            Slide Info
          </div>
          <div className="flex gap-x-3 w-full">
            <div className="w-[40%] pl-5 py-5 imagedrop">
              <ImageDrop
                selectedFiles={currentSlideData.coverImg}
                setSelectedFiles={(file) => handleDataChange("coverImg", file)}
                label={false}
                customHeight="370px"
                customHeight1="370px"
                customClasses="relative !h-[370px]"
              />
              {validationErrors.coverImg && (
                <p className="text-red-500 text-sm mt-1">
                  {validationErrors.coverImg}
                </p>
              )}
            </div>

            <div className="px-5 py-4 flex flex-col gap-y-4 w-[60%]">
              <div>
                <Input
                  label="Slide Title"
                  value={currentSlideData.title}
                  onDebouncedChange={(value) =>
                    handleDataChange("title", value)
                  }
                  placeholder="Add a slide title"
                />
                {validationErrors.title && (
                  <p className="text-red-500 text-sm mt-1">
                    {validationErrors.title}
                  </p>
                )}
              </div>

              <div>
                <Input
                  label="Alt Name"
                  value={currentSlideData.altName}
                  onDebouncedChange={(value) =>
                    handleDataChange("altName", value)
                  }
                  placeholder="Add alt text"
                />

                {validationErrors.altName && (
                  <p className="text-red-500 text-sm mt-1">
                    {validationErrors.altName}
                  </p>
                )}
              </div>

              <div className="flex flex-col">
                <Input
                  label="Photo Credit"
                  value={newContributor}
                  onDebouncedChange={setNewContributor}
                  onEnter={handleContributorAdd}
                  placeholder="Add contributors"
                />

                <div className="flex flex-wrap gap-2 mt-2">
                  {currentSlideData.contributor.map((contributor, index) => (
                    <span
                      key={index}
                      className="flex items-center gap-2 px-3 py-1 rounded-full bg-primary text-white"
                      onClick={() => handleRemoveContributor(contributor)}
                    >
                      {contributor}
                      <RxCross2 />
                    </span>
                  ))}
                </div>
              </div>

              <div className="webstory_description">
                <label className="text-sm text-fadeGray mb-4">
                  Description
                </label>
                <ReactQuill
                  theme="snow"
                  className=" !rounded-md border border-[#d1d5db] outline-none"
                  value={currentSlideData.content}
                  onChange={(value) => handleDataChange("content", value)}
                />
                {validationErrors.content && (
                  <p className="text-red-500 text-sm mt-1">
                    {validationErrors.content}
                  </p>
                )}
              </div>
            </div>
          </div>
          <div className="flex justify-end">
            <Button onClick={handleAddSlide} rounded="full" customClasses="m-4">
              Add Slide
            </Button>
          </div>
        </div>
        <div className="border border-[#c1e4fe] rounded-md bg-white w-[40%]">
          <div className="border-b w-full px-5 text-lg font-semibold  py-5">
            Preview Slides
          </div>
          {slides.length > 0 ? (
            <>
              <SlidePreview
                slides={slides}
                onUpdateSlide={setSlides}
                handleEditSlide={handleEditSlide}
                currentIndex={currentPreviewIndex}
                setCurrentIndex={setCurrentPreviewIndex}
              />
            </>
          ) : (
            <div className="relative min-h-[600px] flex items-center justify-center bg-white rounded-lg text-fadeGray">
              Add Data to see preview here
            </div>
          )}
        </div>

        <Modal
          isOpen={isModalOpen}
          onClose={() => {
            setIsModalOpen(false); // Clear form and validation errors
            setCurrentSlideData({
              title: "",
              altName: "",
              content: "",
              contributor: [],
              coverImg: [],
            });
          }}
          slideData={currentSlideData}
          onUpdateSlideData={setCurrentSlideData}
          handleSaveData={handleSaveSlide}
        />
      </div>
    </div>
  );
};

export default WebStoryInfo;
