import React from "react";

const Columns = ({ data = [], properties = { spacing: 8 } }) => {
  const { spacing } = properties;

  return (
    <div
      className="flex w-full overflow-x-auto scrollbar"
      style={{ gap: `${spacing}px` }}
    >
      {data.map((item, index) => (
        <div
          key={index}
          className="relative shrink-0 w-[190px] h-[500px] overflow-hidden"
        >
          <img
            className="block absolute w-full h-full object-cover object-center"
            src={item.src}
            alt={item.alt || `Columns image ${index}`}
          />
        </div>
      ))}
    </div>
  );
};

export default Columns;
