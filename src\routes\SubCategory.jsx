import React, { useEffect, useMemo, useState } from "react";
import Container from "../parts/Container";
import BreadCrumb from "../parts/BreadCrumb";
import Button, { RoundedIconsButton } from "../parts/Button";
import { BiChevronRight, BiPlus } from "react-icons/bi";
import Table from "../parts/Table";
import {
  useDeleteStoryMutation,
  usePostStoriesMutation,
  useUpdateStatusMutation,
} from "../store/apis/storiesApi";
import { useDispatch, useSelector } from "react-redux";
import { RxCross2 } from "react-icons/rx";
import Accordion, { AccordionProvider } from "../parts/Accordion";
import { CheckboxButton, DropdownButton } from "../parts/FormComponents";
import Select from "react-select";
import {
  setAuthors,
  toggleCategory,
  toggleTag,
} from "../store/slices/tableSlice";
import { CgMathEqual } from "react-icons/cg";
import Filters from "../parts/Filters";
import { BsEye } from "react-icons/bs";
import { FiEdit } from "react-icons/fi";
import { FaRegTrashAlt } from "react-icons/fa";
import {
  Link,
  useLocation,
  useNavigate,
  useParams,
  useSearchParams,
} from "react-router-dom";
import {
  incrementOffset,
  resetFilters,
  resetOffset,
  resetSubCategoryFilters,
  setFetchedData,
  setInitialCategory,
  subCategoryFilterStoryStatus,
} from "../store/slices/categoriesSlice";
import useInfiniteScrollData from "../utils/useInfiniteScrollData";
import useConfirmationModal from "../utils/useConfirmationModal";
import { toast } from "react-toastify";
import ConfirmationModal from "../parts/ConfirmationModal";
import AddToSubMenu from "../parts/AddToSubMenu";
import AddArticle from "../components/subcategories/AddArticle";
import { handleViewClickInNewTab } from "../utils/helperFunctions";
// import Table from "../parts/Table";

const SubCategory = () => {
  const frontendUrl = import.meta.env.VITE_CLIENT_URL;
  const navigate = useNavigate();
  // const { state } = useLocation();
  const [searchParams] = useSearchParams();
  const subcategoryIds = JSON.parse(
    decodeURIComponent(searchParams.get("ids"))
  );

  const finalSubCategoryIds = useMemo(() => subcategoryIds, [subcategoryIds]);

  const name = searchParams.get("name");
  const level = searchParams.get("level");

  const [selectedRows, setSelectedRows] = useState([]);
  const [showSideMenu, setShowSideMenu] = useState(false);
  const [refetch, setRefetch] = useState(false);
  const dispatch = useDispatch();
  const { isModalOpen, rowIdToDelete, openModal, closeModal } =
    useConfirmationModal();
  const [deleteStory, { isLoading: isDeleting, error: deleteError }] =
    useDeleteStoryMutation();
  const [updateStatus] = useUpdateStatusMutation();
  const { addFilter, showFilters, tag, category, writer, publishedTime } =
    useSelector((state) => state.table);

  useEffect(() => {
    dispatch(subCategoryFilterStoryStatus(searchParams.get("status")));
  }, [searchParams.get("status")]);

  // config for the stories api
  const storiesApiConfig = {
    setDataAction: setFetchedData,
    resetDataAction: resetFilters,
    sliceName: "categories",
  };

  const {
    data,
    isLoading,
    offset,
    filter,
    isError,
    error,
    fetchData,
    handleSearch,
    isFetching,
  } = useInfiniteScrollData({
    config: storiesApiConfig,
    url: "/api/article/all-list",
    ids: subcategoryIds,
  });

  // handling the error here
  if (isError) {
    if (error.status === 401) {
      toast.error("Session Expired", { toastId: "video_story_fetch_error" });
      navigate("/signin");
    }
  }

  // load the data on intial page load and also when the offset changes
  useEffect(() => {
    fetchData();
  }, [
    offset,
    filter.status,
    filter.search,
    tag,
    writer,
    category,
    publishedTime,
    refetch,
  ]);

  //resetting the filters on the intital load
  useEffect(() => {
    if (finalSubCategoryIds) {
      dispatch(setInitialCategory(finalSubCategoryIds));
    }

    return () => {
      dispatch(resetFilters());
      dispatch(resetOffset());
      //  dispatch(resetSubCategoryFilters());
    };
  }, []);

  // increase the offset when user has scrolled till the last row of the table which in turn fethes the data
  const fetchMoreData = () => {
    dispatch(incrementOffset());
  };

  const handleViewClick = (link) => {
    const url = `${frontendUrl}${link}`;
    window.open(url, "_blank", "noopener,noreferrer");
  };

  const handleDelete = () => {
    deleteStory(rowIdToDelete)
      .then((res) => {
        if (res.data.status === "success") {
          toast.success("Story deleted successfully!");
          fetchData(true);
        } else {
          toast.error("Failed to delete story.");
        }
      })
      .catch((err) => {
        toast.error("Failed to delete story.");
        console.log(err);
      })
      .finally(() => {
        closeModal();
      });
  };
  const handleChange = ({ value, id }) => {
    updateStatus({ status: value, id })
      .then((res) => {
        console.log(res);
        if (res.data.status === "success") {
          toast.success("Story status updated successfully!");
          fetchData(true);
        } else {
          toast.error("Failed to update story status.");
        }
      })
      .catch((err) => {
        toast.error("Failed to update story status.");
        console.log(err);
      });
  };

  const columns = [
    // {
    //   accessorKey: "_id",
    //   enableSorting: false,
    //   header: ({ table }) => (
    //     <input
    //       type="checkbox"
    //       checked={table.getIsAllRowsSelected()}
    //       indeterminate={table.getIsSomeRowsSelected()}
    //       onChange={table.getToggleAllRowsSelectedHandler()} //or getToggleAllPageRowsSelectedHandler
    //     />
    //   ),
    //   cell: ({ row }) => (
    //     <input
    //       type="checkbox"
    //       checked={row.getIsSelected()}
    //       disabled={!row.getCanSelect()}
    //       onChange={row.getToggleSelectedHandler()}
    //     />
    //   ),
    // },
    {
      accessorKey: "title",
      id: "title",
      width: 200,
      header: () => "Text Story Details",
      cell: ({ row }) => {
        return (
          <div className="flex items-center gap-x-2 font-semibold">
            <img
              src={row.original.coverImg}
              alt=""
              className="w-[80px] h-[60px] object-cover rounded"
            />
            <div>
              <div className="line-clamp-2">{row.original.title}</div>
              <div className="text-[#969696] text-xs font-normal flex items-center space-x-2">
                <span>
                  {new Date(row.original?.timestamp).getHours()} hours ago,
                </span>
                <span>{row.original.author?.toString()}</span>
              </div>
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: "category",
      header: "Category",
    },
    {
      accessorKey: "tag",
      header: () => "Tags",
      cell: ({ row }) => (
        <div className="line-clamp-3">{row.original.tag.join(", ")}</div>
      ),
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => (
        <DropdownButton
          selected={row.original.status}
          handleChange={(value) =>
            handleChange({ value, id: row.original._id })
          }
        />
      ),
    },
    {
      accessorKey: "timestamp",
      header: "Action",
      cell: ({ row }) => {
        return (
          <div className="flex items-center gap-x-2">
            <RoundedIconsButton
              onClick={() => handleViewClick(row.original.viewLink)}
            >
              <BsEye className="h-[15px] w-[15px]" />
            </RoundedIconsButton>

            <RoundedIconsButton
              onClick={() => {
                handleViewClickInNewTab(
                  `/admin/stories/edit/${row.original._id}?location=stories`
                );
              }}
            >
              <FiEdit className="h-[15px] w-[15px]" />
            </RoundedIconsButton>
            {/* <RoundedIconsButton
                onClick={() => {
                  openModal(row.original._id);
                }}
              >
                <FaRegTrashAlt className="h-[15px] w-[15px]" />
              </RoundedIconsButton> */}
          </div>
        );
      },
    },
  ];
  // funtion to perfrom action on row selection in the table
  const handleRowSelectionChange = (rowIds) => {
    console.log(rowIds, " rows ids");
  };

  // used to perform actions when a row is selected
  const handleRowSelect = (rows) => {
    console.log(rows, " rows");
    setSelectedRows(rows);
    // Make API call or perform other actions with the selected rows
  };

  // used to handle the filter change on the table for the status
  const handleFilterChange = (filterName, value) => {
    console.log(filterName, value, " filter names and value");
    // Update filter state and fetch new data
  };

  return (
    <Container>
      <div className="lg:flex items-center justify-between mb-5">
        <div>
          <div className="flex items-center text-xl lg:text-2xl font-semibold">
            <Link
              to={"/admin/categories"}
              className=" py-1 bg-transparent text-[#b4b4b4] font-normal transition-all duration-150 rounded-full pr-3 hover:text-black"
            >
              Categories
            </Link>
            <BiChevronRight className="text-3xl font-normal text-gray-400" />
            <p className=" pl-2 capitalize">
              {name.split("-").join(" ") || "All"}
            </p>
          </div>
          <p className="text-[15px] lg:text-base">
            Create, customize and manage your stories.
          </p>

          {/* <BreadCrumb title={"Sub Category"} description={" "} /> */}
        </div>
        <Button
          rounded="full"
          size="sm"
          customClasses="mt-2 lg:mt-0 pb-1.5 pt-1.5.5"
          onClick={() => setShowSideMenu(!showSideMenu)}
        >
          <BiPlus /> Add Stories
        </Button>
      </div>
      <Table
        module="subcategories"
        data={data}
        isFetching={isFetching}
        isLoading={isLoading}
        columns={columns}
        handleRowSelectionChange={handleRowSelectionChange}
        fetchMoreData={fetchMoreData}
      />
      <Filters />
      <AddToSubMenu
        showSideMenu={showSideMenu}
        setShowSideMenu={setShowSideMenu}
      >
        <AddArticle
          setShowSideMenu={setShowSideMenu}
          subcategoryIds={finalSubCategoryIds}
          level={level}
          type={null}
          id={finalSubCategoryIds}
          refetch={refetch}
          setRefetch={setRefetch}
        />
      </AddToSubMenu>
      <ConfirmationModal
        isOpen={isModalOpen}
        isLoading={isDeleting}
        toggleModal={closeModal}
        message="Are you sure you want to delete this story?"
        onConfirm={handleDelete}
      />
    </Container>
  );
};

export default SubCategory;
