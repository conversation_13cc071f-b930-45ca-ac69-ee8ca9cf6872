import React from "react";
import AddArticle from "../components/subcategories/AddArticle";

const AddToSubMenu = ({ showSideMenu, setShowSideMenu, children }) => {
  return (
    <div
      className={`${
        showSideMenu ? "block" : "hidden"
      } fixed top-0 right-0 z-20 h-screen w-full bg-slate-600 bg-opacity-40 rounded-r-md shadow-2xl transition-all duration-800 ease-in-out`}
    >
      <div className="w-full md:w-1/2 bg-white absolute right-0 top-0 h-screen">
        {children}
      </div>
    </div>
  );
};

export default AddToSubMenu;
