import React from "react";

const ImageSlider = ({ data, loading = false, current, setCurrent }) => {
  const goToPrev = () => {
    const newIndex = (current - 1 + data.length) % data.length;
    setCurrent(newIndex);
  };

  const goToNext = () => {
    const newIndex = (current + 1) % data.length;
    setCurrent(newIndex);
  };

  return (
    <div className="w-full max-w-xl mx-auto">
      <div className="relative flex items-center justify-center rounded-md bg-gray-100 h-[250px] overflow-hidden">
        {/* Left Arrow */}
        <button
          onClick={goToPrev}
          className="absolute left-4 z-10 bg-white rounded-full shadow p-2 hover:bg-gray-200"
        >
          <svg
            className="w-5 h-5 text-gray-700"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M15 19l-7-7 7-7"
            />
          </svg>
        </button>

        <img
          src={data[current]?.src || ""}
          alt={data[current]?.alt || ""}
          className="object-contain h-full transition-all duration-500"
        />

        {/* Right Arrow */}
        <button
          onClick={goToNext}
          className="absolute right-4 z-10 bg-white rounded-full shadow p-2 hover:bg-gray-200"
        >
          <svg
            className="w-5 h-5 text-gray-700"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M9 5l7 7-7 7"
            />
          </svg>
        </button>
      </div>
      {/* Dots Indicator */}
      <div className="flex justify-center gap-2 mt-4">
        {data.map((_, index) => (
          <button
            key={index}
            onClick={() => setCurrent(index)}
            className={`w-3 h-1 rounded-full transition-colors ${
              current === index ? "bg-blue-600" : "bg-blue-300"
            }`}
          ></button>
        ))}
      </div>
    </div>
  );
};

export default ImageSlider;
