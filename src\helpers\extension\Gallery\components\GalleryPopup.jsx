import React, { useState, useCallback } from "react";
import { useDispatch } from "react-redux";
import <PERSON>Click<PERSON><PERSON><PERSON> from "react-outside-click-handler";
import { setShowGallery } from "../../../../store/slices/storiesSlice";
import { FiX } from "react-icons/fi";
import { useUploader } from "../utils/hooks";
import Button from "../../../../parts/Button";
import styles from "./Gallery.module.css";
import ImageDropzone from "./ImageDropzone";
import { Spinner } from "./Spinner";

const GalleryPopup = ({ editor }) => {
  const [files, setFiles] = useState([]);
  const dispatch = useDispatch();

  const handleClose = useCallback(() => {
    setFiles([]);
    dispatch(setShowGallery(false));
  }, [dispatch]);

  const onUpload = useCallback(
    (urls) => {
      if (!urls || urls.length === 0) return;
      const galleryBlockData = urls.map((item) => ({
        src: item || "",
        alt: "",
        caption: "",
        courtesy: "",
        link: {
          url: "",
          target: "_blank",
          noreferrer: "noreferrer",
          nofollow: "",
          sponsored: "",
        },
      }));
      editor.chain().setGalleryBlock({ data: galleryBlockData }).focus().run();
      handleClose();
    },
    [editor, handleClose]
  );

  const { loading, uploadFile } = useUploader({ onUpload });
  const handleSave = useCallback(() => {
    if (files.length === 0) return;
    uploadFile(files);
  }, [files, uploadFile]);

  const getPreviewStyle = () => {
    return {
      position: "absolute",
      inset: 0,
      width: "100%",
      height: "100%",
      transition: "all 0.1s",
      objectFit: "cover",
    };
  };

  return (
    <div className={styles.modal}>
      <OutsideClickHandler onOutsideClick={handleClose}>
        <div className={styles.card}>
          <div className={styles.header}>
            <h4 className={styles.title}>Gallery</h4>
            <FiX className={styles.closeBtn} onClick={handleClose} />
          </div>
          <div className={styles.cardBody}>
            <div className={styles.cardWrapper}>
              <ImageDropzone
                selectedFiles={files}
                setSelectedFiles={setFiles}
                customImgClass={getPreviewStyle()}
                customClasses={
                  "w-full h-fit mx-auto aspect-square overflow-hidden bg-gray-100 relative rounded-[5px]"
                }
              />
            </div>
          </div>
          <div className={styles.footer}>
            <div className={styles.buttonGroup}>
              <Button
                rounded="full"
                variant="outline"
                customClasses={"text-sm"}
                onClick={handleClose}
              >
                Cancel
              </Button>
              <Button
                rounded="full"
                customClasses={"text-sm"}
                disabled={files.length === 0}
                onClick={handleSave}
              >
                {loading ? (
                  <>
                    Saving
                    <Spinner />
                  </>
                ) : (
                  "Save"
                )}
              </Button>
            </div>
          </div>
        </div>
      </OutsideClickHandler>
    </div>
  );
};

export default GalleryPopup;
