import React, { useEffect, useState } from "react";
import styles from "../MediaLibrary.module.css";
import MultipleFile from "../../../../public/svg/MultipleFile";

const Right = ({ isMultiple = false, selectedFiles = [] }) => {
  const [fileInfo, setFileInfo] = useState({
    name: "",
    type: "",
    size: null,
    date: "",
    resolution: {},
  });
  const [copied, setCopied] = useState(false);

  const ClientLink = localStorage.getItem("user")
    ? JSON.parse(localStorage.getItem("user"))?.clientLink
    : "";

  useEffect(() => {
    if (!selectedFiles.length || isMultiple) return;

    const file = selectedFiles?.[0]?.imageUrl || null;
    if (!file) return;
    const fetchFileInfo = async () => {
      try {
        const response = await fetch(file, { method: "HEAD" });
        const contentType = response.headers.get("Content-Type");
        const contentLength = response.headers.get("Content-Length");
        const addedDate = response.headers.get("Last-Modified");

        const urlParts = file.split("/");
        const fileName = urlParts[urlParts.length - 1];
        const dimensions = await getImageDimensions(file);

        setFileInfo({
          name: fileName,
          type: contentType || "Unknown",
          size: contentLength ? formatBytes(+contentLength) : null,
          date: addedDate ? new Date(addedDate).toLocaleDateString() : "",
          resolution: dimensions,
        });
      } catch (error) {
        console.error("Failed to get file info:", error);
      }
    };

    fetchFileInfo();
  }, [selectedFiles[0], isMultiple]);

  const formatBytes = (bytes, decimals = 2) => {
    if (!bytes) return "0 Bytes";
    const k = 1024;
    const dm = decimals < 0 ? 0 : decimals;
    const sizes = ["Bytes", "KB", "MB", "GB", "TB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + " " + sizes[i];
  };

  const getImageDimensions = (url) =>
    new Promise((resolve, reject) => {
      const img = new Image();
      img.src = url;
      img.onload = () => resolve({ width: img.width, height: img.height });
      img.onerror = () => reject(new Error("Failed to load image"));
    });

  const copyTextToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(selectedFiles[0]);
      setCopied(true);
      setTimeout(() => setCopied(false), 500);
    } catch (err) {
      console.error("Failed to copy text: ", err);
    }
  };

  if (!selectedFiles.length) return null;

  return (
    <div className={styles.rightContainer}>
      <div className="w-full h-full block">
        {isMultiple ? (
          <div className="relative flex flex-col justify-center group cursor-move mb-4">
            <div className="w-full mx-auto flex items-center justify-center aspect-square overflow-hidden bg-gray-100 relative transition-all duration-100 ease-in-out">
              <MultipleFile />
            </div>
            <div className="text-sm mt-4">{`${selectedFiles.length} items selected`}</div>
          </div>
        ) : (
          <>
            <div className="relative flex flex-col justify-center group cursor-move mb-4">
              <div className="w-full mx-auto aspect-square overflow-hidden bg-gray-100 relative transition-all duration-100 ease-in-out">
                <img
                  className="absolute inset-0 w-full h-full object-contain transition-all duration-100"
                  src={selectedFiles?.[0]?.imageUrl || ""}
                  alt="Selected"
                />
              </div>
              <div className="text-sm mt-4">{fileInfo.name}</div>
            </div>

            <div className="relative">
              <span className="block text-base font-bold mb-2">File Info</span>
              <div className="flex flex-col gap-2">
                {fileInfo.type && (
                  <div className="flex justify-between gap-4 text-xs text-slate-500">
                    Type <span>{fileInfo.type}</span>
                  </div>
                )}
                {fileInfo?.size && (
                  <div className="flex justify-between gap-4 text-xs text-slate-500">
                    Size <span>{fileInfo.size}</span>
                  </div>
                )}
                {fileInfo?.resolution?.width && (
                  <div className="flex justify-between gap-4 text-xs text-slate-500">
                    Resolution
                    <span>{`${fileInfo.resolution.width} x ${fileInfo.resolution.height}`}</span>
                  </div>
                )}
                <div className="flex justify-between gap-4 text-xs text-slate-500">
                  URL
                  <span
                    className="text-primary cursor-pointer"
                    onClick={!copied ? copyTextToClipboard : undefined}
                  >
                    {copied ? "Copied..." : "Copy URL"}
                  </span>
                </div>
                {fileInfo?.date && (
                  <div className="flex justify-between gap-4 text-xs text-slate-500">
                    Added Date <span>{fileInfo.date}</span>
                  </div>
                )}
                <div className="flex justify-between gap-4 text-xs text-slate-500">
                  Story URL
                  <a
                    href={ClientLink + selectedFiles?.[0]?.slug || ""}
                    target="_blank"
                    rel="nofollow noreferal"
                    className="text-primary cursor-pointer"
                  >
                    LINK
                  </a>
                </div>
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default Right;
