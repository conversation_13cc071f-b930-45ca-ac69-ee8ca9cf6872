import React, { useEffect, useMemo, useState } from "react";
import Container from "../parts/Container";
import BreadCrumb from "../parts/BreadCrumb";
import Button, { RoundedIconsButton } from "../parts/Button";
import { BiChevronRight, BiPlus } from "react-icons/bi";
import Table from "../parts/Table";
import {
  usePostStoriesMutation,
  usePostVideoStorisMutation,
} from "../store/apis/storiesApi";
import { useDispatch, useSelector } from "react-redux";
import { DropdownButton } from "../parts/FormComponents";

import Filters from "../parts/Filters";
import { BsEye } from "react-icons/bs";
import { FiEdit } from "react-icons/fi";
import { FaRegTrashAlt } from "react-icons/fa";
import { Link, useNavigate, useSearchParams } from "react-router-dom";
import {
  incrementOffset,
  resetFilters,
  videoFilterStoryStatus,
} from "../store/slices/videoStorySlice";
import {
  cleanFilterPayload,
  formatDateAndTime,
  formatDateTime,
  handleViewClickInNewTab,
} from "../utils/helperFunctions";
import useInfiniteScrollData from "../utils/useInfiniteScrollData";
import { setFetchedData } from "../store/slices/videoStorySlice";
import { toast } from "react-toastify";
import useConfirmationModal from "../utils/useConfirmationModal";
import {
  useDeleteVideoStoryMutation,
  useUpdateStatusMutation,
} from "../store/apis/videoStoryApi";
import ConfirmationModal from "../parts/ConfirmationModal";
import Loader from "../parts/Loader";
import { CiImageOn } from "react-icons/ci";
import ScheduleModal from "../components/stories/ScheduleModal";
// import Table from "../parts/Table";

const VideoStories = ({ isShorts = false }) => {
  const navigate = useNavigate();
  const {
    user: { clientLink: frontendUrl },
  } = useSelector((state) => state.user);
  const dispatch = useDispatch();
  const [isPublish, setIsPublish] = useState(false);
  const [tableRowId, setTableRowId] = useState(null);
  const [selectedRows, setSelectedRows] = useState([]);
  const { isModalOpen, rowIdToDelete, openModal, closeModal } =
    useConfirmationModal();
  const [deleteVideoStory, { isLoading: isDeleting, error: deleteError }] =
    useDeleteVideoStoryMutation();
  const [updateStatus] = useUpdateStatusMutation();
  const [searchParams] = useSearchParams();
  const { addFilter, showFilters, tag, category, writer, publishedTime } =
    useSelector((state) => state.table);

  const statusParams = searchParams.get("status");
  // config for the stories api
  const storiesApiConfig = {
    setDataAction: setFetchedData,
    resetDataAction: resetFilters,
    sliceName: "videoStory",
  };

  useEffect(() => {
    dispatch(videoFilterStoryStatus(statusParams));
  }, [statusParams]);

  const {
    data,
    isLoading,
    offset,
    filter,
    isError,
    error,
    fetchData,
    handleSearch,
    isFetching,
  } = useInfiniteScrollData({
    config: storiesApiConfig,
    url: "/api/video/list",
    isShorts: isShorts,
  });

  // handling the error here
  if (isError) {
    if (error.status === 401) {
      toast.error("Session Expired", { toastId: "video_story_fetch_error" });
      navigate("/signin");
    }
  }


  // load the data on intial page load and also when the offset changes
  useEffect(() => {
    fetchData();
  }, [
    offset,
    filter.status,
    filter.search,
    statusParams,
    tag,
    writer,
    category,
    publishedTime,
  ]);

  // increase the offset when user has scrolled till the last row of the table which in turn fethes the data
  const fetchMoreData = () => {
    dispatch(incrementOffset());
  };

  const handleViewClick = (link) => {
    const url = `${frontendUrl}${link}`;
    window.open(url, "_blank", "noopener,noreferrer");
  };

  // used to delete the story
  const handleDelete = () => {
    deleteVideoStory(rowIdToDelete)
      .then((res) => {
        if (res.data.status === "success") {
          toast.success("Story deleted successfully!");
          fetchData(true);
        } else {
          toast.error("Failed to delete story.");
        }
      })
      .catch((err) => {
        toast.error("Failed to delete story.");
        console.log(err);
      })
      .finally(() => {
        closeModal();
      });
  };

  const updateStatusFunction = ({ rowId, payload }) => {
    updateStatus({ id: rowId, payload })
      .then((res) => {
        if (res.data.status === "success") {
          toast.success("Story status updated successfully!");
          fetchData(true);
        } else {
          toast.error("Failed to update story status.");
        }
      })
      .catch((err) => {
        toast.error("Failed to update story status.");
        console.log(err);
      })
      .finally(() => {
        closeModal();
      });
  };
  const onScheduleModalClose = (publishDate) => {
    const payload = {};
    if (isShorts) {
      (payload.isShorts = true), (payload.type = 2);
    }
    payload.publishDate = publishDate;
    payload.status = 4;
    if (publishDate && tableRowId) {
      updateStatusFunction({ rowId: tableRowId, payload });
    }
  };

  // used to update the story status
  const handleChange = ({ value, id }) => {
    const payload = {
      status: value,
    };

    if (isShorts) {
      (payload.isShorts = true), (payload.type = 2);
    }

    if (value === 4) {
      setTableRowId(id);
      setIsPublish(true);
    } else {
      updateStatusFunction({ rowId: id, payload });
    }

    // updateStatus({ id: id, payload })
    //   .then((res) => {
    //     console.log(res);
    //     if (res.data.status === "success") {
    //       toast.success("Story status updated successfully!");
    //       fetchData(true);
    //     } else {
    //       toast.error("Failed to update story status.");
    //     }
    //   })
    //   .catch((err) => {
    //     toast.error("Failed to update story status.");
    //     console.log(err);
    //   })
    //   .finally(() => {
    //     closeModal();
    //   });
  };
  const columns = [
    // {
    //   accessorKey: "_id",
    //   enableSorting: false,
    //   header: ({ table }) => (
    //     <input
    //       type="checkbox"
    //       checked={table.getIsAllRowsSelected()}
    //       indeterminate={table.getIsSomeRowsSelected()}
    //       onChange={table.getToggleAllRowsSelectedHandler()} //or getToggleAllPageRowsSelectedHandler
    //     />
    //   ),
    //   cell: ({ row }) => (
    //     <input
    //       type="checkbox"
    //       checked={row.getIsSelected()}
    //       disabled={!row.getCanSelect()}
    //       onChange={row.getToggleSelectedHandler()}
    //     />
    //   ),
    // },
    {
      accessorKey: "title",
      id: "title",
      size: 450,
      header: () => "Video Story Details",
      cell: ({ row }) => {
        return (
          <div className="flex items-center gap-x-2 font-semibold">
            {row.original.coverImg ? (
              <img
                src={row.original.coverImg}
                alt=""
                className="w-[80px] h-[60px] object-cover rounded"
              />
            ) : (
              <div className="w-[80px] h-[60px] object-cover rounded bg-[#daeffe] flex items-center justify-center">
                <div className="w-[40px] h-[40px] rounded-full bg-white flex items-center justify-center">
                  <CiImageOn className="text-[#3b82f6] text-2xl m-auto" />
                </div>
              </div>
            )}
            <div>
              <div className="line-clamp-2">{row.original.title}</div>
              <div className="text-[#969696] text-xs font-normal flex items-center space-x-2">
                <span>{formatDateAndTime(row.original?.timestamp)},</span>
                <span>{row.original?.author?.toString()}</span>
              </div>
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: "category",
      header: "Category",
      minSize: 150,
    },
    {
      accessorKey: "tag",
      header: () => "Tags",
      size: 150,
      minSize: 100,
      maxSize: 200,
      cell: ({ row }) => (
        <div className="line-clamp-3">{row.original.tag.join(", ")}</div>
      ),
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => (
        <>
          <DropdownButton
            selected={
              row.original.status || row.original.status === 0
                ? row.original.status
                : 3
            }
            handleChange={(value) =>
              handleChange({ value, id: row.original._id })
            }
          />
          {row.original.status === 4 ? (
            <div className="text-xs mt-1 text-gray-600">
              {formatDateTime(row.original?.publishDate)}
            </div>
          ) : null}
        </>
      ),
    },
    {
      accessorKey: "timestamp",
      header: "Action",
      cell: ({ row }) => {
        return (
          <div className="flex items-center gap-x-2">
            <RoundedIconsButton
              onClick={() => handleViewClick(row.original.viewLink)}
            >
              <BsEye className="h-[15px] w-[15px]" />
            </RoundedIconsButton>

            <RoundedIconsButton>
              <FiEdit
                className="h-[15px] w-[15px]"
                onClick={() => {
                  if (isShorts) {
                    handleViewClickInNewTab(
                      `/admin/edit-shorts-story/${row.original._id}`
                    );
                  } else {
                    handleViewClickInNewTab(
                      `/admin/edit-video-story/${row.original._id}`
                    );
                  }
                }}
              />
            </RoundedIconsButton>
            <RoundedIconsButton
              onClick={() => {
                openModal(row.original._id);
              }}
            >
              <FaRegTrashAlt className="h-[15px] w-[15px]" />
            </RoundedIconsButton>
          </div>
        );
      },
    },
  ];

  // funtion to perfrom action on row selection in the table
  const handleRowSelectionChange = (rowIds) => {
    console.log(rowIds, " rows ids");
  };

  // used to perform actions when a row is selected
  const handleRowSelect = (rows) => {
    console.log(rows, " rows");
    setSelectedRows(rows);
    // Make API call or perform other actions with the selected rows
  };

  // used to handle the filter change on the table for the status
  const handleFilterChange = (filterName, value) => {
    console.log(filterName, value, " filter names and value");
    // Update filter state and fetch new data
  };

  const createVideoStory = () => {
    if (isShorts) {
      handleViewClickInNewTab("/admin/add-shorts-story");
      //navigate("/admin/add-shorts-story");
    } else {
      handleViewClickInNewTab("/admin/add-video-story");
      // navigate("/admin/add-video-story");
    }
  };

  return (
    <Container>
      <div className="lg:flex items-center justify-between mb-5">
        {isPublish ? (
          <ScheduleModal
            setIsPublish={setIsPublish}
            type={"videoStory"}
            onClose={onScheduleModalClose}
            setStatus={false}
          />
        ) : null}
        <div>
          {/* <div className="flex items-center text-sm">
            <Link
              onClick={() =>
                dispatch(videoFilterStoryStatus(searchParams.get("status")))
              }
              to={
                isShorts
                  ? "/admin/shorts?status=all"
                  : "/admin/video-stories?status=all"
              }
              className="hover:bg-white text-blackShade rounded-full px-3 py-1 bg-transparent "
            >
              {isShorts ? "Shorts" : "Video"} Stories
            </Link>
            <BiChevronRight className="text-xl" />
            <p className="text-fadeGray pl-2 capitalize">
              {searchParams.get("status")}
            </p>
          </div> */}
          <BreadCrumb
            title={isShorts ? "Shorts Stories" : "Video Stories"}
            description={"Create, customize and manage your stories."}
          />
        </div>

        <Button
          rounded="full"
          size="sm"
          customClasses="mt-2 lg:mt-0 pb-1.5 pt-1.5"
          onClick={createVideoStory}
        >
          <BiPlus /> {isShorts ? "Add Shorts Story" : "Add Video Story"}
        </Button>
      </div>
      <Table
        module="videoStory"
        data={data}
        isLoading={isLoading}
        isFetching={isFetching}
        columns={columns}
        handleRowSelectionChange={handleRowSelectionChange}
        fetchMoreData={fetchMoreData}
        customClass={""}
      />

      <Filters />
      <ConfirmationModal
        isOpen={isModalOpen}
        isLoading={isDeleting}
        toggleModal={closeModal}
        message="Are you sure you want to delete this story?"
        onConfirm={handleDelete}
      />
    </Container>
  );
};

export default VideoStories;
