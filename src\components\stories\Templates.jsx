import React from "react";
import { useDispatch, useSelector } from "react-redux";
import { setTemplate } from "../../store/slices/storiesSlice";

const templates = [
  { id: 1, imgSrc: "/images/template-1.png", title: "Template 1" },
  { id: 2, imgSrc: "/images/template-2.png", title: "Template 2" },
  { id: 3, imgSrc: "/images/template-3.svg", title: "Template 3" },
  { id: 4, imgSrc: "/images/template-4.png", title: "Template 4" },
];

const Templates = () => {
  const dispatch = useDispatch();
  const {
    storiesState: { template },
  } = useSelector((state) => state.stories);
  return (
    <div className="px-2 py-5 flex flex-col gap-y-5">
      {templates.map((el) => {
        return (
          <div
            className="text-sm "
            key={el.id}
            onClick={() => dispatch(setTemplate(el.id))}
          >
            <div className="mb-2">{el.title}</div>
            <div className="">
              <img
                src={el.imgSrc}
                alt={el.title}
                className={`w-full h-full  hover:border-primary  hover:border rounded-2xl hover:cursor-pointer ${
                  template === el.id ? "border border-primary" : ""
                }`}
              />
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default Templates;
