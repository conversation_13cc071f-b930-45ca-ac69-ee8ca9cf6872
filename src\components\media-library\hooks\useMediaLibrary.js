import { useCallback } from "react";
import { useDispatch, useSelector } from "react-redux";
import { toast } from "react-toastify";
import {
	// Modal actions
	openMediaLibrary,
	closeMediaLibrary,
	setModalConfig,
	
	// Navigation actions
	setCurrentFolder,
	setFolderPath,
	setFolderHierarchy,
	addToFolderHierarchy,
	removeFromFolderHierarchy,
	navigateToFolder,
	
	// Data actions
	setFolders,
	setMedia,
	appendMedia,
	
	// Selection actions
	setSelectedFolders,
	setSelectedImages,
	setSelectedMedia,
	toggleFolderSelection,
	toggleImageSelection,
	clearSelection,
	
	// Filter actions
	setSearchFilter,
	setTypeFilter,
	setTagsFilter,
	resetFilters,
	
	// UI actions
	setShowCreateFolder,
	setShowEditMedia,
	setActiveView,
	
	// Pagination actions
	setFolderPagination,
	setMediaPagination,
	resetFolderPagination,
	resetMediaPagination,
	
	// Reset actions
	resetMedia,
	resetAll,
} from "../../../store/slices/mediaLibrarySlice";

import {
	useGetFolderListMutation,
	useGetMediaLibraryListMutation,
	useCreateFolderMutation,
	useDeleteFolderMutation,
	useDeleteMediaMutation,
	useUpdateMediaMutation,
} from "../../../store/apis/mediaLibraryApi";

/**
 * Main hook for media library functionality
 * Provides state and actions for the media library
 */
export const useMediaLibrary = () => {
	const dispatch = useDispatch();
	
	// Selectors
	const modal = useSelector((state) => state.mediaLibrary.modal);
	const navigation = useSelector((state) => state.mediaLibrary.navigation);
	const data = useSelector((state) => state.mediaLibrary.data);
	const selection = useSelector((state) => state.mediaLibrary.selection);
	const ui = useSelector((state) => state.mediaLibrary.ui);
	const filters = useSelector((state) => state.mediaLibrary.filters);
	const pagination = useSelector((state) => state.mediaLibrary.pagination);
	const upload = useSelector((state) => state.mediaLibrary.upload);
	
	// API hooks
	const [getFolderList, { isLoading: isLoadingFolders }] = useGetFolderListMutation();
	const [getMediaLibraryList, { isLoading: isLoadingMedia }] = useGetMediaLibraryListMutation();
	const [createFolder, { isLoading: isCreatingFolder }] = useCreateFolderMutation();
	const [deleteFolder, { isLoading: isDeletingFolder }] = useDeleteFolderMutation();
	const [deleteMedia, { isLoading: isDeletingMedia }] = useDeleteMediaMutation();
	const [updateMedia, { isLoading: isUpdatingMedia }] = useUpdateMediaMutation();
	
	// Modal actions
	const openModal = useCallback((config = {}) => {
		dispatch(openMediaLibrary(config));
	}, [dispatch]);
	
	const closeModal = useCallback(() => {
		dispatch(closeMediaLibrary());
	}, [dispatch]);
	
	const updateModalConfig = useCallback((config) => {
		dispatch(setModalConfig(config));
	}, [dispatch]);
	
	// Navigation actions
	const navigateToFolderAction = useCallback((folder, isRoot = false) => {
		dispatch(navigateToFolder({ folder, isRoot }));
	}, [dispatch]);
	
	const updateFolderHierarchy = useCallback((hierarchy) => {
		dispatch(setFolderHierarchy(hierarchy));
	}, [dispatch]);
	
	const addToHierarchy = useCallback((folder) => {
		dispatch(addToFolderHierarchy(folder));
	}, [dispatch]);
	
	const removeFromHierarchy = useCallback((index) => {
		dispatch(removeFromFolderHierarchy(index));
	}, [dispatch]);
	
	// Data loading actions
	const loadFolders = useCallback(async (parentId = null) => {
		try {
			const response = await getFolderList({
				parent: parentId || "",
				limit: pagination.folders.limit,
				offset: 0,
			}).unwrap();
			
			if (response.status === "success") {
				dispatch(setFolders(response.data || []));
				dispatch(setFolderPagination({
					offset: pagination.folders.limit,
					hasMore: (response.data || []).length === pagination.folders.limit,
					totalCount: response.totalCounts || 0,
				}));
			}
		} catch (error) {
			console.error("Error loading folders:", error);
			toast.error("Failed to load folders");
		}
	}, [dispatch, getFolderList, pagination.folders.limit]);
	
	const loadMediaList = useCallback(async (isNew = false) => {
		try {
			const currentOffset = isNew ? 0 : pagination.media.offset;
			
			const response = await getMediaLibraryList({
				search: filters.search || "",
				folder: navigation.currentFolder || "",
				includeSubfolders: false,
				limit: pagination.media.limit,
				offset: currentOffset,
			}).unwrap();
			
			if (response.status === "success") {
				const newMedia = response.data || [];
				if (isNew) {
					dispatch(setMedia(newMedia));
				} else {
					dispatch(appendMedia(newMedia));
				}
				
				dispatch(setMediaPagination({
					offset: currentOffset + pagination.media.limit,
					hasMore: newMedia.length === pagination.media.limit,
					totalCount: response.totalCounts || 0,
				}));
			}
		} catch (error) {
			console.error("Error loading media:", error);
			toast.error("Failed to load media");
			dispatch(setMediaPagination({ hasMore: false }));
		}
	}, [
		dispatch,
		getMediaLibraryList,
		filters.search,
		navigation.currentFolder,
		pagination.media.offset,
		pagination.media.limit,
	]);
	
	const handleLoadMore = useCallback(() => {
		if (pagination.media.hasMore && !isLoadingMedia) {
			loadMediaList(false);
		}
	}, [loadMediaList, pagination.media.hasMore, isLoadingMedia]);
	
	// Selection actions
	const handleFolderSelect = useCallback((folder) => {
		dispatch(toggleFolderSelection(folder));
	}, [dispatch]);
	
	const handleImageSelect = useCallback((mediaItem) => {
		dispatch(toggleImageSelection({ mediaId: mediaItem._id, mediaItem }));
	}, [dispatch]);
	
	const clearSelections = useCallback(() => {
		dispatch(clearSelection());
	}, [dispatch]);
	
	// Filter actions
	const updateSearchFilter = useCallback((search) => {
		dispatch(setSearchFilter(search));
	}, [dispatch]);
	
	const updateTypeFilter = useCallback((type) => {
		dispatch(setTypeFilter(type));
	}, [dispatch]);
	
	const updateTagsFilter = useCallback((tags) => {
		dispatch(setTagsFilter(tags));
	}, [dispatch]);
	
	const clearFilters = useCallback(() => {
		dispatch(resetFilters());
	}, [dispatch]);
	
	// UI actions
	const toggleCreateFolder = useCallback((show) => {
		dispatch(setShowCreateFolder(show));
	}, [dispatch]);
	
	const toggleEditMedia = useCallback((show) => {
		dispatch(setShowEditMedia(show));
	}, [dispatch]);
	
	const changeView = useCallback((view) => {
		dispatch(setActiveView(view));
	}, [dispatch]);
	
	// Utility functions
	const isFolderSelected = useCallback((folderId) => {
		return selection.selectedFolders.some(folder => 
			(folder._id || folder.id) === folderId
		);
	}, [selection.selectedFolders]);
	
	const isImageSelected = useCallback((mediaId) => {
		return selection.selectedImages.some(image => image._id === mediaId);
	}, [selection.selectedImages]);
	
	const getBreadcrumbPath = useCallback(() => {
		return [
			{ name: "Root", _id: null },
			...navigation.folderHierarchy
		];
	}, [navigation.folderHierarchy]);
	
	return {
		// State
		modal,
		navigation,
		data,
		selection,
		ui,
		filters,
		pagination,
		upload,
		
		// Loading states
		isLoadingFolders,
		isLoadingMedia,
		isCreatingFolder,
		isDeletingFolder,
		isDeletingMedia,
		isUpdatingMedia,
		
		// Modal actions
		openModal,
		closeModal,
		updateModalConfig,
		
		// Navigation actions
		navigateToFolderAction,
		updateFolderHierarchy,
		addToHierarchy,
		removeFromHierarchy,
		
		// Data actions
		loadFolders,
		loadMediaList,
		handleLoadMore,
		
		// Selection actions
		handleFolderSelect,
		handleImageSelect,
		clearSelections,
		
		// Filter actions
		updateSearchFilter,
		updateTypeFilter,
		updateTagsFilter,
		clearFilters,
		
		// UI actions
		toggleCreateFolder,
		toggleEditMedia,
		changeView,
		
		// Utility functions
		isFolderSelected,
		isImageSelected,
		getBreadcrumbPath,
		
		// API functions (for direct access if needed)
		createFolder,
		deleteFolder,
		deleteMedia,
		updateMedia,
	};
};
