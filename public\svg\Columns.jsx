import React from "react";

const Columns = ({
  width = 20,
  height = 20,
  fontSize,
  strokeWidth = 1,
  fill = "currentColor",
  stroke,
  ...rest
}) => {
  const style = fontSize ? { fontSize } : undefined;
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 50 50"
      fill={fill}
      stroke={stroke}
      strokeWidth={strokeWidth}
      style={style}
      xmlns="http://www.w3.org/2000/svg"
      {...rest}
    >
      <path
        fill={fill}
        d="M3 2h11a1 1 0 0 1 1 1v44a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V3a1 1 0 0 1 1-1zm0 1v44h11V3H3zm15-1h14a1 1 0 0 1 1 1v44a1 1 0 0 1-1 1H18a1 1 0 0 1-1-1V3a1 1 0 0 1 1-1zm0 1v44h14V3H18zm18-1h11a1 1 0 0 1 1 1v44a1 1 0 0 1-1 1H36a1 1 0 0 1-1-1V3a1 1 0 0 1 1-1zm0 1v44h11V3H36z"
      ></path>
    </svg>
  );
};

export default Columns;
