import { createSlice } from "@reduxjs/toolkit";
import {
  filterStoryStatusState,
  incrementOffsetState,
  resetFiltersState,
  setFetchedDataState,
  storiesInputFilterState,
} from "./sharedReducers";

const initialState = {
  data: [],
  hasMore: true,
  filter: { category: [], search: "", type: null, level: 0, id: null },
  limit: 20,
  subCategoriesData: [],
  subCategoryLimit: 20,
  subCategoryOffset: 0,
  subCategoryFilter: {
    search: "",
    type: null,
    level: 0,
    id: null,
  },
  offset: 0,
  subCategoriesHasMore: true,
};

export const categorieSlice = createSlice({
  name: "categories",
  initialState,
  reducers: {
    subCategoryFilterStoryStatus: (state, { payload }) =>
      filterStoryStatusState(state, payload),
    categoriesInputFilter: (state, { payload }) =>
      storiesInputFilterState(state, payload),
    setFetchedData: (state, { payload }) => setFetchedDataState(state, payload),
    resetFilters: (state, { payload }) => resetFiltersState(state),
    incrementOffset: (state) => incrementOffsetState(state),
    setInitialCategory: (state, { payload }) => {
      state.filter.category = payload;
    },
    resetOffset: (state) => {
      state.offset = 0;
    },
    setSubCategoryInputFilter: (state, { payload }) => {
      const { name, value } = payload;
      state.subCategoryFilter[name] = value;
    },
    setSubCategoryOffset: (state, { payload }) => {
      state.subCategoryOffset += state.subCategoryLimit;
    },
    setSubCategoryFetchedData: (state, { payload }) => {
      const { data, replace = false } = payload;
      state.subCategoriesData = replace
        ? data
        : [...state.subCategoriesData, ...data];
      state.subCategoriesHasMore = data.length === 20;
    },
    resetSubCategoryFilters: (state, { payload }) => {
      state.subCategoryFilter = {
        search: null,
        type: null,
        level: 0,
        id: null,
      };
      state.subCategoryOffset = 0;
      state.subCategoriesHasMore = true;
    },
    setSubCategoryFilter: (state, { payload }) => {
      state.subCategoryFilter.type = parseInt(payload.type);
      state.subCategoryFilter.level = parseInt(payload.level);
    },
    resetCategoryState: () => initialState,
  },
});
// Action creators are generated for each case reducer function
export const {
  categoriesInputFilter,
  setFetchedData,
  resetFilters,
  incrementOffset,
  setInitialCategory,
  subCategoryFilterStoryStatus,
  resetOffset,
  setSubCategoryInputFilter,
  setSubCategoryOffset,
  resetSubCategoryFilters,
  setSubCategoryFetchedData,
  setSubCategoryFilter,
  resetCategoryState,
} = categorieSlice.actions;

export default categorieSlice.reducer;
