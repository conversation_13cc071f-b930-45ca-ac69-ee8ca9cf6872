import React, { useCallback, useEffect, useRef, useState } from "react";
import { EditorContent, useEditor } from "@tiptap/react";
import "../../styles/blockeditor.css";
import InlineToolbar from "./InlineToolbar";
// import ImageBlockMenu from "../../helpers/extension/Image/components/ImageBlockMenu";
// import VideoBlockMenu from "../../helpers/extension/Video/components/VideoBlockMenu";
// import EmbedMenu from "../../helpers/extension/Embed/components/EmbedBlockMenu";
import LinkMenu from "../../helpers/extension/Link/components/LinkMenu";
import { useDispatch, useSelector } from "react-redux";
import { setEditorContent, setMetaData } from "../../store/slices/storiesSlice";
import { extensions, menu } from "../../helpers/TipTapConfig";
import ImageBlockMenu from "../../helpers/extension/Image/components/ImageBlockMenu";

const BlockEditor = ({}) => {
  const dispatch = useDispatch();
  const menuContainerRef = useRef(null);
  const [showLink, setShowLink] = useState(false);
  const [open, setOpen] = useState(true);
  const {
    storiesState: { content },
  } = useSelector((state) => state.stories);
  const editor = useEditor({
    immediatelyRender: true,
    autofocus: true,
    onUpdate: useCallback(
      ({ editor }) => {
        const getContent = editor.getJSON();
        const description = getContent?.content?.find(
          (element) => element.type === "paragraph"
        );

        if (description && description?.content?.length > 0) {
          const getDesc = description?.content
            .map((data) => data.text || "")
            .join("");
          dispatch(setMetaData({ name: "description", value: getDesc }));
        } else {
          dispatch(setMetaData({ name: "description", value: "" }));
        }

        if (JSON.stringify(getContent) !== JSON.stringify(content)) {
          dispatch(setEditorContent(getContent));
        }
      },
      [content]
    ),
    extensions: extensions,
  });

  useEffect(() => {
    if (editor && content) {
      const currentContent = editor.getJSON();
      if (JSON.stringify(currentContent) !== JSON.stringify(content)) {
        editor.commands.setContent(content, false, {
          preserveScrollPosition: true,
        });
      }
    }
  }, [editor, content]);
  return (
    <div ref={menuContainerRef}>
      <InlineToolbar setOpen={setOpen} editor={editor} />
      <EditorContent editor={editor} />
      <ImageBlockMenu editor={editor} appendTo={menuContainerRef} />
      {/* <VideoBlockMenu editor={editor} appendTo={menuContainerRef} /> */}
      {/* <EmbedMenu editor={editor} appendTo={menuContainerRef} /> */}
      {/* <LinkMenu
        editor={editor}
        appendTo={menuContainerRef}
        showLink={showLink}
        setShowLink={setShowLink}
      /> */}
    </div>
  );
};

export default BlockEditor;
