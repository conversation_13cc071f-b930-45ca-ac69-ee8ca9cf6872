import { useEffect, useCallback, useRef } from "react";
import { NodeViewWrapper } from "@tiptap/react";
import GalleryLayout from "../GalleryLayout";

export const GalleryView = (props) => {
  const imageWrapperRef = useRef(null);
  const { editor, getPos, node } = props;
  const { data, layout, properties, width, alignment } = node.attrs;
  const parentDiv = document.querySelector(".node-galleryBlock")
    ? document.querySelector(".node-galleryBlock")
    : null;
  useEffect(() => {
    const updateMaxWidth = () => {
      if (parentDiv) {
        if (parentDiv.querySelector(".original-view")) {
          parentDiv.style.maxWidth = "100rem";
        } else {
          parentDiv.style.maxWidth = "";
        }
      }
    };

    updateMaxWidth();

    const observer = new MutationObserver(() => {
      updateMaxWidth();
    });

    if (parentDiv) {
      observer.observe(parentDiv, {
        childList: true,
        subtree: true,
        attributes: true,
        attributeFilter: ["class"],
      });
    }

    return () => observer.disconnect();
  }, [parentDiv]);

  let wrapperClassName = "";
  if (alignment === "left") {
    wrapperClassName = "ml-0";
  } else if (alignment === "right") {
    wrapperClassName = "ml-auto";
  } else if (alignment === "center") {
    wrapperClassName = "mx-auto";
  } else {
    wrapperClassName = "ml-auto mr-auto";
  }

  const onClick = useCallback(() => {
    editor.commands.setNodeSelection(getPos());
  }, [getPos, editor.commands]);

  return (
    <NodeViewWrapper>
      <div
        className={`${wrapperClassName} ${
          width === "100vw" ? "original-view" : ""
        }`}
        style={{
          width: width === "100vw" ? `calc(100vw - 115px)` : width,
        }}
        onClick={onClick}
      >
        <div
          className="image-wrapper"
          contentEditable={false}
          ref={imageWrapperRef}
        >
          <GalleryLayout layout={layout} properties={properties} data={data} />
        </div>
      </div>
    </NodeViewWrapper>
  );
};

export default GalleryView;
