import React, { use<PERSON>allback, useState } from "react";
import { Form<PERSON>rovider, useForm } from "react-hook-form";
import { Link, useNavigate } from "react-router-dom";
import { DropdownMenu, InputField } from "../parts/FormComponents";
import { HiOutlineMail } from "react-icons/hi";
import { CiLock } from "react-icons/ci";
import { IoMdLock } from "react-icons/io";
import Button from "../parts/Button";
import { usePostLoginMutation } from "../store/apis/userApi";
import { toast } from "react-toastify";
import { login } from "../store/slices/userSlice";
import { useDispatch } from "react-redux";
import { useAuthorLoginMutation } from "../store/apis/authorsApi";
import { FiEye, FiEyeOff } from "react-icons/fi";

const Signin = () => {
	const navigate = useNavigate();
	const dispatch = useDispatch();
	const [postLogin, { isLoading, data }] = usePostLoginMutation();
	const [authorLogin, { data: authorRes, isLoading: authorLoading }] = useAuthorLoginMutation();

	const [role, setRole] = useState("author");
	const [email, setEmail] = useState("");
	const [passwordShow, setPasswordShow] = useState(false);

	const {
		register,
		handleSubmit,
		reset,
		getFieldState,
		getValues,
		setValue,
		control,
		watch,
		formState: { errors, isValid },
	} = useForm({
		//    resolver: yupResolver(stepsValidationSchema[current]),
	});

	const onSubmit = (payload) => {
		if (!role) {
			toast.error("PLease select role");
			return;
		}
		if (role == "admin") {
			postLogin(payload)
				.unwrap()
				.then()
				.catch((err) => {
					toast.error(err?.data?.message, { toastId: "loginError" });
				});
		} else {
			authorLogin({ ...payload, email })
				.unwrap()
				.then()
				.catch((err) => {
					toast.error(err?.data?.message, { toastId: "loginError" });
				});
		}
	};

	const handleClick = useCallback(() => {
		setPasswordShow(!passwordShow);
	}, [passwordShow]);

	if (data && !isLoading) {
		toast.success("Login successful", { toastId: "loginSuccessMsg" });
		dispatch(login({ token: data.token, user: data.data }));
		navigate("/admin/home");
	} else if (authorRes && !authorLoading) {
		toast.success("Login successful", { toastId: "loginSuccessMsg" });
		dispatch(login({ token: authorRes.token, user: authorRes.data }));
		navigate("/admin/home");
	}
	return (
		<div className="flex justify-center items-center h-screen w-full gradient">
			<div className="flex flex-col gap-y-3 w-full md:w-1/2 xl:w-1/3 shadow-md   p-10 rounded-xl bg-white">
				<div>
					<h2 className="text-lg font-bold text-center">Login</h2>
				</div>
				<FormProvider
					getFieldState={getFieldState}
					getValues={getValues}
					setValue={setValue}
					watch={watch}
					register={register}
					errors={errors}
					control={control}
				>
					<form className="" onSubmit={handleSubmit(onSubmit)}>
						<select
							value={email}
							onChange={(e) => setEmail(e.target.value)}
							className={`border text-sm border-gray-300 ${
								!email ? "text-gray-500" : ""
							} rounded-md px-4 py-2 mb-5 w-full focus:outline-none focus:border-blue-500`}
						>
							<option value={""}>Select Brand</option>
							<option name="email" value="<EMAIL>">
								THR
							</option>
							<option name="email" value="<EMAIL>">
								Manifest
							</option>
							<option name="email" value="<EMAIL>">
								Esquire
							</option>
							<option name="email" value="<EMAIL>">
								Robb Report
							</option>
						</select>
						<InputField
							name="authorEmail"
							startIcon={<HiOutlineMail className="text-xl text-fadeGray" />}
							placeholder="Enter your email"
						/>
						{/* {role == "author" && (
              <InputField
                name="username"
                startIcon={<HiOutlineMail className="text-xl text-fadeGray" />}
                placeholder="Enter your user name"
              />
            )} */}
						<InputField
							name="password"
							type={passwordShow ? "text" : "password"}
							startIcon={<IoMdLock className="text-xl text-fadeGray" />}
							placeholder="Enter your password"
							endIcon={
								passwordShow ? (
									<FiEyeOff onClick={handleClick} className="cursor-pointer" />
								) : (
									<FiEye onClick={handleClick} className="cursor-pointer" />
								)
							}
						/>
						<Button type="submit" customClasses="w-full">
							{isLoading ? "Please wait..." : "Sign in"}
						</Button>
					</form>
				</FormProvider>

				<div>
					<p className="text-center text-[12px]">
						Developed by <span className="font-semibold">RPSG Media</span>
					</p>
				</div>
			</div>
		</div>
	);
};

export default Signin;
