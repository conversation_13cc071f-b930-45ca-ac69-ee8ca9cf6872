import { createSlice } from "@reduxjs/toolkit";
import {
  filterStoryStatusState,
  incrementOffsetState,
  resetFiltersState,
  setFetchedDataState,
  storiesInputFilterState,
} from "./sharedReducers";

const initialState = {
  data: [],
  hasMore: true,
  filter: { section: [], search: null, status: null },
  limit: 20,
  offset: 0,
};

export const flaggedStories = createSlice({
  name: "flaggedStory",
  initialState,
  reducers: {
    flaggedStoryFilterStoryStatus: (state, { payload }) =>
      filterStoryStatusState(state, payload),
    flaggedStoryInputFilter: (state, { payload }) =>
      storiesInputFilterState(state, payload),
    setFetchedData: (state, { payload }) => setFetchedDataState(state, payload),
    resetFilters: (state, { payload }) => resetFiltersState(state),
    resetSubFlagFilter: (state, { payload }) => {
      state.filter.search = null;
      state.filter.status = null;
      state.limit = 20;
      state.hasMore = true;
      state.offset = 0;
      state.filter.section = state.filter.section;
    },
    incrementOffset: (state) => incrementOffsetState(state),
    setInitialFlaggedStory: (state, { payload }) => {
      state.filter.section = payload;
    },
    resetData: (state) => (state = initialState),
    resetOffset: (state) => {
      state.offset = 0;
    },
    setNonSubCatArticleFilter: (state, { payload }) => {},
    resetFlaggedStorieState: () => initialState,
  },
});
// Action creators are generated for each case reducer function
export const {
  flaggedStoryFilterStoryStatus,
  flaggedStoryInputFilter,
  setFetchedData,
  resetFilters,
  incrementOffset,
  resetSubFlagFilter,
  setInitialFlaggedStory,
  resetData,
  resetOffset,
  resetFlaggedStorieState,
} = flaggedStories.actions;

export default flaggedStories.reducer;
