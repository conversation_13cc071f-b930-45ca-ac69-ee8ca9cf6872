import React from "react";
import { useSelectionManager } from "../hooks/useSelectionManager";

/**
 * Component for displaying selection information and actions
 */
const SelectionInfo = ({ 
	onClearSelection,
	onAddToPage,
	showAddToPage = true,
	className = "" 
}) => {
	const { 
		getSelectedItems, 
		getSelectionSummary, 
		isSelectionValid,
		clearAllSelections 
	} = useSelectionManager();
	
	const selectedItems = getSelectedItems();
	const selectionSummary = getSelectionSummary();
	const canAddToPage = isSelectionValid();
	
	const handleClearSelection = () => {
		if (onClearSelection) {
			onClearSelection();
		} else {
			clearAllSelections();
		}
	};
	
	const handleAddToPage = () => {
		if (onAddToPage && canAddToPage) {
			onAddToPage();
		}
	};
	
	if (!selectedItems.hasSelection) {
		return (
			<div className={`text-sm text-gray-500 ${className}`}>
				<p>No items selected</p>
			</div>
		);
	}
	
	return (
		<div className={`bg-blue-50 border border-blue-200 rounded-lg p-3 ${className}`}>
			<div className="flex items-center justify-between">
				{/* Selection Summary */}
				<div className="flex items-center space-x-3">
					<div className="flex items-center space-x-2">
						{/* Selection Icon */}
						<div className="w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center">
							<svg 
								className="w-3 h-3 text-white" 
								fill="currentColor" 
								viewBox="0 0 20 20"
							>
								<path 
									fillRule="evenodd" 
									d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" 
									clipRule="evenodd" 
								/>
							</svg>
						</div>
						
						{/* Selection Text */}
						<span className="text-sm font-medium text-blue-700">
							{selectionSummary}
						</span>
					</div>
					
					{/* Selection Details */}
					<div className="text-xs text-blue-600">
						{selectedItems.folders.length > 0 && (
							<span className="mr-2">
								📁 {selectedItems.folders.length}
							</span>
						)}
						{selectedItems.media.length > 0 && (
							<span>
								🖼️ {selectedItems.media.length}
							</span>
						)}
					</div>
				</div>
				
				{/* Actions */}
				<div className="flex items-center space-x-2">
					{/* Clear Selection Button */}
					<button
						onClick={handleClearSelection}
						className="px-3 py-1 text-xs text-blue-600 hover:text-blue-800 hover:bg-blue-100 rounded-md transition-colors"
						title="Clear selection"
					>
						Clear
					</button>
					
					{/* Add to Page Button */}
					{showAddToPage && (
						<button
							onClick={handleAddToPage}
							disabled={!canAddToPage}
							className={`px-4 py-1 text-xs font-medium rounded-md transition-colors ${
								canAddToPage
									? "bg-blue-600 text-white hover:bg-blue-700"
									: "bg-gray-300 text-gray-500 cursor-not-allowed"
							}`}
							title={canAddToPage ? "Add selected items to page" : "Invalid selection"}
						>
							Add to Page
						</button>
					)}
				</div>
			</div>
			
			{/* Selected Items Preview */}
			{selectedItems.hasSelection && (
				<div className="mt-3 pt-3 border-t border-blue-200">
					<div className="flex flex-wrap gap-2">
						{/* Show selected folders */}
						{selectedItems.folders.map((folder) => (
							<div
								key={folder._id || folder.id}
								className="flex items-center space-x-1 px-2 py-1 bg-white rounded-md border border-blue-200 text-xs"
							>
								<svg className="w-3 h-3 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
									<path d="M2 6a2 2 0 012-2h5l2 2h5a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6z" />
								</svg>
								<span className="text-blue-700 truncate max-w-20" title={folder.name}>
									{folder.name}
								</span>
							</div>
						))}
						
						{/* Show selected media */}
						{selectedItems.media.slice(0, 5).map((media) => (
							<div
								key={media._id}
								className="flex items-center space-x-1 px-2 py-1 bg-white rounded-md border border-blue-200 text-xs"
							>
								{media.url ? (
									<img 
										src={media.url} 
										alt={media.title || media.alt} 
										className="w-4 h-4 object-cover rounded"
									/>
								) : (
									<svg className="w-3 h-3 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
										<path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd" />
									</svg>
								)}
								<span className="text-blue-700 truncate max-w-20" title={media.title || media.alt}>
									{media.title || media.alt || "Untitled"}
								</span>
							</div>
						))}
						
						{/* Show "and X more" if there are more items */}
						{selectedItems.media.length > 5 && (
							<div className="flex items-center px-2 py-1 bg-blue-100 rounded-md text-xs text-blue-600">
								+{selectedItems.media.length - 5} more
							</div>
						)}
					</div>
				</div>
			)}
		</div>
	);
};

export default SelectionInfo;
