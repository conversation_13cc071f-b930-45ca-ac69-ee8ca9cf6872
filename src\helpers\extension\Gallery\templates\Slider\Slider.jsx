import React, { useRef } from "react";

const Slider = ({
  data = [],
  properties = {
    spacing: 5,
    resize: "crop", // "crop" or "contain"
    ratio: "16/9", // Aspect ratio format
  },
}) => {
  const { spacing, resize, ratio } = properties;
  const containerRef = useRef(null);

  const scrollByAmount = () => {
    if (!containerRef.current) return 0;
    const containerWidth = containerRef.current.clientWidth;
    return containerWidth * 0.8; // scroll 80% of the container
  };

  const scrollNext = () => {
    if (containerRef.current) {
      containerRef.current.scrollBy({
        left: scrollByAmount(),
        behavior: "smooth",
      });
    }
  };

  const scrollPrev = () => {
    if (containerRef.current) {
      containerRef.current.scrollBy({
        left: -scrollByAmount(),
        behavior: "smooth",
      });
    }
  };

  const buttonStyle = {
    position: "absolute",
    top: "50%",
    transform: "translateY(-50%)",
    backgroundColor: "rgba(0,0,0,0.5)",
    border: "none",
    color: "white",
    padding: "8px",
    cursor: "pointer",
    borderRadius: "50%",
    zIndex: 10,
  };

  return (
    <div className="relative w-full">
      {/* Scrollable Slider */}
      <div
        ref={containerRef}
        className="flex overflow-x-auto scroll-smooth scrollbar"
        style={{ gap: `${spacing}px` }}
      >
        {data.map((item, index) => (
          <div
            key={index}
            className="relative shrink-0 overflow-hidden"
            style={{
              width: `calc(100% / 1.2)`,
              aspectRatio: ratio,
              flex: "0 0 auto",
            }}
          >
            <img
              src={item.src}
              alt={item.alt || `Slide ${index}`}
              className={`absolute w-full h-full ${
                resize === "crop" ? "object-cover" : "object-contain"
              }`}
            />
          </div>
        ))}
      </div>

      {/* Prev Button */}
      <button
        style={{ ...buttonStyle, left: 10 }}
        onClick={scrollPrev}
        aria-label="Previous"
      >
        {/* Left arrow SVG */}
        <svg
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          viewBox="0 0 24 24"
          width="16"
          height="16"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            d="M15 19l-7-7 7-7"
          />
        </svg>
      </button>

      {/* Next Button */}
      <button
        style={{ ...buttonStyle, right: 10 }}
        onClick={scrollNext}
        aria-label="Next"
      >
        {/* Right arrow SVG */}
        <svg
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          viewBox="0 0 24 24"
          width="16"
          height="16"
        >
          <path strokeLinecap="round" strokeLinejoin="round" d="M9 5l7 7-7 7" />
        </svg>
      </button>
    </div>
  );
};

export default Slider;
