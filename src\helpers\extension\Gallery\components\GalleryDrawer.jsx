import React, { useState } from "react";
import { FiChevronLeft, FiX } from "react-icons/fi";
import { Media, Layout, Edit } from "./tabs";

const tabs = [
  { label: "Media", value: "media" },
  { label: "Layout", value: "layout" },
];
const GalleryDrawer = ({ editor, open, setOpen }) => {
  const [active, setActive] = useState("media");
  const [isEdit, setIsEdit] = useState(null);
  const {
    data = [],
    layout = "grid",
    properties = {},
  } = editor?.getAttributes("galleryBlock") || {};

  const handleTabClick = (tab) => {
    setActive(tab);
  };

  return (
    <>
      <div
        className={`${
          open ? "block" : "hidden"
        } fixed top-0 right-0 z-[20] h-screen w-full bg-slate-600 bg-opacity-40 rounded-r-md shadow-2xl transition-all duration-800 ease-in-out overflow-hidden`}
      >
        <div className="w-full md:w-96 bg-white absolute right-0 top-0 h-screen text-fadeGray">
          <div className="menu-drawer">
            <div className="!p-4 headsec">
              {isEdit !== null ? (
                <>
                  <div className="flex gap-4">
                    <FiChevronLeft
                      className="close"
                      onClick={() => setIsEdit(null)}
                    />
                    <h2 className="heading">Image</h2>
                  </div>
                  <FiX className="close" onClick={() => setIsEdit(null)} />
                </>
              ) : (
                <>
                  <h2 className="heading">Gallery</h2>
                  <FiX className="close" onClick={() => setOpen(false)} />
                </>
              )}
            </div>
            <div className="w-full h-[calc(100vh-58px)] overflow-y-scroll">
              {isEdit === null && (
                <div className="grid grid-cols-2 -mb-px w-full border-b">
                  {tabs.map((tab) => (
                    <div key={tab.value} className="me-2">
                      <button
                        onClick={() => handleTabClick(tab.value)}
                        className={`inline-block w-full p-3 border-b-2 rounded-t-lg hover:text-gray-600 transition-all duration-300 ${
                          active === tab.value
                            ? "text-primary border-primary"
                            : "border-transparent"
                        }`}
                      >
                        <h3 className="text-base">{tab.label}</h3>
                      </button>
                    </div>
                  ))}
                </div>
              )}
              {isEdit !== null && (
                <Edit
                  editor={editor}
                  data={data}
                  current={isEdit}
                  setCurrent={setIsEdit}
                />
              )}
              {active === "media" && isEdit === null && (
                <Media editor={editor} data={data} setIsEdit={setIsEdit} />
              )}
              {active === "layout" && isEdit === null && (
                <Layout
                  editor={editor}
                  layout={layout}
                  properties={properties}
                />
              )}
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default GalleryDrawer;
