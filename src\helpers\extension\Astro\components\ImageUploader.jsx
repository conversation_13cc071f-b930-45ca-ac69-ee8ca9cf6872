import { CiImageOn } from "react-icons/ci";
import Button from "../../../../parts/Button";
import { FiUpload } from "react-icons/fi";

export const ImageUploader = ({ editor, handleUploadClick }) => {
  return (
    <div
      className={
        "flex flex-col items-center justify-center px-8 py-10 rounded-lg bg-opacity-80"
      }
      contentEditable={false}
    >
      <CiImageOn
        name="Image"
        className="text-5xl mb-4 text-fadeGray opacity-20"
      />
      <div className="flex flex-col items-center justify-center gap-2">
        <div>
          <Button
            rounded="full"
            customClasses={"text-sm"}
            onClick={handleUploadClick}
          >
            <FiUpload className="mr-2" />
            Upload an image
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ImageUploader;
