import React from "react";

const Thumbnails = ({
  width = 20,
  height = 20,
  fontSize,
  strokeWidth = 1,
  fill = "currentColor",
  stroke,
  ...rest
}) => {
  const style = fontSize ? { fontSize } : undefined;
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 50 50"
      fill={fill}
      stroke={stroke}
      strokeWidth={strokeWidth}
      style={style}
      xmlns="http://www.w3.org/2000/svg"
      {...rest}
    >
      <path fill={fill} d="M3 2h44a1 1 0 0 1 1 1v31a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V3a1 1 0 0 1 1-1zm0 1v31h44V3H3zm0 34h12a1 1 0 0 1 1 1v9a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1v-9a1 1 0 0 1 1-1zm0 1v9h12v-9H3zm16-1h12a1 1 0 0 1 1 1v9a1 1 0 0 1-1 1H19a1 1 0 0 1-1-1v-9a1 1 0 0 1 1-1zm0 1v9h12v-9H19zm16-1h12a1 1 0 0 1 1 1v9a1 1 0 0 1-1 1H35a1 1 0 0 1-1-1v-9a1 1 0 0 1 1-1zm0 1v9h12v-9H35zM11.32 20.616a.5.5 0 1 1-.64.768l-3-2.5a.5.5 0 0 1 0-.768l3-2.5a.5.5 0 1 1 .64.768L8.781 18.5l2.54 2.116zm27.36-4.232a.5.5 0 1 1 .64-.768l3 2.5a.5.5 0 0 1 0 .768l-3 2.5a.5.5 0 1 1-.64-.768l2.539-2.116-2.54-2.116z"></path>
    </svg>
  );
};

export default Thumbnails;
