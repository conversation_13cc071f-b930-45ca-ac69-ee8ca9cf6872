import { ReactNodeView<PERSON><PERSON>er } from "@tiptap/react";
import { Node } from "@tiptap/core";
import RelatedPostsView from "./components/RelatedPostsView";

export const RelatedPosts = Node.create({
  name: "relatedPosts",

  group: "block",

  draggable: true,

  isolating: true,

  selectable: true,

  addAttributes() {
    return {
      postIds: {
        default: [],
        parseHTML: (element) =>
          (element.getAttribute("data-posts-ids") || "").split(","),
        renderHTML: (attributes) => ({
          "data-posts-ids": attributes.postIds.join(","),
        }),
      },
      title: {
        default: "Related Stories",
        parseHTML: (element) =>
          element.getAttribute("data-layout") || "Related Stories",
        renderHTML: (attributes) => ({
          "data-title": attributes.title,
        }),
      },
      layout: {
        default: "grid",
        parseHTML: (element) => element.getAttribute("data-layout") || "grid",
        renderHTML: (attributes) => ({
          "data-layout": attributes.layout,
        }),
      },
      width: {
        default: "100%",
        parseHTML: (element) => element.getAttribute("data-width") || "100%",
        renderHTML: (attributes) => ({
          "data-width": attributes.width,
        }),
      },
      align: {
        default: "center",
        parseHTML: (element) => element.getAttribute("data-align") || "center",
        renderHTML: (attributes) => ({
          "data-align": attributes.align,
        }),
      },
    };
  },

  parseHTML() {
    return [
      {
        tag: 'div[data-type="relatedPosts"]',
      },
    ];
  },

  renderHTML({ HTMLAttributes }) {
    return ["div", { "data-type": "relatedPosts", ...HTMLAttributes }];
  },

  addCommands() {
    return {
      setRelatedPosts:
        (attrs) =>
        ({ commands }) => {
          if (!attrs.postIds || !Array.isArray(attrs.postIds)) return false;
          return commands.insertContent({
            type: this.name,
            attrs: {
              postIds: attrs.postIds,
              title: attrs.title || "You may also like",
              layout: attrs.layout || "grid",
              width: attrs.width || "100%",
              align: attrs.align || "center",
            },
          });
        },

      updateRelatedPosts:
        (postIds) =>
        ({ commands }) => {
          if (!Array.isArray(postIds)) return false;
          return commands.updateAttributes("relatedPosts", { postIds });
        },

      setRelatedPostsTitle:
        (title) =>
        ({ commands }) =>
          commands.updateAttributes("relatedPosts", { title }),

      setRelatedPostsLayout:
        (layout) =>
        ({ commands }) =>
          commands.updateAttributes("relatedPosts", { layout }),

      setRelatedPostsWidth:
        (width) =>
        ({ commands }) =>
          commands.updateAttributes("relatedPosts", {
            width: `${Math.max(0, Math.min(100, width))}%`,
          }),

      setRelatedPostsAlign:
        (align) =>
        ({ commands }) =>
          commands.updateAttributes("relatedPosts", { align }),
    };
  },

  addNodeView() {
    return ReactNodeViewRenderer(RelatedPostsView);
  },
});

export default RelatedPosts;
