import React, { useState } from "react";
import { AiFillHome } from "react-icons/ai";
import {
	BiChevronDown,
	BiChevronLeft,
	BiChevronRight,
	BiChevronUp,
	BiHome,
	BiMenu,
	BiSolidCategory,
} from "react-icons/bi";
import { FaTags, FaUserCircle, FaUserEdit } from "react-icons/fa";
import { HiSpeakerphone } from "react-icons/hi";
import { IoMdAnalytics, IoMdLogOut } from "react-icons/io";
import { IoFlag, IoSettings } from "react-icons/io5";
import { MdEditDocument, MdPhotoLibrary } from "react-icons/md";
import { SiGoogleforms } from "react-icons/si";
import { useDispatch, useSelector } from "react-redux";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { logout } from "../store/slices/userSlice";
import { toast } from "react-toastify";
import ConfirmationModal from "./ConfirmationModal";
import useConfirmationModal from "../utils/useConfirmationModal";
import { filterStoryStatus } from "../store/slices/storiesSlice";

// menu items for the sidebar
const navMenus = [
	{ name: "Home", icons: <AiFillHome />, path: "/admin/home" },
	{
		name: "Analytics",
		icons: <IoMdAnalytics />,
		// path: "/analytics",
		submenu: [
			{
				name: "Traffic Overview",
				icons: null,
				path: "/admin/analytics/overview",
			},
			{
				name: "Realtime Overview",
				icons: null,
				path: "/admin/analytics/realtime-overview",
			},
			{
				name: "Behaviour Overview",
				icons: null,
				path: "/admin/analytics/behaviour-overview",
			},
			{
				name: "Story Overview",
				icons: null,
				path: "/admin/analytics/story-overview",
			},
			{
				name: "Marketing Overview",
				icons: null,
				path: "/admin/analytics/marketing-overview",
			},
		],
	},
	{
		name: "Stories",
		icons: <MdEditDocument />,
		// path: "/stories",
		submenu: [
			{
				name: "Text Stories",
				icons: null,
				path: "/admin/text-stories?status=all",
			},
			{
				name: "Video Stories",
				icons: null,
				path: "/admin/video-stories?status=all",
			},
			{
				name: "Shorts Stories",
				icons: null,
				path: "/admin/shorts?status=all",
			},
			{
				name: "Web Stories",
				icons: null,
				path: "/admin/web-stories?status=all",
			},
		],
	},
	{
		name: "Categories",
		icons: <BiSolidCategory />,
		path: "/admin/categories",
	},
	{
		name: "Flagged Stories",
		icons: <IoFlag />,
		path: "/admin/flagged-stories",
	},
	{ name: "Authors", icons: <FaUserEdit />, path: "/admin/authors" },
	{ name: "Tags", icons: <FaTags />, path: "/admin/tags" },
	{ name: "Media Library", icons: <MdPhotoLibrary />, path: "/admin/tags" },
	{
		name: "Forms & Submissions",
		icons: <SiGoogleforms />,
		// path: "/",
		submenu: [
			{ name: "Contact", icons: null, path: "/admin/forms/contact" },
			{
				name: "Contact Form Submission",
				icons: null,
				path: "/admin/forms/contact-submit",
			},
			{
				name: "Email Contacts",
				icons: null,
				path: "/admin/email/contacts",
			},
			{
				name: "Email Groups",
				icons: null,
				path: "/admin/email/groups",
			},
			{
				name: "Bulk Email",
				icons: null,
				path: "/admin/email/campaign",
			},
			{
				name: "Email Analytics",
				icons: null,
				path: "/admin/email/analytics",
			},
		],
	},
	{
		name: "Marketing",
		icons: <HiSpeakerphone />,
		// path: "/",
		submenu: [
			{ name: "SMS Marketing", icons: null, path: "/admin/marketing/sms" },
			{
				name: "Whatsapp Marketing",
				icons: null,
				path: "/admin/marketing/whatsapp",
			},
			{ name: "Email Marketing", icons: null, path: "/admin/marketing/email" },
		],
	},
	{ name: "Settings", icons: <IoSettings />, path: "/admin/settings" },
];

const MenuItem = ({ menu, isOpen, setOpenMenus, openSideBar }) => {
	const location = useLocation();
	const navigate = useNavigate();
	const dispatch = useDispatch();
	const isActive = location.pathname === menu.path;
	const isSubmenuActive = menu.submenu?.some((item) => location.pathname === item.path);

	const toggleSubmenu = () => {
		setOpenMenus((prev) => ({
			...prev,
			[menu.name]: !prev[menu.name],
		}));
		navigate("/admin/text-stories?status=all");
	};

	const removeStatusAll = (url) => {
		return url.replace(/\?status=all($|&)/, "");
	};
	// rendering the submenus which are there
	if (menu.submenu) {
		return (
			<div className="w-full">
				<button
					onClick={toggleSubmenu}
					className={`flex ${!openSideBar && "justify-center"} items-center w-full px-3 h-9 ${
						isOpen && "bg-[#3e3e3e]"
					} hover:bg-[#5e5e5e] hover:text-gray-200 transition-all rounded duration-200
            ${isSubmenuActive ? "bg-[#3e3e3e] text-gray-200" : ""}`}
				>
					<div
						className={`flex items-center gap-x-3 whitespace-nowrap  ${
							openSideBar ? "" : "justify-center"
						}`}
					>
						<span className="text-xl">{menu.icons}</span>
						{openSideBar && <span className="text-sm font-bold">{menu.name}</span>}
					</div>
					{openSideBar && (
						<span className="ml-auto">
							{isOpen ? <BiChevronUp className="w-4 h-4" /> : <BiChevronDown className="w-4 h-4" />}
						</span>
					)}
				</button>
				{isOpen && openSideBar && (
					<div className="bg-[#3e3e3e] transition-all flex flex-col py-2 duration-300 ease-in-out scrollbar">
						<div>
							{menu.submenu.map((submenu, index) => {
								return (
									<div className="py-[1px]">
										<Link
											key={index}
											to={submenu.path}
											onClick={() => {
												if (submenu.path === "/admin/text-stories?status=all") {
													dispatch(filterStoryStatus("all"));
												}
											}}
											className={`flex items-center pl-11 gap-x-3 pr-4 mx-auto py-1 w-11/12 hover:bg-[#5e5e5e] hover:text-gray-200 rounded-md transition-all duration-200
                  ${
										location.pathname === removeStatusAll(submenu.path)
											? "bg-[#5e5e5e] text-gray-200"
											: ""
									}`}
										>
											<span className="w-1.5 h-1.5 bg-white rounded-full"></span>
											<span className="text-sm">{submenu.name}</span>
										</Link>
									</div>
								);
							})}
						</div>
					</div>
				)}
			</div>
		);
	}

	// rendering the sidebar items which don't have the submenus
	return (
		<Link
			to={menu.path}
			onClick={() => setOpenMenus(false)}
			className={`flex items-center rounded ${
				openSideBar ? "" : "justify-center"
			} px-3 w-full h-9 gap-x-3 hover:bg-[#5e5e5e] hover:text-gray-200 transition-all duration-200
        ${isActive ? "bg-[#5e5e5e] text-gray-200" : ""}`}
		>
			<span className="text-xl">{menu.icons}</span>
			{openSideBar && <span className="text-sm font-bold">{menu.name}</span>}
		</Link>
	);
};

const Sidebar = ({ openModalFunction = () => {} }) => {
	const dispatch = useDispatch();
	const navigate = useNavigate();
	const { user } = useSelector((state) => state.user);
	const [openSideBar, setOpenSideBar] = useState(true);
	const [openMenus, setOpenMenus] = useState({});
	const [isAvatarOpen, setIsAvatarOpen] = useState(false);
	const [showMobileSidebar, setShowMobileSidebar] = useState(false);

	const toggleSidebar = () => {
		setOpenSideBar(!openSideBar);
		if (!openSideBar) {
			setOpenMenus({}); // Close all submenus when collapsing sidebar
		}
	};

	const toggleMobileSidebar = () => {
		setShowMobileSidebar(!showMobileSidebar);
	};

	return (
		<div className="sticky top-0 h-screen scrollbar">
			{/* Hamburger icon for mobile */}
			<div className="md:hidden flex w-full justify-between p-4">
				<button onClick={toggleMobileSidebar}>
					<BiMenu className="text-3xl" />
				</button>
				<img src="/images/logo.jpg" alt="logo" className="h-12" />
			</div>

			{/* Sidebar container */}
			<div
				className={`fixed md:relative z-20 flex flex-col h-screen overflow-hidden top-0 text-white bg-black transition-all duration-300
        ${openSideBar ? "w-72" : "w-16"} ${
					showMobileSidebar ? "left-0" : "-left-full"
				} md:left-0 scrollbar`}
			>
				<div className="flex items-center justify-between p-4">
					{openSideBar ? <img src="/images/logo.jpg" alt="logo" className="h-12" /> : null}
					<button
						onClick={toggleSidebar}
						className="p-1.5 rounded-lg bg-gray-700 hover:bg-gray-700"
					>
						{openSideBar ? (
							<BiChevronLeft className="w-4 h-4" />
						) : (
							<BiChevronRight className="w-4 h-4" />
						)}
					</button>
				</div>

				<div className="flex-1 overflow-y-auto scrollbar">
					<div className="flex flex-col w-full gap-y-1 px-2 border-gray-700 ">
						{navMenus.map((menu, index) => (
							<MenuItem
								key={index}
								menu={menu}
								isOpen={openMenus[menu.name]}
								setOpenMenus={setOpenMenus}
								openSideBar={openSideBar}
							/>
						))}
					</div>
				</div>

				{/* Section for avatar and show the logout menu on click */}
				<div className="relative flex w-full content-between justify-between items-center pl-3 h-14 bg-gray-700 hover:bg-gray-700 transition-colors">
					<div
						className="flex items-center gap-x-3 outline-none border-none focus:outline-none active:outline-none"
						onClick={() => setIsAvatarOpen(!isAvatarOpen)}
					>
						<img
							type="button"
							className="w-8 h-8 rounded-full cursor-pointer"
							src="/images/avatar.jpg"
							alt="User dropdown"
						/>
						{openSideBar ? (
							<div className="capitalize">
								<div>Hi, {user.firstname}</div>
								{/* <div className="text-xs">({user.clientName})</div> */}
							</div>
						) : null}
					</div>

					<IoMdLogOut
						onClick={() => openModalFunction()}
						className="text-2xl hover:text-gray-300 ml-2 hover:cursor-pointer mr-2"
					/>

					{/* <div
            className={`z-10 ${
              isAvatarOpen ? "block" : "hidden"
            } transition-all bottom-14 absolute left-2 duration-200 bg-white hover:bg-gray-100 divide-y divide-gray-100 rounded-lg shadow w-44 `}
          >
            <div className="py-1">
              <button
                className="flex items-center gap-x-3  px-4 py-2 text-sm text-gray-700 w-full"
                onClick={() => {
                  dispatch(logout());
                  navigate("/signin");
                  setIsAvatarOpen(false);
                  toast.success("Logged out successfully!");
                }}
              >
                <IoMdLogOut className="text-2xl" /> <span>Logout</span>
              </button>
            </div>
          </div> */}
				</div>
				{/* Showing the copyrights section is the navbar is fully open */}
				{openSideBar && (
					<div className="text-[12px] text-gray-400 p-3">
						<p className="capitalize">Developed by RPSG Media</p>
						<div className="flex gap-x-1">
							<p> &copy; {new Date().getFullYear()} All Rights Reserved |</p>
							<p>Vesion 1.01.0000</p>
						</div>
					</div>
				)}
			</div>
			{/* Overlay for mobile sidebar */}
			{showMobileSidebar && (
				<div
					className="fixed inset-0 bg-black opacity-50 z-10 md:hidden"
					onClick={toggleMobileSidebar}
				></div>
			)}
		</div>
	);
};

export default Sidebar;
