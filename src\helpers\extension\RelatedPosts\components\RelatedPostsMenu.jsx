import { BubbleMenu as BaseBubbleMenu } from "@tiptap/react";
import React, { useState, useEffect, useCallback, useRef } from "react";
import { getRenderContainer } from "../../../utils/getRenderContainer";
import SizeSmall from "../../../../../public/svg/SizeSmall";
import SizeMedium from "../../../../../public/svg/SizeMedium";
import SizeFullWidth from "../../../../../public/svg/SizeFullWidth";
import AlignLeft from "../../../../../public/svg/AlignLeft";
import AlignCenter from "../../../../../public/svg/AlignCenter";
import AlignRight from "../../../../../public/svg/AlignRight";
import { FiSettings, FiX } from "react-icons/fi";
import { TfiLayoutGrid2 } from "react-icons/tfi";
import { GoRows } from "react-icons/go";
import { DropdownMenu } from "../../../../parts/FormComponents";
import { useDispatch, useSelector } from "react-redux";
import { MdKeyboardArrowDown } from "react-icons/md";
import {
  closeAllDropdowns,
  toggleDropdown,
} from "../../../../store/slices/editorSlice";
import { imageAlignIcons } from "../../Image/components/ImageBlockMenu";
import { BiTrash } from "react-icons/bi";
import Button from "../../../../parts/Button";

export const imageSizeIcons = {
  50: <SizeSmall />,
  75: <SizeMedium />,
  100: <SizeFullWidth />,
};

export const imageLayoutIcons = {
  grid: <TfiLayoutGrid2 className="w-[16px] h-[16px]" />,
  row: <GoRows className="w-[16px] h-[16px]" />,
};

export const RelatedPostsMenu = ({ editor, appendTo }) => {
  const menuRef = useRef(null);
  const tippyInstance = useRef(null);
  const { dropdowns } = useSelector((state) => state.editor);
  const [open, setOpen] = useState(false);
  const [title, setTitle] = useState("");
  const [imageContent, setImageContent] = useState({
    size: 100,
    layout: "grid",
    imageAlign: "left",
  });
  const dispatch = useDispatch();

  useEffect(() => {
    if (editor) {
      const attributes = editor.getAttributes("relatedPosts");
      setTitle(attributes?.title ?? "");
    }
  }, [editor, open]);

  const getReferenceClientRect = useCallback(() => {
    const renderContainer = getRenderContainer(editor, "node-relatedPosts");
    const rect =
      renderContainer?.getBoundingClientRect() ||
      new DOMRect(-1000, -1000, 0, 0);

    return rect;
  }, [editor]);

  const shouldShow = useCallback(() => {
    const isActive = editor.isActive("relatedPosts");
    return isActive;
  }, [editor]);

  const onAlignChange = useCallback(
    (alignment) => {
      editor
        .chain()
        .focus(undefined, { scrollIntoView: false })
        .setRelatedPostsAlign(alignment)
        .run();
    },
    [editor]
  );

  const onWidthChange = useCallback(
    (value) => {
      editor
        .chain()
        .focus(undefined, { scrollIntoView: false })
        .setRelatedPostsWidth(value)
        .run();
    },
    [editor]
  );

  const onLayoutChange = useCallback(
    (value) => {
      editor
        .chain()
        .focus(undefined, { scrollIntoView: false })
        .setRelatedPostsLayout(value)
        .run();
    },
    [editor]
  );

  const createTrigger = useCallback(
    (content, dropdownKey) => (
      <button
        className="p-1 hover:bg-gray-100 rounded-sm flex items-center gap-1"
        onClick={() => dispatch(toggleDropdown(dropdownKey))}
      >
        {content}
        <MdKeyboardArrowDown size={16} />
      </button>
    ),
    [dispatch]
  );

  const updateRelatedPostsTitle = useCallback(() => {
    editor
      .chain()
      .focus(undefined, { scrollIntoView: false })
      .setRelatedPostsTitle(title)
      .run();
  }, [editor, title]);

  const onImagePropChange = ({ id, value }) => {
    setImageContent({ ...imageContent, [id]: value });
  };

  const deleteRelatedPosts = useCallback(() => {
    editor.chain().focus().deleteSelection().run();
  }, [editor]);

  return (
    <>
      <BaseBubbleMenu
        editor={editor}
        pluginKey="relatedPostsMenu"
        shouldShow={shouldShow}
        updateDelay={0}
        tippyOptions={{
          interactive: true,
          offset: [0, 8],
          maxWidth: "100%",
          popperOptions: {
            modifiers: [{ name: "flip", enabled: false }],
          },
          getReferenceClientRect,
          onCreate: (instance) => {
            tippyInstance.current = instance;
          },
          appendTo: () => {
            return appendTo?.current;
          },
        }}
      >
        <div className="bubble-menu" ref={menuRef}>
          <div className="bubble-menu-icon text-fadeGray py-1 text-xs ">
            <DropdownMenu
              isOpen={dropdowns.size}
              onSelect={(value) => {
                onWidthChange(parseInt(value));
                onImagePropChange({ id: "size", value });
                dispatch(closeAllDropdowns());
              }}
              trigger={createTrigger(
                <button className="px-1 rounded-sm flex items-center justify-center">
                  {imageSizeIcons[imageContent.size]}
                </button>,
                "size"
              )}
            >
              <button
                value={50}
                className="w-full hover:bg-blueShade px-2 py-2 whitespace-nowrap flex items-center gap-2"
              >
                <SizeSmall /> Small
              </button>
              <button
                value={75}
                className="w-full hover:bg-blueShade px-2 py-2 whitespace-nowrap flex items-center gap-2"
              >
                <SizeMedium /> Medium
              </button>
              <button
                value={100}
                className="w-full hover:bg-blueShade px-2 whitespace-nowrap py-2 flex items-center gap-2"
              >
                <SizeFullWidth /> Full Width
              </button>
            </DropdownMenu>
          </div>
          <span className="bubble-menu-divider"></span>
          <div className="bubble-menu-icon text-fadeGray py-1 text-xs ">
            {/* Dropdown Menu */}
            <DropdownMenu
              isOpen={dropdowns.imageAlignment}
              onSelect={(value) => {
                onAlignChange(value);
                onImagePropChange({ id: "imageAlign", value });
                dispatch(closeAllDropdowns());
              }}
              trigger={createTrigger(
                <button className="px-1 rounded-sm flex items-center justify-center">
                  {imageAlignIcons[imageContent.imageAlign]}
                </button>,
                "imageAlignment"
              )}
            >
              <button
                value="left"
                className="w-full hover:bg-blueShade px-2 py-2 whitespace-nowrap flex items-center gap-2"
              >
                <AlignLeft /> <span>Align left</span>
              </button>
              <button
                value="center"
                className="w-full hover:bg-blueShade px-2 py-2 whitespace-nowrap flex items-center gap-2"
              >
                <AlignCenter /> <span>Align center</span>
              </button>
              <button
                value="right"
                className="w-full hover:bg-blueShade px-2 py-2 whitespace-nowrap flex items-center gap-2"
              >
                <AlignRight /> <span>Align right</span>
              </button>
            </DropdownMenu>
          </div>
          <span className="bubble-menu-divider"></span>
          <div className="bubble-menu-icon text-fadeGray py-1 text-xs ">
            <DropdownMenu
              isOpen={dropdowns.layout}
              onSelect={(value) => {
                onLayoutChange(value);
                onImagePropChange({ id: "layout", value });
                dispatch(closeAllDropdowns());
              }}
              trigger={createTrigger(
                <button className="px-1 rounded-sm flex items-center justify-center">
                  {imageLayoutIcons[imageContent.layout]}
                </button>,
                "layout"
              )}
            >
              <button
                value="grid"
                className="w-full hover:bg-blueShade px-2 py-2 whitespace-nowrap flex items-center gap-2"
              >
                <TfiLayoutGrid2 className="w-[16px] h-[16px]" /> Grid
              </button>
              <button
                value="row"
                className="w-full hover:bg-blueShade px-2 py-2 whitespace-nowrap flex items-center gap-2"
              >
                <GoRows className="w-[16px] h-[16px]" /> Row
              </button>
            </DropdownMenu>
          </div>
          <span className="bubble-menu-divider"></span>
          <button
            className="bubble-menu-icon text-fadeGray"
            onClick={() => setOpen(true)}
          >
            <div className="p-1 hover:bg-gray-100 rounded-sm flex items-center gap-1">
              <FiSettings />
            </div>
          </button>
          <span className="bubble-menu-divider"></span>
          <button
            className="bubble-menu-icon text-fadeGray"
            onClick={() => deleteRelatedPosts()}
          >
            <div className="p-1 hover:bg-gray-100 rounded-sm flex items-center gap-1">
              <BiTrash />
            </div>
          </button>
        </div>
      </BaseBubbleMenu>
      <div
        className={`${
          open ? "block" : "hidden"
        } fixed top-0 right-0 z-20 h-screen w-full bg-slate-600 bg-opacity-40 rounded-r-md shadow-2xl transition-all duration-800 ease-in-out overflow-hidden`}
      >
        <div className="w-full md:w-96 bg-white absolute right-0 top-0 h-screen text-fadeGray">
          <div className="menu-drawer">
            <div className="headsec">
              <h2 className="heading">Related Posts Setting</h2>
              <FiX className="close" onClick={() => setOpen(false)} />
            </div>
            <div className="w-full p-5 h-[calc(100vh-129px)] overflow-y-scroll">
              <div className="flex flex-col gap-y-4">
                <div className="flex flex-col gap-y-2">
                  <div className="text-base">Title</div>
                  <input
                    id="title"
                    type="text"
                    className="border text-sm border-gray-300 rounded-md px-4 py-2 w-full focus:outline-none focus:border-blue-500"
                    value={title}
                    onChange={(e) => setTitle(e.target.value)}
                    placeholder="Eg. Related Stories"
                  />
                </div>
              </div>
            </div>
            <div className="footersec">
              <Button
                variant="secondary"
                rounded="full"
                className="btn-primary-outline"
                onClick={() => setOpen(false)}
              >
                Cancel
              </Button>
              <Button
                rounded="full"
                onClick={() => {
                  updateRelatedPostsTitle();
                  setOpen(false);
                }}
              >
                Save
              </Button>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default RelatedPostsMenu;
