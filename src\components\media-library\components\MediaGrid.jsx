import React from "react";
import { useSelectionManager } from "../hooks/useSelectionManager";

/**
 * Component for displaying media items in a grid layout
 */
const MediaGrid = ({ 
	media = [], 
	onMediaSelect,
	onMediaDoubleClick,
	showActions = true,
	itemSize = "w-24 h-24" 
}) => {
	const { isMediaSelected } = useSelectionManager();
	
	const handleMediaClick = (mediaItem) => {
		if (onMediaSelect) {
			onMediaSelect(mediaItem);
		}
	};
	
	const handleMediaDoubleClick = (mediaItem) => {
		if (onMediaDoubleClick) {
			onMediaDoubleClick(mediaItem);
		}
	};
	
	const getMediaIcon = (mediaType) => {
		if (mediaType?.startsWith('image/')) {
			return null; // Will show actual image
		}
		
		if (mediaType?.startsWith('video/')) {
			return (
				<svg className="w-8 h-8 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
					<path d="M2 6a2 2 0 012-2h6l2 2h6a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6zM5 8a1 1 0 000 2v3a1 1 0 001 1h3a1 1 0 001-1v-3a1 1 0 000-2H5z" />
				</svg>
			);
		}
		
		// Default file icon
		return (
			<svg className="w-8 h-8 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
				<path fillRule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clipRule="evenodd" />
			</svg>
		);
	};
	
	if (!media || media.length === 0) {
		return (
			<div className="text-center py-8 text-gray-500">
				<svg className="mx-auto w-12 h-12 mb-2 opacity-50" fill="currentColor" viewBox="0 0 20 20">
					<path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd" />
				</svg>
				<p>No media found</p>
			</div>
		);
	}
	
	return (
		<div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 2xl:grid-cols-8 gap-4">
			{media.map((mediaItem) => {
				const isSelected = isMediaSelected(mediaItem._id);
				const mediaIcon = getMediaIcon(mediaItem.type || mediaItem.mimeType);
				
				return (
					<div
						key={mediaItem._id}
						className={`group relative cursor-pointer transition-all duration-200 ${
							isSelected
								? "ring-2 ring-blue-500 ring-offset-2"
								: "hover:ring-1 hover:ring-gray-300 hover:ring-offset-1"
						}`}
						onClick={() => handleMediaClick(mediaItem)}
						onDoubleClick={() => handleMediaDoubleClick(mediaItem)}
					>
						{/* Media Container */}
						<div className={`${itemSize} rounded-lg overflow-hidden bg-gray-100 shadow-sm hover:shadow-md transition-shadow relative`}>
							{mediaIcon ? (
								// Show icon for non-image files
								<div className="w-full h-full flex items-center justify-center bg-gray-50">
									{mediaIcon}
								</div>
							) : (
								// Show actual image
								<img
									src={mediaItem.url || mediaItem.src}
									alt={mediaItem.alt || mediaItem.title || "Media item"}
									className={`w-full h-full object-cover transition-all duration-200 ${
										isSelected ? "opacity-90" : "group-hover:opacity-80"
									}`}
									loading="lazy"
									onError={(e) => {
										// Fallback to icon if image fails to load
										e.target.style.display = 'none';
										e.target.nextSibling.style.display = 'flex';
									}}
								/>
							)}
							
							{/* Fallback icon (hidden by default) */}
							<div className="w-full h-full absolute inset-0 items-center justify-center bg-gray-50 hidden">
								<svg className="w-8 h-8 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
									<path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd" />
								</svg>
							</div>
							
							{/* Media Type Badge */}
							{mediaItem.type && (
								<div className="absolute top-1 left-1 px-1.5 py-0.5 bg-black bg-opacity-60 text-white text-xs rounded">
									{mediaItem.type.split('/')[0].toUpperCase()}
								</div>
							)}
							
							{/* Selection Indicator */}
							{isSelected && (
								<div className="absolute -top-1 -right-1 w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center z-10">
									<svg 
										className="w-3 h-3 text-white" 
										fill="currentColor" 
										viewBox="0 0 20 20"
									>
										<path 
											fillRule="evenodd" 
											d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" 
											clipRule="evenodd" 
										/>
									</svg>
								</div>
							)}
							
							{/* Hover Overlay */}
							{showActions && (
								<div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-200 flex items-center justify-center opacity-0 group-hover:opacity-100">
									<div className="flex space-x-2">
										<button
											className="p-2 bg-white rounded-full shadow-md hover:shadow-lg transition-shadow"
											onClick={(e) => {
												e.stopPropagation();
												// Could open preview modal here
											}}
											title="Preview"
										>
											<svg className="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
												<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
												<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
											</svg>
										</button>
									</div>
								</div>
							)}
						</div>
						
						{/* Media Title */}
						<div className="mt-2 px-1">
							<p 
								className="text-xs text-gray-700 truncate font-medium"
								title={mediaItem.title || mediaItem.alt || "Untitled"}
							>
								{mediaItem.title || mediaItem.alt || "Untitled"}
							</p>
							
							{/* Media Dimensions/Size */}
							{(mediaItem.width && mediaItem.height) && (
								<p className="text-xs text-gray-500">
									{mediaItem.width} × {mediaItem.height}
								</p>
							)}
						</div>
					</div>
				);
			})}
		</div>
	);
};

export default MediaGrid;
