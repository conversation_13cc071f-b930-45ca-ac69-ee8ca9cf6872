import React, { useCallback, useMemo, useRef, useState } from "react";
import { Input } from "../../parts/FormComponents";
import { useDispatch, useSelector } from "react-redux";
import {
  setErrors,
  setLocalVideo,
  setYoutubeVideo,
  updateThumbnail,
} from "../../store/slices/videoStorySlice";
import { useDropzone } from "react-dropzone";
import { GoPlus } from "react-icons/go";
import { handleVideoThumbnail } from "../../utils/video-thumbnail";

const AddVideo = ({ active, isShorts }) => {
  const dispatch = useDispatch();
  const { src, coverImg, isYoutube, youtubeUrl, errors } = useSelector(
    (state) => state.videoStory
  );
  const videoRef = useRef(null);
  const [uploadError, setUploadError] = useState("");

  // Memoized video URL
  const videoUrl = useMemo(
    () =>
      src ? (typeof src === "string" ? src : URL.createObjectURL(src)) : null,
    [src]
  );

  const getYoutubeId = (url) => {
    // Handle both regular videos and shorts
    const regExp =
      /^.*((youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=|shorts\/)([^#&?]*)).*/;
    const match = url.match(regExp);
    return match && match[3].length === 11 ? match[3] : null;
  };

  // Handle YouTube URL change
  const handleYoutubeUrlChange = (input) => {
    const url = input.value;
    const videoId = getYoutubeId(url);
    if (videoId) {
      dispatch(
        setYoutubeVideo({
          youtubeUrl: url,
          thumbnailUrl: `https://img.youtube.com/vi/${videoId}/0.jpg`,
        })
      );
    } else {
      dispatch(setYoutubeVideo({ youtubeUrl: url, thumbnailUrl: coverImg }));
    }
    dispatch(setErrors({ ...errors, src: null }));
  };

  // Dropzone configuration for video upload
  const onDrop = useCallback(
    async (acceptedFiles) => {
      setUploadError("");
      const file = acceptedFiles[0];

      if (file) {
        // Validate file type
        if (!file.type.startsWith("video/")) {
          setUploadError("Please upload a valid video file");
          return;
        }
        const maxSize = 150 * 1024 * 1024;
        // Validate file size (e.g., max 150MB)
        if (file.size > maxSize) {
          setUploadError("File size should be less than 150MB");
          return;
        }

        try {
          const thumbnailFile = await handleVideoThumbnail(file);
          dispatch(
            setLocalVideo({
              videoFile: file,
              thumbnailFile,
            })
          );
        } catch (error) {
          setUploadError("Error processing video. Please try again.");
        }
      }
    },
    [dispatch]
  );

  const {
    getRootProps,
    getInputProps,
    isFocused,
    isDragAccept,
    isDragReject,
    isDragActive,
  } = useDropzone({
    onDrop,
    accept: {
      "video/*": [".mp4", ".webm", ".ogg", ".mov"],
    },
    maxFiles: 1,
    multiple: false,
  });

  const baseStyle = {
    flex: 1,
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    justifyContent: "center",
    padding: "20px",

    borderWidth: 2,
    borderRadius: 5,
    borderColor: "#3b82f6",
    backgroundColor: "#f4f7ff",
    color: "#bdbdbd",
    outline: "none",
    cursor: "pointer",
    transition: "border .24s ease-in-out",
  };

  const focusedStyle = {
    borderColor: "fc6e00",
  };

  const acceptStyle = {
    borderColor: "#00e676",
  };

  const rejectStyle = {
    borderColor: "#ff1744",
  };

  const style = useMemo(
    () => ({
      ...baseStyle,
      ...(isFocused ? focusedStyle : {}),
      ...(isDragAccept ? acceptStyle : {}),
      ...(isDragReject ? rejectStyle : {}),
    }),
    [isFocused, isDragAccept, isDragReject]
  );

  return (
    <div className="border border-[#c1e4fe] rounded-md bg-white">
      <div className="border-b w-full px-5 text-lg font-semibold  py-5">
        Add Video
      </div>
      <div className="px-5 py-4">
        {active === "youtube" ? (
          <>
            <Input
              label="Video Youtube Link"
              name="src"
              id="src"
              placeholder="Enter youtube link"
              type="text"
              value={src}
              required={true}
              onDebouncedChange={(value) => handleYoutubeUrlChange({ value })}
            />
            {errors.src && <p className="text-red-500 text-sm">{errors.src}</p>}
            {youtubeUrl && getYoutubeId(youtubeUrl) && (
              <div
                className={`${
                  isShorts ? "" : "aspect-[16/9]"
                } mt-5 flex justify-center`}
              >
                <iframe
                  width={isShorts ? "50%" : "100%"}
                  height={isShorts ? "500" : "315"}
                  src={`https://www.youtube.com/embed/${getYoutubeId(
                    youtubeUrl
                  )}`}
                  allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                  allowFullScreen
                  title="YouTube Video"
                />
              </div>
            )}
          </>
        ) : (
          <div className="space-y-4">
            <div
              {...getRootProps({ style })}
              className={`border border-primary border-dashed  w-full h-20 flex items-center justify-center
                  ${
                    isDragActive
                      ? "border-blue-500 bg-blue-50"
                      : "border-gray-300"
                  }
                  ${uploadError ? "border-red-500" : ""}
                  hover:border-blue-500 hover:bg-blue-50 transition-colors duration-150`}
            >
              <input {...getInputProps()} />
              <div className=" text-sm text-gray-600">
                {isDragActive ? (
                  <p>Drop the files here ...</p>
                ) : (
                  <div className="flex flex-col items-center justify-center gap-y-2">
                    <GoPlus className="text-3xl" />
                    <p className="mt-1 text-xs text-gray-500">
                      Supported formats: MP4, WebM, OGG, MOV (Max size: 150MB)
                    </p>
                  </div>
                )}
              </div>
            </div>
            {uploadError && (
              <p className="text-red-500 text-sm text-center">{uploadError}</p>
            )}
            {!isYoutube && src && videoUrl && (
              <div className="mt-4">
                <video
                  ref={videoRef}
                  src={videoUrl} // Memoized video URL
                  controls
                  className={`${
                    isShorts
                      ? "w-[50%] h-[500px] mx-auto object-cover"
                      : "w-full"
                  } rounded`}
                />
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default AddVideo;
