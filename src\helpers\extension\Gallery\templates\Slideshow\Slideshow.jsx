import React, { useEffect, useState, useRef } from "react";

const Slideshow = ({ data = [], interval = 4000, autoplay = false }) => {
  const [current, setCurrent] = useState(0);
  const timeoutRef = useRef(null);

  useEffect(() => {
    if (!autoplay || data.length <= 1) return;
    resetTimeout();
    timeoutRef.current = setTimeout(
      () => setCurrent((prev) => (prev + 1) % data.length),
      interval
    );
    return () => resetTimeout();
  }, [current, data.length, interval, autoplay]);

  const resetTimeout = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
  };

  const goToNext = () => {
    resetTimeout();
    setCurrent((prev) => (prev + 1) % data.length);
  };

  const goToPrev = () => {
    resetTimeout();
    setCurrent((prev) => (prev - 1 + data.length) % data.length);
  };

  const navButtonStyle = {
    position: "absolute",
    top: "50%",
    transform: "translateY(-50%)",
    backgroundColor: "rgba(0,0,0,0.5)",
    border: "none",
    color: "white",
    padding: "8px",
    cursor: "pointer",
    borderRadius: "50%",
    zIndex: 10,
    transition: "all 0.3s ease-in-out",
  };

  return (
    <div className="w-full m-auto relative overflow-hidden">
      <div className="relative w-full aspect-video overflow-hidden">
        {data.map((item, index) => (
          <div
            className="absolute w-full h-full transition-opacity duration-[1.5s] ease-in-out"
            key={index}
            style={{
              opacity: index === current ? 1 : 0,
              zIndex: index === current ? 1 : 0,
            }}
          >
            <img
              className="block w-full h-full object-cover"
              src={item.src}
              alt={item.alt || `Slide ${index}`}
            />
          </div>
        ))}

        {/* Navigation buttons */}
        <button
          style={{
            ...navButtonStyle,
            left: 10,
            opacity: current === 0 ? 0 : 1,
          }}
          disabled={current === 0}
          onClick={goToPrev}
        >
          {/* Left Arrow SVG */}
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            viewBox="0 0 24 24"
            width="16"
            height="16"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M15 19l-7-7 7-7"
            />
          </svg>
        </button>
        <button
          style={{
            ...navButtonStyle,
            right: 10,
            opacity: current === data.length ? 0 : 1,
          }}
          onClick={goToNext}
          disabled={current === data.length - 1}
        >
          {/* Right Arrow SVG */}
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            viewBox="0 0 24 24"
            width="16"
            height="16"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M9 5l7 7-7 7"
            />
          </svg>
        </button>
      </div>
    </div>
  );
};

export default Slideshow;
