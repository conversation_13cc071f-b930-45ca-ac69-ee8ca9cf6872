const Collage = ({
  width = 20,
  height = 20,
  fontSize,
  strokeWidth = 1,
  fill = "currentColor",
  stroke,
  ...rest
}) => {
  const style = fontSize ? { fontSize } : undefined;
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 50 50"
      fill={fill}
      stroke={stroke}
      strokeWidth={strokeWidth}
      style={style}
      xmlns="http://www.w3.org/2000/svg"
      {...rest}
    >
      <path
        fill={fill}
        d="M3 2h20a1 1 0 0 1 1 1v20a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V3a1 1 0 0 1 1-1zm0 1v20h20V3H3zm24-1h20a1 1 0 0 1 1 1v10a1 1 0 0 1-1 1H27a1 1 0 0 1-1-1V3a1 1 0 0 1 1-1zm0 1v10h20V3H27zm0 13h8a1 1 0 0 1 1 1v6a1 1 0 0 1-1 1h-8a1 1 0 0 1-1-1v-6a1 1 0 0 1 1-1zm0 1v6h8v-6h-8zm12-1h8a1 1 0 0 1 1 1v6a1 1 0 0 1-1 1h-8a1 1 0 0 1-1-1v-6a1 1 0 0 1 1-1zm0 1v6h8v-6h-8zM3 38h26a1 1 0 0 1 1 1v8a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1v-8a1 1 0 0 1 1-1zm0 1v8h26v-8H3zm0-13h26a1 1 0 0 1 1 1v8a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1v-8a1 1 0 0 1 1-1zm0 1v8h26v-8H3zm30-1h14a1 1 0 0 1 1 1v20a1 1 0 0 1-1 1H33a1 1 0 0 1-1-1V27a1 1 0 0 1 1-1zm0 1v20h14V27H33z"
      />
    </svg>
  );
};

export default Collage;
