import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import { baseQuery } from "./baseConfig";
import { apiEndpoints } from "../../utils/constants";

// api endpoints related to the categories
export const flaggedStoriesApi = createApi({
  reducerPath: "flaggedStoriesApi",
  baseQuery: baseQuery,
  endpoints: (builder) => ({
    getCategories: builder.query({
      query: ({ search, limit, offset, status }) => ({
        url: apiEndpoints.getCategoriesList,
        params: {
          search,
          limit,
          offset,
        },
      }),
      transformResponse: (response) => ({
        count: response.count,
        data: response.data,
      }),
      // Only have one cache entry because the arg always maps to one string
      serializeQueryArgs: ({ endpointName }) => {
        return endpointName;
      },
      // Always merge incoming data to the cache entry
      merge: (currentCache, newItems) => {
        currentCache.data.push(...newItems.data);
      },
      // Refetch when the page arg changes
      forceRefetch({ currentArg, previousArg }) {
        return currentArg !== previousArg;
      },
    }),
    getAllSingleCategory: builder.query({
      query: ({ search, limit, offset, categories, status }) => ({
        url: apiEndpoints.getSingleCategoriAllList,
        params: {
          filter: { category: categories, search },
          limit,
          offset,
          status,
        },
      }),
      // transformResponse: (response) => ({
      //   count: response.count,
      //   data: response.data,
      // }),
    }),

    postSubCategories: builder.mutation({
      query: ({ data, url }) => ({
        url: url,
        body: data,
        method: "POST",
      }),
    }),

    getSubCategoriesList: builder.query({
      query: ({ search, limit, offset, categories, status }) => ({
        url: apiEndpoints.getSubCategoryList,
        params: {
          category: categories,
          limit,
          search,
          offset,
          status,
        },
      }),
      serializeQueryArgs: ({ endpointName }) => {
        return endpointName;
      },
      // Always merge incoming data to the cache entry
      merge: (currentCache, newItems) => {
        currentCache.results.push(...newItems.results);
      },
      // Refetch when the page arg changes
      forceRefetch({ currentArg, previousArg }) {
        return currentArg !== previousArg;
      },
      // transformResponse: (response) => ({
      //   count: response.count,
      //   data: response.data,
      // }),
    }),

    deleteFlaggedSubStory: builder.mutation({
      query: ({ id, data }) => ({
        url: `${apiEndpoints.patchFlaggedStory}/${id}`,
        method: "PUT",
        body: data,
      }),
    }),

    updateStatus: builder.mutation({
      query: ({ id, status }) => ({
        url: `${apiEndpoints.updateStoryStatus}/${id}`,
        method: "PUT",
        body: { status },
      }),
    }),

    bulkUpdateFlaggedStory: builder.mutation({
      query: (data) => ({
        url: apiEndpoints.patchBulkUpdateFlaggedStory,
        method: "PATCH",
        body: data,
      }),
    }),
  }),
});

// Export hooks for usage in functional components, which are
// auto-generated based on the defined endpoints
export const {
  useGetCategoriesQuery,
  useGetAllSingleCategoryQuery,
  useGetSubCategoriesListQuery,
  usePostSubCategoriesMutation,
  useDeleteFlaggedSubStoryMutation,
  useBulkUpdateFlaggedStoryMutation,
  useUpdateStatusMutation,
} = flaggedStoriesApi;
