import React, { useEffect, useState } from "react";
import { BiChevronRight } from "react-icons/bi";
import { Link, useNavigate, useParams, useSearchParams } from "react-router-dom";
import Container from "../parts/Container";
import Button from "../parts/Button";
import AddYoutube from "../components/videos/AddVideo";
import AddVideo from "../components/videos/AddVideo";
import VideoSE<PERSON> from "../components/videos/VideoSEO";
import Flags from "../components/stories/Flags";
import Categories from "../components/stories/Categories";
import VideoInfo from "../components/videos/VideoInfo";
import Thumbnail from "../components/videos/Thumbnail";
import {
	useCreateVideoStoryMutation,
	useEditVideoStoryMutation,
	useGetSingleVideoStoryQuery,
} from "../store/apis/videoStoryApi";
import { useDispatch, useSelector } from "react-redux";
import {
	clearErrors,
	resetLocalVideoData,
	resetState,
	resetYoutubeVideoData,
	setErrors,
	setLocalVideo,
	setVideoStoryData,
	setYoutubeStatus,
	setYoutubeVideo,
} from "../store/slices/videoStorySlice";
import { toast } from "react-toastify";
import Loader from "../parts/Loader";
import ImageCropper from "../components/stories/ImageCropper";
import { areAllKeywordsPresent } from "../components/stories/TextEditorToolbar";

// need to check for the post and put state
// figure how to change and clear the caching upon post and put request
const tabs = [
	{ label: "Add from Youtube", value: "youtube" },
	{ label: "Add from this device", value: "device" },
];

const AddEditVideoStory = ({ method, isShorts = false }) => {
	const dispatch = useDispatch();
	const navigate = useNavigate();
	// const [transformData, setTransformData] = useState(null);

	// const handleCropComplete = (data) => {
	//   setTransformData(data);
	//   console.log("Transform data for API:", data);
	// };
	const { id } = useParams();
	const [searchParams] = useSearchParams();

	const {
		storiesState: {
			title,
			excerpt,
			subcategory,
			contributors,
			isHighlighted,
			section,
			writer,
			highlightedCategory,
			meta,
			ogImg,
			twitterImg,
			twitter,
			og,
		},
		storiesState,
		youtubeUrl,
		errors,
		status,
		isYoutube,
		coverImg,
		src,
	} = useSelector((state) => state.videoStory);
	const [active, setActive] = useState("youtube");
	const { data, isLoading, isError, error } = useGetSingleVideoStoryQuery(id, {
		skip: !id,
	});

	const [createVideoStory, { isLoading: createLoading, errro: createError }] =
		useCreateVideoStoryMutation();
	const [editVideoStory, { isLoading: editLoading, errro: editError }] =
		useEditVideoStoryMutation();

	// displaying the error message here
	// displaying the error message here
	if (isError) {
		if (error.status === 401) {
			toast.error("Session Expired", { toastId: "video_edit_session_expired" });
			navigate("/signin");
		} else {
			if (error.status === 404) {
				navigate(-1);
			}
			toast.error(error.data.message, { toastId: "get_single_video_error" });
		}
	}

	// Click handler to set the active state and make API call
	const handleTabClick = (tab) => {
		setActive(tab);

		if (tab === "youtube") {
			dispatch(setYoutubeStatus(true));
		} else if (tab === "device") {
			dispatch(setYoutubeStatus(false));
		}
		if (tab === "youtube" && method === "POST") {
			dispatch(resetLocalVideoData());
		} else if (tab === "device" && method === "POST") {
			dispatch(resetYoutubeVideoData());
		}
	};

	useEffect(() => {
		setActive(isYoutube ? "youtube" : "device");
	}, [isYoutube]);
	// clear the edit data when component unmounts
	useEffect(() => {
		if (data) {
			dispatch(setVideoStoryData(data));
		}

		return () => {
			if (data) {
				dispatch(resetState());
			}
		};
	}, [data]);

	const validateStory = () => {
		const newErrors = {};
		if (!src) newErrors.src = "src is required";
		if (!title) newErrors.title = "Title is required";

		return newErrors;
	};

	const handleSave = () => {
		const validationErrors = validateStory();
		if (!areAllKeywordsPresent({ title, meta: storiesState.meta })) {
			// Optional: stop publishing process
			return;
		}
		if (Object.keys(validationErrors).length > 0) {
			dispatch(setErrors(validationErrors));
		} else {
			const formData = new FormData();
			dispatch(clearErrors());
			const payload = {
				isYoutube,
				contributor: storiesState.contributors,
				status,
				subcategory: storiesState.subcategory,
				section: storiesState.section,
				writer: storiesState.writer,
				highlightedCategory: storiesState.highlightedCategory,
				title: storiesState.title,
				publishDate: storiesState.publishDate,
				excerpt: storiesState.excerpt,
				slug: `/${storiesState.meta.slug}`,
				meta: {
					...storiesState.meta,
					og: {
						title: storiesState.og.title,
						description: storiesState.og.description,
					},
					twitter: {
						title: storiesState.twitter.title,
						description: storiesState.twitter.description,
						card: storiesState.twitter.card === "large" ? "summary_large_image" : "summary",
					},
				},
			};
			// Helper function to check if a value is a string URL
			const isStringUrl = (value) => typeof value === "string";

			// Handle cover image
			if (isStringUrl(coverImg)) {
				payload.coverImg = coverImg; // Add URL to the payload
			} else if (coverImg) {
				formData.append("coverImg", coverImg); // Add file to FormData
			}

			if (isStringUrl(src)) {
				payload.src = src; // Add URL to the payload
			} else if (src) {
				formData.append("src", src); // Add file to FormData
			}

			// Handle Open Graph (og) image
			if (isStringUrl(ogImg)) {
				payload.meta.og.image = ogImg; // Add URL to the payload
			} else if (ogImg) {
				formData.append("ogImg", ogImg[0]); // Add file to FormData
			}

			// Handle Twitter image
			if (isStringUrl(twitterImg)) {
				payload.meta.twitter.image = twitterImg; // Add URL to the payload
			} else if (twitterImg) {
				formData.append("twitterImg", twitterImg[0]); // Add file to FormData
			}

			if (isShorts) {
				payload.isShorts = true;
				payload.type = 2;
			}
			formData.append("data", JSON.stringify(payload));
			console.log(payload, " payload");

			if (method === "POST") {
				createVideoStory(formData)
					.then((res) => {
						if (res.data.status === "success") {
							toast.success("Video Story Created Successfully", {
								toastId: "video_story_creation_successfull",
							});
							if (parseInt(status, 10) === 1 || parseInt(status, 10) === 4) {
								if (isShorts) {
									navigate("/admin/shorts?status=all");
								} else {
									navigate("/admin/video-stories?status=all");
								}
								window.close();
							}
						} else {
							toast.error(res.data.message, {
								toastId: "video_story_creation_fail",
							});
						}
					})
					.catch((err) => {
						console.log("error", err);
						toast.error(err.message, {
							toastId: "video_story_creation_fail1",
						});
					});
			} else {
				editVideoStory({ formData, id })
					.then((res) => {
						if (res.data.status === "success") {
							toast.success("Video Story Updated Successfully", {
								toastId: "video_story_updation_successfull",
							});

							if (parseInt(status, 10) === 1 || parseInt(status, 10) === 4) {
								if (isShorts) {
									navigate("/admin/shorts?status=all");
								} else {
									navigate("/admin/video-stories?status=all");
								}
								window.close();
							}
						} else {
							toast.error(res.message, {
								toastId: "video_story_updation_fail",
							});
						}
					})
					.catch((err) => {
						toast.error(err.message, {
							toastId: "video_story_updation_fail1",
						});
						console.log(err);
					});
			}
		}
	};

	if (isLoading) {
		return <Loader />;
	}
	const handleCancel = () => {
		if (isShorts) {
			navigate("/admin/shorts");
		} else {
			navigate("/admin/video-stories");
		}
	};
	return (
		<Container>
			<div className="flex w-full items-center justify-between mb-0">
				<div>
					<div className="flex items-center text-xl lg:text-2xl font-semibold">
						<Link
							to={isShorts ? "/admin/shorts?status=all" : "/admin/video-stories?status=all"}
							className=" py-1 bg-transparent text-[#b4b4b4] font-normal transition-all duration-150 rounded-full pr-3 hover:text-black"
						>
							{isShorts ? "Shorts Stories" : "Video Stories"}
						</Link>
						<BiChevronRight className="text-3xl font-normal text-gray-400" />
						<p className="text-fadeGray pl-2">{title || "Untitled Story"}</p>
					</div>
					<p className="pl-3 mt-1 text-sm">
						Manage what info is shown on your {isShorts ? "Shorts" : "Video"} stories.
					</p>
				</div>
				<div className="flex items-center gap-x-3">
					<Button
						rounded="full"
						variant="secondary"
						size="sm"
						customClasses="py-[7px]"
						onClick={handleCancel}
					>
						Cancel
					</Button>
					<Button size="sm" customClasses="py-[7px]" rounded="full" onClick={handleSave}>
						{createLoading || editLoading ? "Please Wait..." : "Save"}
					</Button>
				</div>
			</div>
			{/* <div>
        <ImageCropper
          image="https://cdn.pixabay.com/photo/2023/08/02/18/21/yoga-8165759_1280.jpg"
          onCropComplete={handleCropComplete}
        />
      </div> */}
			<div className="flex items-center flex-col justify-center">
				<div className="flex items-center mt-10">
					{tabs.map((tab) => (
						<div key={tab.value}>
							<button
								onClick={() => handleTabClick(tab.value)}
								className={`inline-block w-full px-7 h-8 border ${
									tab.value === "youtube" ? "rounded-l-full" : "rounded-r-full"
								}  transition-all duration-300 outline-none active:outline-none ${
									active === tab.value ? "text-white bg-primary border-primary" : "bg-white"
								}`}
							>
								<h3>{tab.label}</h3>
							</button>
						</div>
					))}
				</div>
			</div>
			<div className="mt-4 px-2 flex gap-x-5">
				<div className="w-[60%] flex flex-col gap-y-5">
					<AddVideo active={active} isShorts={isShorts} />
					<VideoInfo />
					<div className=" bg-white border border-[#c1e4fe] rounded-md">
						<div className=" border-b w-full  text-lg font-semibold px-5 py-5">Categories</div>
						<div className="py-5 px-5">
							<Categories type="videoStory" isShorts={isShorts} />
						</div>
					</div>
					{/* <div className="">
            <div className="py-5 border-b w-full px-5 text-lg font-semibold ">
              Video Info
            </div>
          </div> */}
				</div>
				<div className="flex flex-col gap-y-5 w-[40%]">
					<Thumbnail />
					<VideoSEO />
					<div className="bg-white rounded-md border border-[#c1e4fe]">
						<div className="border-b w-full px-5 text-lg font-semibold  py-5">Flags</div>
						<div className="px-4 py-5">
							<Flags type="videoStory" isShorts={isShorts} />
						</div>
					</div>
				</div>
			</div>
		</Container>
	);
};

export default AddEditVideoStory;
