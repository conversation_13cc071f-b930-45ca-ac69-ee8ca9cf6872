import React, { useEffect, useMemo } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useGetCategoriesQuery } from "../store/apis/categoriesApi";
import { IoLockClosedOutline } from "react-icons/io5";
import { Link, useNavigate } from "react-router-dom";
import Table from "../parts/Table";
import Container from "../parts/Container";
import { BiChevronRight } from "react-icons/bi";
import { toast } from "react-toastify";
import BreadCrumb from "../parts/BreadCrumb";
import {
  incrementOffset,
  resetFilters,
  resetOffset,
} from "../store/slices/categoriesSlice";
import Loader from "../parts/Loader";
import { CiImageOn } from "react-icons/ci";

const frontendUrl = import.meta.env.VITE_CLIENT_URL;
const Categories = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const {
    filter: { status, search },
    limit,
    offset,
  } = useSelector((state) => state.categories);


  useEffect(() => {
    dispatch(resetFilters());
    dispatch(resetOffset());
  }, []);

  const { data, isLoading, isError, error, isFetching } = useGetCategoriesQuery(
    {
      search: search !== "" ? search : "",
      status,
      offset: 0, // need to check here. tis a bug for now and leave it as it is
      limit,
    }
  );

  // displaying the error message here
  if (isError) {
    toast.error(error.data.message, { toastId: "get_all_catgories_error" });
    if (error.status === 401) {
      toast.error("Session Expired", { toastId: "author_fetch_error" });
      navigate("/signin");
    }
  }

  const columns = useMemo(
    () => [
      {
        accessorKey: "_id",
        size: 50,
        enableSorting: false,
        header: () => <IoLockClosedOutline className="texgt-primary text-xl" />,
        cell: () => <IoLockClosedOutline className="text-primary text-xl" />,
      },
      {
        accessorKey: "name",
        id: "name",
        size: 200,
        header: () => "Category Details",
        cell: ({ row }) => {
          return (
            <div className="flex items-center gap-x-2 font-semibold ">
              {row.original.imgsrc ? (
                <img
                  src={row.original.imgsrc}
                  alt=""
                  className="w-[80px] h-[60px] object-cover rounded"
                />
              ) : (
                <div className="w-[80px] h-[60px] object-cover rounded bg-[#daeffe] flex items-center justify-center">
                  <div className="w-[40px] h-[40px] rounded-full bg-white flex items-center justify-center">
                    <CiImageOn className="text-[#3b82f6] text-2xl m-auto" />
                  </div>
                </div>
              )}
              <div>
                <div className="line-clamp-2">{row.original.name}</div>
                <div className="text-[#969696] text-xs font-normal flex items-center space-x-2">
                  URL slug: {row.original.slug}
                </div>
              </div>
            </div>
          );
        },
      },
      // {
      //   accessorKey: "submenus",
      //   header: () => "Sub Categories",
      // },
      {
        accessorKey: "submenus",
        header: () => "Sub-Categories",
        cell: ({ row }) => (
          <div className=" flex flex-col gap-y-1">
            {row.original.submenus.map((menu) => {
              return (
                <button
                  onClick={() => {
                    const subcategoryIds =
                      menu.name === "All"
                        ? row.original.submenus.map((item) => item._id)
                        : [menu._id];

                    navigate(
                      `/admin/subcategory/${menu.name
                        .toLowerCase()
                        .replace(" ", "-")}?ids=${encodeURIComponent(
                        JSON.stringify(subcategoryIds)
                      )}&name=${encodeURIComponent(menu.name)}&level=${
                        menu.type
                      }?status=all`
                    );
                  }}
                  key={menu._id}
                  // to={`/admin/subcategory/${menu._id}`}
                  className="hover:text-fadeGray flex items-center gap-x-2 group"
                >
                  <span> {menu.name}</span>

                  <BiChevronRight className="opacity-0 group-hover:opacity-100 group-hover:text-fadeGray text-xl transition-all duration-150" />
                </button>
              );
            })}
          </div>
        ),
      },
      {
        accessorKey: "stories",
        header: "Stories",
        cell: ({ row }) => (
          <div className="">
            {row.original.submenus.map((menu) => {
              return <div key={menu._id}>{menu.postCount} Stories</div>;
            })}
          </div>
        ),
      },
      // {
      //   accessorKey: "timestamp",
      //   header: "Action",
      //   cell: ({ row }) => {
      //     return (
      //       <div className="flex items-center gap-x-2">
      //         <RoundedIconsButton
      //         // onClick={() => handleViewClick(row.original.viewLink)}
      //         >
      //           <BsEye className="h-[15px] w-[15px]" />
      //         </RoundedIconsButton>

      //         <RoundedIconsButton>
      //           <FiEdit
      //             className="h-[15px] w-[15px]"
      //             // onClick={() =>
      //             //   navigate(`/admin/edit-video-story/${row.original._id}`)
      //             // }
      //           />
      //         </RoundedIconsButton>
      //         <RoundedIconsButton>
      //           <FaRegTrashAlt className="h-[15px] w-[15px]" />
      //         </RoundedIconsButton>
      //       </div>
      //     );
      //   },
      // },
    ],
    []
  );

  const fetchMoreData = () => {
    if (
      data?.data?.length > 0 && // Check if `data.data` has items
      !isFetching && // Ensure no ongoing fetch
      data?.data?.length < data?.count // Check if there are more items to fetch
    ) {
      dispatch(incrementOffset());
    }
  };

  return (
    <Container>
      <div className="lg:flex items-center justify-between mb-5">
        <div>
          {/* <div className="flex items-center text-sm">
            <Link
              onClick={() =>
                dispatch(videoFilterStoryStatus(searchParams.get("status")))
              }
              to={"/admin/categories"}
              className="hover:bg-white text-blackShade rounded-full px-3 py-1 bg-transparent "
            >
              Categories
            </Link>
           
          </div> */}
          <BreadCrumb
            title={"Categories"}
            description={
              "Group posts by topic to help readers and search engines find your content."
            }
          />
        </div>
      </div>
      <Table
        module="categories"
        data={data ? data.data : []}
        isLoading={isLoading}
        columns={columns}
        isFetching={isFetching}
        fetchMoreData={fetchMoreData}
        customClass={"categories"}
        enableRowSelection={false}
        enableMultiRowSelection={false}
      />
    </Container>
  );
};

export default Categories;
