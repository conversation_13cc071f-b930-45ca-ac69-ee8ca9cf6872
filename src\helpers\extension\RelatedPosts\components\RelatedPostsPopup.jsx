import React, { useEffect, useState, useCallback, useRef } from "react";
import OutsideClickHandler from "react-outside-click-handler";
import { DebouncedInput } from "../../../../parts/FormComponents";
import { useDispatch } from "react-redux";
import {
  setShowRelatedPosts,
  filterStoryStatus,
  incrementOffset,
  resetFilters,
  setFetchedData,
  storiesInputFilter,
  setFilterPostIds,
  resetState,
} from "../../../../store/slices/storiesSlice";
import styles from "./RelatedPosts.module.css";
import { FiX } from "react-icons/fi";
import { formatDateAndTime } from "../../../../utils/helperFunctions";
import { CiImageOn } from "react-icons/ci";
import useInfiniteScrollRelatedPosts from "../../../../utils/useInfiniteScrollRelatedPosts";
import { useIntersectionObserver } from "../../../../parts/Table";

const RelatedPostsPopup = ({ editor, getPos, hasMore = true }) => {
  const [postIds, setPostIds] = useState([]);
  const dispatch = useDispatch();

  // config for the stories api
  const storiesApiConfig = {
    setDataAction: setFetchedData,
    resetDataAction: resetFilters,
    sliceName: "stories",
  };

  const {
    data,
    isLoading,
    offset,
    filter,
    isError,
    error,
    fetchData,
    isFetching,
  } = useInfiniteScrollRelatedPosts({
    config: storiesApiConfig,
    url: "/api/article/list",
    postIds: null,
  });

  // Load data when filters change
  useEffect(() => {
    fetchData();
  }, [offset, filter]);

  const lastFetchTime = useRef(Date.now());

  // Throttled fetch function
  const handleFetchMore = () => {
    const now = Date.now();
    const timeSinceLastFetch = now - lastFetchTime.current;
    const THROTTLE_DELAY = 1000; // 1 second throttle

    if (timeSinceLastFetch >= THROTTLE_DELAY && !isFetching && hasMore) {
      lastFetchTime.current = now;
      dispatch(incrementOffset());
    }
  };

  // Setup intersection observer for the loading trigger
  const loadingTriggerRef = useIntersectionObserver(handleFetchMore, {
    threshold: 0,
    rootMargin: "200px",
  });

  const handlePostIds = useCallback(
    (event) => {
      const { checked, value } = event.target;

      setPostIds((prevPostIds) =>
        checked
          ? [...prevPostIds, value]
          : prevPostIds.filter((id) => id !== value)
      );
    },
    [setPostIds]
  );

  const handleClose = () => {
    dispatch(resetFilters());
    setPostIds([]);
    dispatch(setShowRelatedPosts(false));
  };

  const handleSave = useCallback(() => {
    if (postIds.length > 0) {
      editor.chain().setRelatedPosts({ postIds: postIds }).focus().run();
      handleClose();
    }
  }, [postIds, editor, handleClose]);

  return (
    <div className={styles.modal}>
      <OutsideClickHandler onOutsideClick={handleClose}>
        <div className={styles.card}>
          <div className={styles.header}>
            <h4 className={styles.title}>Choose Posts</h4>
            <FiX className={styles.closeBtn} onClick={handleClose} />
          </div>
          <div className={styles.cardBody}>
            <div className={styles.filterWrapper}>
              <div className={styles.searchBar}>
                <DebouncedInput
                  type="text"
                  value={filter?.search ?? ""}
                  onChange={(value) => dispatch(storiesInputFilter(value))}
                  placeholder="Search..."
                  className="border shadow rounded w-full md:w-auto h-8"
                  inputClass="!h-[2.4rem]"
                />
              </div>
            </div>
            <div className={styles.tableBlock}>
              {data.map((item, index) => {
                if (data.length === index + 1) {
                  // Attach the ref to the last item
                  return (
                    <label
                      htmlFor={`stories-item-${index}`}
                      className="flex items-center rounded-lg hover:cursor-pointer hover:bg-blueShade transition-all duration-100 text-[14px]"
                      key={item?._id}
                      ref={loadingTriggerRef}
                    >
                      <div className="p-2">
                        <div className="flex items-center">
                          <input
                            id={`stories-item-${index}`}
                            className="w-[16px] h-[16px]"
                            type="checkbox"
                            value={item?._id || ""}
                            onChange={handlePostIds}
                            checked={postIds?.includes(item?._id)}
                          />
                        </div>
                      </div>
                      <div className="p-2">
                        <div className="flex items-center gap-x-2 font-semibold">
                          {item?.coverImg ? (
                            <img
                              src={item?.coverImg}
                              alt=""
                              className="w-[80px] h-[60px] object-cover rounded"
                            />
                          ) : (
                            <div className="w-[80px] h-[60px] object-cover rounded bg-[#daeffe] flex items-center justify-center">
                              <div className="w-[40px] h-[40px] rounded-full bg-white flex items-center justify-center">
                                <CiImageOn className="text-[#3b82f6] text-2xl m-auto" />
                              </div>
                            </div>
                          )}
                          <div>
                            <div className="line-clamp-2">{item?.title}</div>
                            <div className="text-[#969696] text-xs font-normal flex items-center space-x-2">
                              <span>{formatDateAndTime(item?.timestamp)}</span>
                              <span>{item?.author.toString()}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </label>
                  );
                }
                return (
                  <label
                    htmlFor={`stories-item-${index}`}
                    className="flex items-center rounded-lg hover:cursor-pointer hover:bg-blueShade transition-all duration-100 text-[14px]"
                    key={item?._id}
                  >
                    <div className="p-2">
                      <div className="flex items-center">
                        <input
                          id={`stories-item-${index}`}
                          className="w-[16px] h-[16px]"
                          type="checkbox"
                          value={item?._id || ""}
                          onChange={handlePostIds}
                          checked={postIds?.includes(item?._id)}
                        />
                      </div>
                    </div>
                    <div className="p-2">
                      <div className="flex items-center gap-x-2 font-semibold">
                        {item?.coverImg ? (
                          <img
                            src={item?.coverImg}
                            alt=""
                            className="w-[80px] h-[60px] object-cover rounded"
                          />
                        ) : (
                          <div className="w-[80px] h-[60px] object-cover rounded bg-[#daeffe] flex items-center justify-center">
                            <div className="w-[40px] h-[40px] rounded-full bg-white flex items-center justify-center">
                              <CiImageOn className="text-[#3b82f6] text-2xl m-auto" />
                            </div>
                          </div>
                        )}
                        <div>
                          <div className="line-clamp-2">{item?.title}</div>
                          <div className="text-[#969696] text-xs font-normal flex items-center space-x-2">
                            <span>{formatDateAndTime(item?.timestamp)}</span>
                            <span>{item?.author.toString()}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </label>
                );
              })}
            </div>
          </div>
          <div className={styles.footer}>
            <p
              className={styles.footerSelectedText}
            >{`${postIds.length} of ${data.length} posts selected`}</p>
            <div className={styles.buttonGroup}>
              <button
                className={styles.btnPrimaryOutline}
                onClick={handleClose}
              >
                Cancel
              </button>
              <button
                className={styles.btnPrimary}
                onClick={handleSave}
                disabled={postIds.length === 0}
              >
                Save
              </button>
            </div>
          </div>
        </div>
      </OutsideClickHandler>
    </div>
  );
};

export default RelatedPostsPopup;
