import { components } from "react-select";

const { Option, SingleValue } = components;

export const CustomOption = (props) => (
  <Option {...props}>
    <div className="flex items-center gap-x-2">
      {props.data.image ? (
        <img
          src={props.data.image}
          alt={props.data.label}
          className="rounded-full w-9 h-9 object-fill"
        />
      ) : (
        <div className="relative w-10 h-10 overflow-hidden bg-gray-100 rounded-full">
          <svg
            className="absolute w-12 h-12 text-gray-400 -left-1"
            fill="currentColor"
            viewBox="0 0 20 20"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              fillRule="evenodd"
              d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"
              clipRule="evenodd"
            ></path>
          </svg>
        </div>
      )}
      <span>{props.data.label}</span>
    </div>
  </Option>
);

export const CustomSingleValue = (props) => (
  <SingleValue {...props}>
    <div className="flex items-center gap-x-2">
      {props.data.imgsrc ? (
        <img
          src={props.data.imgsrc}
          alt={props.data.label}
          className="rounded-full w-6 h-6 object-fill"
        />
      ) : (
        <div className="relative w-6 h-6 overflow-hidden bg-gray-100 rounded-full">
          <svg
            className="absolute w-8 h-8 text-gray-400 -left-1"
            fill="currentColor"
            viewBox="0 0 20 20"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              fillRule="evenodd"
              d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"
              clipRule="evenodd"
            ></path>
          </svg>
        </div>
      )}
      <span>{props.data.label}</span>
    </div>
  </SingleValue>
);
