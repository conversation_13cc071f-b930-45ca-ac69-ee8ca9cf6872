import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import { apiEndpoints } from "../../utils/constants";
import { baseQuery } from "./baseConfig";

// Define a service using a base URL and expected endpoints

const baseUrl = import.meta.env.VITE_API_URL;
export const authorsApi = createApi({
	reducerPath: "authorsApi",
	baseQuery: baseQuery,
	tagTypes: ["Authors"],
	endpoints: (builder) => ({
		getAuthorsList: builder.query({
			query: ({ search, limit, offset }) => ({
				url: `${apiEndpoints.getAuthorsList}`,
				params: { search: `${search}`, limit, offset },
			}),
			providesTags: ["Authors"],
			transformResponse: (response) => {
				return {
					count: response.totalCounts,
					data: response.data,
				};
			},
			// Only have one cache entry because the arg always maps to one string
			serializeQueryArgs: ({ endpointName, queryArgs }) => {
				// Include search in the cache key so different searches have different caches
				return `${endpointName}-${queryArgs.search}`;
			},
			merge: (currentCache, newItems, { arg: { offset } }) => {
				// If offset is 0, it means either:
				// 1. This is the first request
				// 2. Search term has changed (since you'll reset offset to 0)
				// In either case, we want to replace the cache
				if (offset === 0) {
					return newItems;
				}
				// Otherwise, merge the new items with existing cache
				currentCache.data.push(...newItems.data);
				return currentCache;
			},
			forceRefetch({ currentArg, previousArg }) {
				// Refetch if any of the arguments change
				return (
					currentArg.search !== previousArg?.search ||
					currentArg.offset !== previousArg?.offset ||
					currentArg.limit !== previousArg?.limit
				);
			},
		}),
		getAuthor: builder.query({
			query: (id) => ({
				url: `${apiEndpoints.getEditAuthor}/${id}`,
			}),
			transformResponse: (response) => response.data,
			providesTags: ["Authors"],
		}),

		createAuthor: builder.mutation({
			query: (data) => ({
				url: apiEndpoints.postAuthor,
				method: "POST",
				body: data,
			}),
			invalidatesTags: ["Authors"],
		}),
		updateAuthor: builder.mutation({
			query: ({ data, id }) => ({
				url: `${apiEndpoints.pathAuthor}/${id}`,
				method: "PATCH",
				body: data,
			}),
			invalidatesTags: ["Authors"],
		}),

		deleteAuthor: builder.mutation({
			query: (id) => ({
				url: `${apiEndpoints.deleteAuthor}/${id}`,
				method: "PATCH",
			}),
			invalidatesTags: ["Authors"],
		}),
		getMetaDataAuthor: builder.query({
			query: () => ({
				url: `${apiEndpoints.getMetaDataTemplate}`,
				params: { modelName: "Writers" },
			}),
			transformResponse: (response) => response.data[0],
		}),
		authorLogin: builder.mutation({
			query: (credentials) => ({
				url: apiEndpoints.authorSignin,
				method: "POST",
				body: credentials,
			}),
		}),
		resetPassword: builder.mutation({
			query: (body) => ({
				url: `/api/writer/reset-password`,
				method: "POST",
				body,
			}),
		}),
	}),
});

// Export hooks for usage in functional components, which are
// auto-generated based on the defined endpoints
export const {
	useGetMetaDataAuthorQuery,
	useGetAuthorsListQuery,
	useLazyGetAuthorsListQuery,
	useGetAuthorQuery,
	useDeleteAuthorMutation,
	useCreateAuthorMutation,
	useUpdateAuthorMutation,
	useAuthorLoginMutation,
	useResetPasswordMutation,
} = authorsApi;
