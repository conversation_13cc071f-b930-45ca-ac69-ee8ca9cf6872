import React, { useState } from "react";
import { setSearchFilter } from "../../../store/slices/mediaLibrarySlice";
import { SearchInput } from "../../../parts/FormComponents";
import styles from "../MediaLibrary.module.css";
import { useDispatch } from "react-redux";

const Filter = () => {
	const dispatch = useDispatch();
	const [filter, setFilter] = useState({ search: "" });

	return (
		<>
			<div className={styles.filterWrapper}>
				<div className={styles.searchBar}>
					<SearchInput
						type="text"
						value={filter?.search ?? ""}
						onChange={(value) => dispatch(setSearchFilter(value))}
						placeholder="Looking for something? Type and press Enter"
						className="border shadow rounded w-full md:w-auto h-8"
						inputClass="!h-[2rem]"
					/>
				</div>
			</div>
		</>
	);
};

export default Filter;
