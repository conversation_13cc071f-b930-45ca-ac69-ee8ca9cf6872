.modal {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.73);
  width: 100vw;
  height: 100vh;
  position: fixed;
  z-index: 5;
  top: 0;
  left: 0;
  animation-name: fadeIn;
  animation-duration: 0.2s;
  animation-iteration-count: 1;
  animation-timing-function: all ease;
}
.card {
  padding: 15px 15px;
  width: 400px;
  background-color: white;
  border-radius: 20px;
  box-shadow: rgba(100, 100, 111, 0.2) 0px 7px 29px 0px;
  animation-name: zoomin;
  animation-duration: 0.5s;
  animation-iteration-count: 1;
  animation-timing-function: ease;
  overflow-y: hidden;
}
.errorMessage {
  display: block;
  margin-top: 5px;
  font-size: 13px;
  color: red;
}
.cardBody {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  gap: 20px;
}
.imageSec {
  position: relative;
  width: 100px;
  min-width: 100px;
  height: 100px;
  overflow: hidden;
}
.imageSec img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}
.imageSec svg {
  width: 100%;
  height: 100%;
}
.contentSec {
  display: block;
  text-align: left;
  width: 100%;
}
.contentSec h4 {
  font-size: 22px;
  font-weight: 600;
  margin-bottom: 10px;
}
.contentSec p {
  font-size: 16px;
  font-weight: 300;
  color: rgba(0, 0, 0, 0.65);
  margin-bottom: 15px;
}
.box {
  margin-top: 10px;
  padding: 6px 12px;
  background-color: rgb(230, 244, 255);
  border: 1px solid #3475de;
  border-radius: 8px;
}
.box span {
  display: block;
  font-size: 15px;
  font-weight: 600;
}
.box p {
  font-size: 15px;
}
.footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-top: 20px;
  gap: 10px;
}
.btnPrimaryOutline {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 32px;
  padding: 4px 16px;
  font-size: 14px;
  background-color: transparent;
  border-radius: 25px;
  border: 1px solid #3475de;
  color: #3475de;
  transition: all 0.3s ease;
}
.btnPrimary {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 32px;
  padding: 4px 16px;
  font-size: 14px;
  background-color: #3475de;
  border-radius: 25px;
  border: 1px solid #3475de;
  color: #fff;
  transition: all 0.3s ease;
}

.btnPrimaryOutline:hover {
  background-color: #3475de;
  border-radius: 25px;
  border: 1px solid #3475de;
  color: #fff;
}
.btnPrimary:hover {
  border: 1px solid #3475de;
  background-color: #3475de;
}

@keyframes zoomin {
  0% {
    transform: scale(0.5);
  }
  100% {
    transform: scale(1);
  }
}
@keyframes fadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
