import { configureStore } from "@reduxjs/toolkit";
import usersReducer from "./slices/userSlice";
import { usersApi } from "./apis/userApi";
import { categoriesApi } from "./apis/categoriesApi";
import { storiesApi } from "./apis/storiesApi";
import storiesReducer from "./slices/storiesSlice";
import tableReducer from "./slices/tableSlice";
import { editorSlice } from "./slices/editorSlice";
import editorReducer from "./slices/editorSlice";
import videoStoryReducer from "./slices/videoStorySlice";
import authorsReducer from "./slices/authorsSlice";
import { videoStoryApi } from "./apis/videoStoryApi";
import { authorsApi } from "./apis/authorsApi";
import { tagsApi } from "./apis/tagsApi";
import tagsReducer from "./slices/tagsSlice";
import categoriesReducer from "./slices/categoriesSlice";
import flaggedStoriesReducer from "./slices/flaggedStoriesSlice";
import webStoryReducer from "./slices/webstorySlice";
import { flaggedStoriesApi } from "./apis/flaggedStoriesApi";
import { webStoryApi } from "./apis/webStoryApi";
import mediaLibraryReducer from "./slices/mediaLibrarySlice";
import { mediaLibraryApi } from "./apis/mediaLibraryApi";
import emailMarketingReducer from "./slices/emailMarketingSlice";
// UNCOMMENT WHEN API IS READY:
import { emailMarketingApi } from "./apis/emailMarketingApi";

export const store = configureStore({
  reducer: {
    user: usersReducer,
    stories: storiesReducer,
    table: tableReducer,
    editor: editorReducer,
    videoStory: videoStoryReducer,
    authors: authorsReducer,
    tags: tagsReducer,
    categories: categoriesReducer,
    flaggedStory: flaggedStoriesReducer,
    webStory: webStoryReducer,
    mediaLibrary: mediaLibraryReducer,
    emailMarketing: emailMarketingReducer,
    [usersApi.reducerPath]: usersApi.reducer,
    [categoriesApi.reducerPath]: categoriesApi.reducer,
    [storiesApi.reducerPath]: storiesApi.reducer,
    [videoStoryApi.reducerPath]: videoStoryApi.reducer,
    [authorsApi.reducerPath]: authorsApi.reducer,
    [tagsApi.reducerPath]: tagsApi.reducer,
    [flaggedStoriesApi.reducerPath]: flaggedStoriesApi.reducer,
    [webStoryApi.reducerPath]: webStoryApi.reducer,
    [mediaLibraryApi.reducerPath]: mediaLibraryApi.reducer,
    // UNCOMMENT WHEN API IS READY:
    [emailMarketingApi.reducerPath]: emailMarketingApi.reducer,
  },
  middleware: (middleware) => [
    ...middleware(),
    usersApi.middleware,
    categoriesApi.middleware,
    storiesApi.middleware, // using it for both vidoe stories and text stories
    videoStoryApi.middleware,
    authorsApi.middleware,
    tagsApi.middleware,
    flaggedStoriesApi.middleware,
    webStoryApi.middleware,
    mediaLibraryApi.middleware,
    // UNCOMMENT WHEN API IS READY:
    emailMarketingApi.middleware,
  ],
});
