import { createSlice } from "@reduxjs/toolkit";
import {
	filterStoryStatusState,
	incrementOffsetState,
	resetFiltersState,
	setCanonicalState,
	setFetchedDataState,
	setHighlightedSubCategoryState,
	setImageState,
	setIndexingState,
	setIntialFiltersStateData,
	setKeyWordsState,
	setMetaDataState,
	setOgContentState,
	setSubCategoryState,
	setXContentState,
	storiesInputFilterState,
	toggleFlagState,
} from "./sharedReducers";
import { generateSlugStories } from "../../utils/helperFunctions";

const initialState = {
	_id: null,
	data: [],
	errors: {},
	filter: {
		status: null,
		search: "",
	},
	limit: 20,
	offset: 0,
	hasMore: true,
	editCoverImg: false,
	croppedImg: [],
	storiesState: {
		title: "",
		excerpt: "",
		subcategory: "",
		contributors: [],
		isHighlighted: false,
		section: [],
		publishDate: null,
		writer: [],
		highligthedSubCategory: null,
		meta: {
			title: null,
			description: null,
			keywords: [],
			primaryKeywords: [],
			slug: null,
			author: null,
			canonical: null,
			robots: "index,follow",
			_id: null,
		},
		ogImg: [],
		twitterImg: [],
		og: {
			title: null,
			description: null,
		},
		twitter: {
			title: null,
			description: null,
			card: "large",
		},
	},
	src: null,
	status: 3,
	isYoutube: true,
	coverImg: null,
	isCustomThumbnail: false,
	youtubeUrl: null,
};

export const videoStory = createSlice({
	name: "videoStory",
	initialState,
	reducers: {
		setCoverImgTransformData: (state, { payload }) => {
			state.storiesState.coverImgFocalPosition = payload;
		},
		setEditCoverImg: (state, { payload }) => {
			state.editCoverImg = payload;
		},
		setErrors: (state, action) => {
			state.errors = action.payload; // Set validation errors
		},
		clearErrors: (state) => {
			state.errors = {};
		},

		videoFilterStoryStatus: (state, { payload }) => filterStoryStatusState(state, payload),

		videoStoriesInputFilter: (state, { payload }) => storiesInputFilterState(state, payload),
		incrementOffset: (state) => incrementOffsetState(state),
		resetFilters: (state, { payload }) => resetFiltersState(state, payload),
		// for handling the tag toggle in the filter
		setVideoSubCategory: (state, { payload }) => setSubCategoryState(state, payload),
		setHighlightedVideoSubCategory: (state, { payload }) =>
			setHighlightedSubCategoryState(state, payload),
		setFetchedData: (state, { payload }) => setFetchedDataState(state, payload),

		// set meta data here
		setVideoMetaData: (state, { payload }) => setMetaDataState(state, payload),

		// set keywords here
		setVideoKeyWords: (state, { payload }) => setKeyWordsState(state, payload),

		// set the canonical here
		setVideoCanonical: (state, { payload }) => setCanonicalState(state, payload),

		// set indexing
		setVideoIndexing: (state, { payload }) => setIndexingState(state, payload),
		//set twitter and og image
		setVideoImage: (state, { payload }) => setImageState(state, payload),

		// set og content
		setVideoOgContent: (state, { payload }) => setOgContentState(state, payload),
		// set x content
		setVideoXContent: (state, { payload }) => setXContentState(state, payload),

		//set toggle state
		toggleVideoFlag: (state, { payload }) => toggleFlagState(state, payload),

		//set excerpt here
		setInputData: (state, { payload }) => {
			const { name, value } = payload;
			state.storiesState[name] = value;
		},

		//set youtube video here
		setYoutubeVideo: (state, action) => {
			const { youtubeUrl, thumbnailUrl } = action.payload;
			state.src = youtubeUrl;
			state.youtubeUrl = youtubeUrl;
			state.isYoutube = true;
			state.storiesState.ogImg = thumbnailUrl;
			state.storiesState.twitterImg = thumbnailUrl;
			state.coverImg = thumbnailUrl;
			state.isCustomThumbnail = false;
		},

		resetYoutubeVideoData: (state, { payload }) => {
			state.src = null;
			state.isYoutube = false;
			state.youtubeUrl = null;
			state.storiesState.ogImg = [];
			state.storiesState.twitterImg = [];
			state.coverImg = null;
			state.isCustomThumbnail = false;
			state.storiesState.twitterImg = [];
			state.storiesState.ogImg = [];
		},

		resetLocalVideoData: (state, { payload }) => {
			state.src = null;
			state.isYoutube = false;
			state.coverImg = null;
			state.youtubeUrl = null;
			state.isCustomThumbnail = false;
			state.storiesState.twitterImg = [];
			state.storiesState.ogImg = [];
		},

		//set local video here
		setLocalVideo: (state, action) => {
			const { videoFile, thumbnailFile } = action.payload;
			state.src = videoFile;
			state.isYoutube = false;
			state.coverImg = thumbnailFile;
			state.isCustomThumbnail = false;
			state.storiesState.ogImg = [thumbnailFile];
			state.storiesState.twitterImg = [thumbnailFile];
		},

		setYoutubeStatus: (state, { payload }) => {
			state.isYoutube = payload;
		},

		// update thumbnail here
		updateThumbnail: (state, action) => {
			state.coverImg = action.payload;
			state.storiesState.ogImg = [action.payload];
			state.storiesState.twitterImg = [action.payload];
			state.isCustomThumbnail = true;
		},

		// reset video upload
		resetVideoUpload: (state) => {
			return initialState;
		},
		setInititalFilterData: (state, { payload }) => setIntialFiltersStateData(state, payload),

		setVideoStoryData: (state, { payload }) => {
			const {
				_id,
				coverImg,
				src,
				isYoutube,
				status,
				template,
				type,
				writer,
				contributor,
				meta,
				section,
				excerpt,
				title,
				slug,
				subcategory,
			} = payload;

			// Assign root-level properties
			state._id = _id;
			state.coverImg = coverImg;
			state.src = src;
			state.youtubeUrl = src;
			state.isYoutube = isYoutube;
			state.status = status;

			// Update `storiesState` properties
			state.storiesState = {
				...state.storiesState,
				title,
				excerpt,
				subcategory,
				writer: writer || [],
				contributors: contributor || [],
				section: section || [],
				isHighlighted: state.storiesState.isHighlighted, // Preserve existing highlight state
				highligthedSubCategory: state.storiesState.highligthedSubCategory, // Preserve existing value
				meta: {
					...state.storiesState.meta,
					title: meta?.title || state.storiesState.meta.title,
					description: meta?.description || state.storiesState.meta.description,
					keywords: meta?.keywords || state.storiesState.meta.keywords,
					slug: generateSlugStories(slug) || state.storiesState.meta.slug,
					primaryKeywords: payload?.meta?.primaryKeywords || [],
					canonical: meta?.canonical || state.storiesState.meta.canonical,
					robots: meta?.robots || state.storiesState.meta.robots,
				},
				og: {
					...state.storiesState.og,
					title: meta?.og?.title,
					description: meta?.og?.description,
				},
				twitter: {
					...state.storiesState.twitter,
					title: meta?.twitter?.title,
					description: meta?.twitter?.description,
					card: meta?.twitter?.card === "summary_large_image" ? "large" : "small",
				},
				ogImg: meta?.og?.image || state.storiesState.ogImg,
				twitterImg: meta?.twitter?.image || state.storiesState.twitterImg,
			};
		},
		resetState: () => initialState,
		setStatus: (state, { payload }) => {
			state.status = payload;
		},
		setScheduleTime: (state, { payload }) => {
			state.status = payload.status;
			state.storiesState.publishDate = payload.publishDate;
		},
		setVideoPublishedData: (state, { payload }) => {
			state.storiesState.publishDate = payload;
		},
	},
});
// Action creators are generated for each case reducer function
export const {
	setVideoPublishedData,
	setVideoSubCategory,
	setHighlightedVideoSubCategory,
	setVideoKeyWords,
	setVideoCanonical,
	setVideoImage,
	setVideoIndexing,
	setVideoMetaData,
	setVideoOgContent,
	setVideoXContent,
	toggleVideoFlag,
	setInputData,
	setYoutubeVideo,
	setYoutubeStatus,
	resetVideoUpload,
	setLocalVideo,
	updateThumbnail,
	setVideoStoryData,
	resetState,
	setFetchedData,
	videoFilterStoryStatus,
	incrementOffset,
	resetFilters,
	videoStoriesInputFilter,
	setErrors,
	clearErrors,
	setStatus,
	setInititalFilterData,
	resetLocalVideoData,
	resetYoutubeVideoData,
	setCoverImgTransformData,
	setEditCoverImg,
	setScheduleTime,
} = videoStory.actions;

export default videoStory.reducer;
