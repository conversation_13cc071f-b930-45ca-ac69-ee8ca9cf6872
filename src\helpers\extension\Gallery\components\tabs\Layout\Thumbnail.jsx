import React from "react";
import { GoCheck } from "react-icons/go";

const Thumbnail = ({ icon, title = "", selected, ...props }) => {
  return (
    <div className="block cursor-pointer aspect-square" {...props}>
      <div
        className={`relative transition-all duration-100 ease-in-out flex justify-center items-center w-full h-full after:content-[''] after:pointer-events-none after:absolute after:inset-0 after:rounded-[5px] after:shadow-[0_0_0_1px_rgba(8,78,189,0.24)] hover:after:shadow-[0_0_0_1px_#3b82f6] ${
          selected ? "after:!shadow-[0_0_0_1px_#3b82f6]" : ""
        }`}
      >
        {selected && (
          <div className="absolute right-[-5px] top-[-5px] flex justify-center items-center w-[20px] h-[20px] z-50 rounded-full bg-[#3b82f6]">
            <GoCheck className="text-[white] text-sm stroke-1" />
          </div>
        )}
        <div className="flex w-full h-full min-w-0 min-h-0 max-w-full">
          <div className={`flex justify-center items-center w-full h-full`}>
            {icon}
          </div>
        </div>
      </div>
      {title && (
        <div className="flex items-center justify-center w-full mt-[5px]">
          <span className="inline-block break-all text-xs font-light whitespace-nowrap text-center text-ellipsis overflow-hidden max-w-full align-bottom">
            {title}
          </span>
        </div>
      )}
    </div>
  );
};

export default Thumbnail;
