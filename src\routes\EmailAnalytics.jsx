import React, { useEffect, useState } from "react";
import Container from "../parts/Container";
import BreadCrumb from "../parts/BreadCrumb";
import { useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { toast } from "react-toastify";
import Table from "../parts/Table";
import { formatDateAndTime } from "../utils/helperFunctions";
import { RoundedIconsButton } from "../parts/Button";
import { BsEye } from "react-icons/bs";
// UNCOMMENT WHEN API IS READY:
// import { useGetCampaignsQuery, useGetOverallAnalyticsQuery } from "../store/apis/emailMarketingApi";
import { dummyCampaigns } from "../store/slices/emailMarketingSlice";

const EmailAnalytics = () => {
	const navigate = useNavigate();
	const [dateRange, setDateRange] = useState({
		startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split("T")[0], // 30 days ago
		endDate: new Date().toISOString().split("T")[0], // today
	});

	// Get data from Redux store (initialized with dummy data)
	const { data: analyticsData, isLoading: isLoadingAnalytics } = useSelector(
		(state) => state.emailMarketing.analytics
	);
	const campaignsData = dummyCampaigns;
	const isLoadingCampaigns = false;

	// UNCOMMENT WHEN API IS READY:
	// // Fetch campaigns
	// const { data: campaignsData, isLoading: isLoadingCampaigns } = useGetCampaignsQuery({
	//   limit: 100,
	//   offset: 0,
	// });
	//
	// // Fetch analytics data
	// const { data: analyticsData, isLoading: isLoadingAnalytics } = useGetOverallAnalyticsQuery({
	//   startDate: dateRange.startDate,
	//   endDate: dateRange.endDate,
	// });
	//
	// useEffect(() => {
	//   if (analyticsData) {
	//     dispatch(setAnalyticsData(analyticsData));
	//   }
	// }, [analyticsData, dispatch]);

	// Simulate date range filter effect
	useEffect(() => {
		console.log("Date range changed:", dateRange);
		// In a real implementation, this would trigger a new API call
	}, [dateRange]);

	// Table columns for campaigns
	const columns = [
		{
			accessorKey: "subject",
			id: "subject",
			size: 300,
			header: () => "Campaign",
			cell: ({ row }) => {
				return (
					<div className="flex items-center gap-x-2 font-semibold">
						<div>
							<div className="line-clamp-2">{row.original.subject}</div>
							<div className="text-[#969696] text-xs font-normal flex items-center space-x-2">
								<span>{formatDateAndTime(row.original?.sentAt || row.original?.scheduledAt)}</span>
							</div>
						</div>
					</div>
				);
			},
		},
		{
			accessorKey: "status",
			header: "Status",
			size: 120,
			cell: ({ row }) => (
				<div
					className={`px-2 py-1 rounded-full text-center text-xs ${
						row.original.status === "sent"
							? "bg-green-100 text-green-800"
							: row.original.status === "scheduled"
							? "bg-blue-100 text-blue-800"
							: "bg-yellow-100 text-yellow-800"
					}`}
				>
					{row.original.status === "sent"
						? "Sent"
						: row.original.status === "scheduled"
						? "Scheduled"
						: "Draft"}
				</div>
			),
		},
		{
			accessorKey: "recipients",
			header: "Recipients",
			size: 100,
			cell: ({ row }) => <div className="text-center">{row.original.recipientCount || 0}</div>,
		},
		{
			accessorKey: "opened",
			header: "Opened",
			size: 100,
			cell: ({ row }) => (
				<div className="text-center">
					{row.original.openCount || 0}
					<span className="text-xs text-gray-500 ml-1">
						({row.original.openRate ? `${row.original.openRate}%` : "0%"})
					</span>
				</div>
			),
		},
		{
			accessorKey: "clicked",
			header: "Clicked",
			size: 100,
			cell: ({ row }) => (
				<div className="text-center">
					{row.original.clickCount || 0}
					<span className="text-xs text-gray-500 ml-1">
						({row.original.clickRate ? `${row.original.clickRate}%` : "0%"})
					</span>
				</div>
			),
		},
		{
			accessorKey: "actions",
			header: "Action",
			size: 80,
			cell: ({ row }) => {
				return (
					<div className="flex items-center gap-x-2">
						<RoundedIconsButton
							onClick={() => navigate(`/admin/email/analytics/campaign/${row.original._id}`)}
						>
							<BsEye className="h-[15px] w-[15px]" />
						</RoundedIconsButton>
					</div>
				);
			},
		},
	];

	// Render analytics cards
	const renderAnalyticsCards = () => {
		if (isLoadingAnalytics) {
			return (
				<div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
					{[1, 2, 3, 4].map((i) => (
						<div key={i} className="bg-white rounded-lg border border-gray-200 p-6 animate-pulse">
							<div className="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
							<div className="h-8 bg-gray-200 rounded w-1/4"></div>
						</div>
					))}
				</div>
			);
		}

		const stats = analyticsData || {
			totalSent: 0,
			totalOpened: 0,
			totalClicked: 0,
			averageOpenRate: 0,
		};

		return (
			<div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
				<div className="bg-white rounded-lg border border-gray-200 p-6">
					<h3 className="text-gray-500 text-sm font-medium mb-2">Total Emails Sent</h3>
					<p className="text-3xl font-bold">{stats.totalSent}</p>
				</div>

				<div className="bg-white rounded-lg border border-gray-200 p-6">
					<h3 className="text-gray-500 text-sm font-medium mb-2">Total Opens</h3>
					<p className="text-3xl font-bold">{stats.totalOpened}</p>
				</div>

				<div className="bg-white rounded-lg border border-gray-200 p-6">
					<h3 className="text-gray-500 text-sm font-medium mb-2">Total Clicks</h3>
					<p className="text-3xl font-bold">{stats.totalClicked}</p>
				</div>

				<div className="bg-white rounded-lg border border-gray-200 p-6">
					<h3 className="text-gray-500 text-sm font-medium mb-2">Average Open Rate</h3>
					<p className="text-3xl font-bold">{stats.averageOpenRate}%</p>
				</div>
			</div>
		);
	};

	// Render date range selector
	const renderDateRangeSelector = () => {
		return (
			<div className="bg-white rounded-lg border border-gray-200 p-4 mb-6">
				<div className="flex flex-col md:flex-row items-center gap-4">
					<div>
						<label className="block text-sm font-medium text-gray-700 mb-1">Start Date</label>
						<input
							type="date"
							value={dateRange.startDate}
							onChange={(e) => setDateRange({ ...dateRange, startDate: e.target.value })}
							className="p-2 border border-gray-300 rounded-md"
						/>
					</div>

					<div>
						<label className="block text-sm font-medium text-gray-700 mb-1">End Date</label>
						<input
							type="date"
							value={dateRange.endDate}
							onChange={(e) => setDateRange({ ...dateRange, endDate: e.target.value })}
							max={new Date().toISOString().split("T")[0]}
							className="p-2 border border-gray-300 rounded-md"
						/>
					</div>

					<div className="flex-1"></div>

					<div className="flex gap-2">
						<button
							onClick={() =>
								setDateRange({
									startDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
										.toISOString()
										.split("T")[0],
									endDate: new Date().toISOString().split("T")[0],
								})
							}
							className="px-3 py-2 text-sm bg-gray-100 hover:bg-gray-200 rounded-md"
						>
							Last 7 days
						</button>

						<button
							onClick={() =>
								setDateRange({
									startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
										.toISOString()
										.split("T")[0],
									endDate: new Date().toISOString().split("T")[0],
								})
							}
							className="px-3 py-2 text-sm bg-gray-100 hover:bg-gray-200 rounded-md"
						>
							Last 30 days
						</button>

						<button
							onClick={() =>
								setDateRange({
									startDate: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000)
										.toISOString()
										.split("T")[0],
									endDate: new Date().toISOString().split("T")[0],
								})
							}
							className="px-3 py-2 text-sm bg-gray-100 hover:bg-gray-200 rounded-md"
						>
							Last 90 days
						</button>
					</div>
				</div>
			</div>
		);
	};

	return (
		<Container>
			<div className="lg:flex items-center justify-between mb-5">
				<div>
					<BreadCrumb
						title={"Email Analytics"}
						description={"Track and analyze your email campaign performance."}
					/>
				</div>
			</div>

			{renderDateRangeSelector()}
			{renderAnalyticsCards()}

			<div className="bg-white rounded-lg border border-gray-200 p-6">
				<h2 className="text-lg font-semibold mb-4">Campaign Performance</h2>

				<Table
					module="emailCampaigns"
					data={campaignsData || []}
					actionColumn={6}
					isLoading={isLoadingCampaigns}
					columns={columns}
					customClass={""}
				/>
			</div>
		</Container>
	);
};

export default EmailAnalytics;
