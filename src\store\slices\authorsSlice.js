import { createSlice } from "@reduxjs/toolkit";
import {
  incrementOffsetState,
  resetFiltersState,
  setCanonicalState,
  setImageState,
  setIndexingState,
  setKeyWordsState,
  setMetaDataState,
  setMetaDataTemplateData,
  setOgContentState,
  setXContentState,
  storiesInputFilterState,
} from "./sharedReducers";
import { generateSlugStories } from "../../utils/helperFunctions";

const initialState = {
  filter: {
    status: null,
    search: "",
  },
  data: [],
  limit: 20,
  offset: 0,
  hasMore: true,
  firstname: "",
  _id: null,
  lastname: "",
  email: "",
  password: "",
  subheading: "",
  facebook: "",
  instagram: "",
  twitterX: "",
  linkedin: "",
  aboutus: "",
  metaDescription: "",
  errors: {},

  storiesState: {
    meta: {
      title: null,
      description: null,
      keywords: [],
      slug: null,
      author: null,
      canonical: null,
      robots: "index,follow",
      _id: null,
    },
    ogImg: [],
    imgsrc: [],
    twitterImg: [],
    og: {
      title: null,
      description: null,
    },
    twitter: {
      title: null,
      description: null,
      card: "large",
    },
  },
};

export const authorsSlice = createSlice({
  name: "authors",
  initialState,
  reducers: {
    setAuthorsInutFilter: (state, { payload }) =>
      storiesInputFilterState(state, payload),
    resetFilters: (state, { payload }) => resetFiltersState(state),
    incrementOffset: (state) => incrementOffsetState(state),

    // set meta data here
    setAuthorMetaData: (state, { payload }) => setMetaDataState(state, payload),

    // set keywords here
    setAuthorKeyWords: (state, { payload }) => setKeyWordsState(state, payload),

    // set the canonical here
    setAuthorCanonical: (state, { payload }) =>
      setCanonicalState(state, payload),

    // set indexing
    setAuthorIndexing: (state, { payload }) => setIndexingState(state, payload),
    //set twitter and og image
    setAuthorImage: (state, { payload }) => setImageState(state, payload),

    // set og content
    setAuthorOgContent: (state, { payload }) =>
      setOgContentState(state, payload),
    // set x content
    setAuthorXContent: (state, { payload }) => setXContentState(state, payload),
    setInputData: (state, { payload }) => {
      const { name, value } = payload;
      state.storiesState[name] = value;
    },

    setAuthorProfile: (state, { payload }) => {
      const { name, value } = payload;
      state[name] = value;
    },
    setErrors: (state, action) => {
      state.errors = action.payload; // Set validation errors
    },
    clearErrors: (state) => {
      state.errors = {};
    },
    setAuthorsData: (state, { payload }) => {
      if (payload) {
        const {
          _id,
          firstname,
          lastname,
          password,
          email,
          subheading,
          social = {},
          aboutus,
          imgsrc,
          slug,
          meta = {},
          twitter = {},
        } = payload;

        const {
          facebook = "",
          instagram = "",
          twitterX = "",
          linkedin = "",
        } = social;

        state._id = _id || null;
        state.firstname = firstname || "";
        state.lastname = lastname || "";
        state.password = password || "";
        state.email = email || "";
        state.subheading = subheading || "";
        state.facebook = facebook;
        state.instagram = instagram;
        state.twitterX = twitterX;
        state.linkedin = linkedin;
        state.aboutus = aboutus || "";

        // Update storiesState properties
        state.storiesState.imgsrc = imgsrc || [];
        state.storiesState.meta = {
          ...state.storiesState.meta,
          title: meta.title || null,
          description: meta.description || null,
          keywords: meta.keywords || [],
          slug: generateSlugStories(slug) || null,
          author: meta.author || null,
          canonical: meta.canonical || null,
          robots: meta.robots || "index,follow",
          _id: meta._id || null,
        };
        state.storiesState.twitter = {
          ...state.storiesState.twitter,
          title: meta?.twitter?.title || null,
          description: meta?.twitter?.description || null,
          card:
            meta?.twitter?.card === "summary_large_image" ? "large" : "small",
        };
        state.storiesState.twitterImg = meta.twitter?.image || [];

        state.storiesState.ogImg = meta.og?.image || [];
        state.storiesState.og = {
          ...state.storiesState.og,
          title: meta.title || null,
          description: meta.description || null,
        };
      }
    },
    setAuthorsMetaTemplateData: (state, { payload }) =>
      setMetaDataTemplateData(state, payload),
    replaceAuthorMetaData: (state, { payload }) => {
      const { name, value } = payload;
      state.storiesState.meta[name] = value;
    },
    resetState: () => initialState,
  },
});
// Action creators are generated for each case reducer function
export const {
  setAuthorsMetaTemplateData,
  replaceAuthorMetaData,
  setAuthorMetaData,
  setAuthorCanonical,
  setAuthorImage,
  setAuthorIndexing,
  setAuthorKeyWords,
  setAuthorOgContent,
  setAuthorXContent,
  setAuthorProfile,
  setErrors,
  clearErrors,
  setInputData,
  setAuthorsData,
  resetState,
  incrementOffset,
  resetFilters,
  setAuthorsInutFilter,
} = authorsSlice.actions;

export default authorsSlice.reducer;
