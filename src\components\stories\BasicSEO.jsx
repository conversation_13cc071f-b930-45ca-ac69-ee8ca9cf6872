import React, { useState, useEffect } from "react";
import { Input } from "../../parts/FormComponents";
import {
	setErrors,
	setIndexing,
	setKeyWords,
	setMetaData,
	setMetaTitleActive,
} from "../../store/slices/storiesSlice";
import { useDispatch, useSelector } from "react-redux";
import { RxCross2 } from "react-icons/rx";
import ToggleSwitch from "../../parts/ToggleSwitch";
import { storyType } from "../../utils/constants";
import {
	setVideoIndexing,
	setVideoKeyWords,
	setVideoMetaData,
} from "../../store/slices/videoStorySlice";
import {
	setAuthorIndexing,
	setAuthorKeyWords,
	setAuthorMetaData,
	setErrors as setAuthorErrors,
} from "../../store/slices/authorsSlice";
import { setTagIndexing, setTagKeyWords, setTagMetaData } from "../../store/slices/tagsSlice";
import { generateSlugStories } from "../../utils/helperFunctions";
import {
	setWebStoryIndexing,
	setWebStoryKeyWords,
	setWebStoryMetaData,
} from "../../store/slices/webstorySlice";
import { ImQrcode } from "react-icons/im";

const BasicSEO = ({ type = "stories" }) => {
	const dispatch = useDispatch();

	const {
		storiesState: { meta },
		status,
		errors,
	} = useSelector((state) => state[storyType[type]]);

	const [keywords, setKeywordsState] = useState([]);
	const [newKeyword, setNewKeyword] = useState("");
	const [primaryKeywords, setPrimaryKeywordsState] = useState([]);
	const [newPrimaryKeyword, setNewPrimaryKeyword] = useState("");

	const [seoState, setSeoState] = useState({
		isCanonical: meta.canonical ? true : false,
		indexing: true,
	});

	useEffect(() => {
		if (meta.keywords && meta.keywords.length > 0) {
			setKeywordsState(meta.keywords);
		}
		if (meta.primaryKeywords && meta.primaryKeywords.length > 0) {
			setPrimaryKeywordsState(meta.primaryKeywords);
		}
	}, [meta.keywords, meta.primaryKeywords]);

	const handleDataChange = ({ name, value }) => {
		const mappedNames = {
			title: "metaTitle",
			description: "metaDescription",
			slug: "slug",
		};

		if (["title", "description", "slug"].includes(name) && !value) {
			const errorPayload = { ...errors, [mappedNames[name]]: `${name} is required` };
			if (type === "stories") dispatch(setErrors(errorPayload));
			else if (type === "authors") dispatch(setAuthorErrors(errorPayload));
		} else {
			const errorPayload = { ...errors, [mappedNames[name]]: null };
			if (type === "stories") dispatch(setErrors(errorPayload));
			else if (type === "authors") dispatch(setAuthorErrors(errorPayload));
		}

		const payload = { name, value };
		switch (type) {
			case "stories":
				dispatch(setMetaData(payload));
				break;
			case "videoStory":
				dispatch(setVideoMetaData(payload));
				break;
			case "authors":
				dispatch(setAuthorMetaData(payload));
				break;
			case "tags":
				dispatch(setTagMetaData(payload));
				break;
			case "webStory":
				dispatch(setWebStoryMetaData(payload));
				break;
			default:
				break;
		}
	};

	const keywordDispatcher = (updatedKeywords, isPrimary = false) => {
		switch (type) {
			case "stories":
				dispatch(
					isPrimary
						? setMetaData({ name: "primaryKeywords", value: updatedKeywords })
						: setKeyWords(updatedKeywords)
				);
				break;
			case "videoStory":
				dispatch(
					isPrimary
						? setVideoMetaData({ name: "primaryKeywords", value: updatedKeywords })
						: setVideoKeyWords(updatedKeywords)
				);
				break;
			case "authors":
				dispatch(
					setAuthorKeyWords({
						name: isPrimary ? "primaryKeywords" : "keywords",
						value: updatedKeywords,
					})
				);
				break;
			case "tags":
				dispatch(
					isPrimary
						? setTagMetaData({ name: "primaryKeywords", value: updatedKeywords })
						: setTagKeyWords(updatedKeywords)
				);
				break;
			case "webStory":
				dispatch(
					isPrimary
						? setWebStoryMetaData({ name: "primaryKeywords", value: updatedKeywords })
						: setWebStoryKeyWords(updatedKeywords)
				);
				break;
			default:
				break;
		}
	};

	const handleAddKeyword = (isPrimary = false) => {
		const value = isPrimary ? newPrimaryKeyword.trim() : newKeyword.trim();
		if (!value) return;
		const current = isPrimary ? primaryKeywords : keywords;
		if (!current.includes(value)) {
			const updated = [...current, value];
			isPrimary ? setPrimaryKeywordsState(updated) : setKeywordsState(updated);
			keywordDispatcher(updated, isPrimary);
			isPrimary ? setNewPrimaryKeyword("") : setNewKeyword("");
		}
	};

	const handleRemoveKeyword = (value, isPrimary = false) => {
		const updated = (isPrimary ? primaryKeywords : keywords).filter((kw) => kw !== value);
		isPrimary ? setPrimaryKeywordsState(updated) : setKeywordsState(updated);
		keywordDispatcher(updated, isPrimary);
	};

	const toggleSeoState = (field) => {
		setSeoState((prev) => {
			const updated = { ...prev, [field]: !prev[field] };
			if (!updated.isCanonical) {
				handleDataChange({ name: "canonical", value: null });
			}
			return updated;
		});
	};

	useEffect(() => {
		if (meta && meta.canonical?.length > 0) {
			setSeoState((prev) => ({ ...prev, isCanonical: true }));
		}
	}, [meta]);

	return (
		<div className="flex flex-col gap-y-4 text-sm text-fadeGray">
			<div>
				<p>Preview on Google</p>
				<p>When will changes show live?</p>
				<div className="border rounded p-4 mt-2 ">
					<div className="flex items-center">
						<div className="text-xs w-5 h-5 rounded-full border flex justify-center items-center p-4">
							R
						</div>
						<div className="flex flex-col ml-2">
							<div className="-mb-1">RMS</div>
							<div className="text-[10px]">https://www.rms.com &gt; prefix</div>
						</div>
					</div>
				</div>
			</div>

			<div>
				<Input
					label="Title Tag"
					name="title"
					id="title"
					value={meta.title}
					placeholder="Search engines may show a different title"
					onDebouncedChange={(value) => {
						if (type === "stories" || type === "webStory" || type === "videoStory") {
							if (status !== 1) {
								const slug = generateSlugStories(value);
								if (type === "stories") {
									dispatch(setMetaTitleActive());
									dispatch(setMetaData({ name: "slug", value: slug }));
								} else if (type === "webStory") {
									dispatch(setWebStoryMetaData({ name: "slug", value: slug }));
								} else if (type === "videoStory") {
									dispatch(setVideoMetaData({ name: "slug", value: slug }));
								}
							}
						}
						handleDataChange({ value, name: "title" });
					}}
					toolTip="Search engines may show a different title"
				/>
				{errors.metaTitle ? (
					<small className="text-sm text-red-600 capitalize">{errors.metaTitle}</small>
				) : (
					<small className="text-sm text-primary">
						Total {meta.title ? meta.title.length : 0} characters
					</small>
				)}
			</div>
			<div>
				<Input
					label="Meta Description"
					name="description"
					id="description"
					value={meta.description}
					placeholder="Search engines may show a different description"
					onDebouncedChange={(value) => handleDataChange({ value, name: "description" })}
					toolTip="Search engines may show a different description"
				/>
				{errors.metaDescription ? (
					<small className="text-sm text-red-600 capitalize">{errors.metaDescription}</small>
				) : (
					<small className="text-sm text-primary">
						Total {meta.description ? meta.description.length : 0} characters
					</small>
				)}
			</div>

			<div>
				<Input
					label="Keywords"
					name="keywords"
					value={newKeyword}
					onDebouncedChange={setNewKeyword}
					onEnter={() => handleAddKeyword(false)}
					placeholder="Add Keywords"
					type="text"
					toolTip="Add Keywords"
				/>
				<div className="flex flex-wrap gap-2 mt-2">
					{keywords.map((kw, i) => (
						<span
							key={i}
							className="flex items-center gap-2 px-3 py-1 rounded-full bg-primary text-white text-sm font-medium cursor-pointer"
							onClick={() => handleRemoveKeyword(kw, false)}
						>
							{kw} <RxCross2 className="text-[16px]" />
						</span>
					))}
				</div>
			</div>
			<div>
				<Input
					label="Primary Keywords"
					name="primaryKeywords"
					value={newPrimaryKeyword}
					onDebouncedChange={setNewPrimaryKeyword}
					onEnter={() => handleAddKeyword(true)}
					placeholder="Add Primary Keywords"
					type="text"
					toolTip="Add Primary Keywords"
				/>
				<div className="flex flex-wrap gap-2 mt-2">
					{primaryKeywords.map((kw, i) => (
						<span
							key={i}
							className="flex items-center gap-2 px-3 py-1 rounded-full bg-primary text-white text-sm font-medium cursor-pointer"
							onClick={() => handleRemoveKeyword(kw, true)}
						>
							{kw} <RxCross2 className="text-[16px]" />
						</span>
					))}
				</div>
			</div>

			<div>
				<Input
					label="URL slug"
					name="slug"
					isSlug={true}
					id="slug"
					placeholder="Add the last part of the URL"
					type="text"
					disabled={
						(type === "stories" || type === "videoStory" || type === "webStory") && status === 1
					}
					value={meta.slug}
					toolTip="Add Slugs"
					onDebouncedChange={(value) =>
						handleDataChange({ value: generateSlugStories(value), name: "slug" })
					}
				/>
				{errors.slug ? (
					<small className="text-sm text-red-600 capitalize">{errors.slug}</small>
				) : null}
			</div>
			<div className="flex items-center gap-x-2">
				<ToggleSwitch
					checked={seoState.isCanonical}
					onChange={() => toggleSeoState("isCanonical")}
				/>
				<div>Let enable the canonical link</div>
			</div>
			{seoState.isCanonical && (
				<Input
					label="Canonical Link"
					name="canonical"
					id="canonical"
					placeholder="https://example.com/sample"
					type="text"
					value={meta.canonical}
					toolTip="Add Canonical URL"
					onDebouncedChange={(value) => handleDataChange({ value, name: "canonical" })}
				/>
			)}

			<div className="flex items-center gap-x-2">
				<ToggleSwitch
					checked={meta.robots === "index,follow"}
					onChange={() => {
						if (type === "stories") {
							dispatch(setIndexing()); // Dispatch indexing update to Redux
						} else if (type === "videoStory") {
							dispatch(setVideoIndexing());
						} else if (type === "authors") {
							dispatch(setAuthorIndexing());
						} else if (type === "tags") {
							dispatch(setTagIndexing());
						} else if (type === "webStory") {
							dispatch(setWebStoryIndexing());
						}
					}}
				/>
				<div>Let search engines index this page</div>
			</div>
		</div>
	);
};

export default BasicSEO;
