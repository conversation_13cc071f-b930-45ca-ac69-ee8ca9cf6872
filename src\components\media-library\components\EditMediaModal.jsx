import React, { useState, useEffect } from "react";
import { toast } from "react-toastify";
import Button from "../../../parts/Button";
import { Input } from "../../../parts/FormComponents";
import { useUpdateMediaMutation } from "../../../store/apis/mediaLibraryApi";

const EditMediaModal = ({ isOpen, mediaItem, onClose, onUpdate }) => {
	const [updateMedia, { isLoading: isUpdating }] = useUpdateMediaMutation();

	// Form state
	const [formData, setFormData] = useState({
		title: "",
		alt: "",
		caption: "",
		courtesy: "",
		keywords: "",
	});

	// Initialize form data when modal opens or media item changes
	useEffect(() => {
		if (isOpen && mediaItem) {
			setFormData({
				title: mediaItem.title || "",
				alt: mediaItem.alt || "",
				caption: mediaItem.caption || "",
				courtesy: mediaItem.courtesy || "",
				keywords: mediaItem.keywords || "",
			});
		}
	}, [isOpen, mediaItem]);

	// Handle form field changes
	const handleInputChange = (field, value) => {
		setFormData((prev) => ({
			...prev,
			[field]: value,
		}));
	};

	// Handle form submission
	const handleSubmit = async (e) => {
		e.preventDefault();

		if (!mediaItem) return;

		try {
			const response = await updateMedia({
				mediaId: mediaItem._id,
				...formData,
			}).unwrap();

			toast.success("Media details updated successfully");

			// Call the onUpdate callback with the updated data and media item
			if (onUpdate) {
				await onUpdate({
					...mediaItem,
					...formData,
				});
			}

			// Close the modal after update is complete
			onClose();
		} catch (error) {
			console.error("Error updating media:", error);
			toast.error(error?.data?.message || "Failed to update media details");
		}
	};

	// Handle modal close
	const handleClose = () => {
		setFormData({
			title: "",
			alt: "",
			caption: "",
			courtesy: "",
			keywords: "",
		});
		onClose();
	};

	if (!isOpen || !mediaItem) return null;

	return (
		<div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[10000]">
			<div className="bg-white rounded-lg w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto">
				{/* Modal Header */}
				<div className="flex items-center justify-between p-6 border-b border-gray-200">
					<h3 className="text-lg font-semibold text-gray-900">Edit Media Details</h3>
					<button
						onClick={handleClose}
						className="text-gray-400 hover:text-gray-600 transition-colors"
						disabled={isUpdating}
					>
						<svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path
								strokeLinecap="round"
								strokeLinejoin="round"
								strokeWidth={2}
								d="M6 18L18 6M6 6l12 12"
							/>
						</svg>
					</button>
				</div>

				{/* Modal Body */}
				<form onSubmit={handleSubmit} className="p-6">
					{/* Media Preview */}
					<div className="mb-6">
						<div className="flex items-start space-x-4">
							<div className="flex-shrink-0">
								<img
									src={mediaItem.url}
									alt={mediaItem.title || "Media preview"}
									className="w-32 h-32 object-cover rounded-lg border border-gray-200"
								/>
							</div>
							<div className="flex-1 min-w-0">
								<p className="text-sm text-gray-500 mb-1">File URL:</p>
								<p className="text-sm text-gray-900 break-all">{mediaItem.url}</p>
							</div>
						</div>
					</div>

					{/* Form Fields */}
					<div className="space-y-4">
						{/* Title */}
						<div>
							<Input
								label="Title"
								type="text"
								value={formData.title}
								onDebouncedChange={(value) => handleInputChange("title", value)}
								placeholder="Enter media title"
								customClass="focus:border-primary"
							/>
						</div>

						{/* Alt Text */}
						<div>
							<Input
								label="Alt Text"
								type="text"
								value={formData.alt}
								onDebouncedChange={(value) => handleInputChange("alt", value)}
								placeholder="Enter alt text for accessibility"
								customClass="focus:border-primary"
							/>
						</div>

						{/* Caption */}
						<div>
							<Input
								label="Caption"
								value={formData.caption}
								onDebouncedChange={(value) => handleInputChange("caption", value)}
								placeholder="Enter media caption"
								isTextarea={true}
								rows={3}
								customClass="focus:border-primary"
							/>
						</div>

						{/* Courtesy */}
						<div>
							<Input
								label="Courtesy"
								type="text"
								value={formData.courtesy}
								onDebouncedChange={(value) => handleInputChange("courtesy", value)}
								placeholder="Enter courtesy/credit information"
								customClass="focus:border-primary"
							/>
						</div>

						{/* Keywords */}
						<div>
							<Input
								label="Keywords"
								type="text"
								value={formData.keywords}
								onDebouncedChange={(value) => handleInputChange("keywords", value)}
								placeholder="Enter keywords (comma separated)"
								customClass="focus:border-primary"
							/>
						</div>
					</div>

					{/* Modal Footer */}
					<div className="flex justify-end space-x-3 mt-6 pt-6 border-t border-gray-200">
						<Button
							type="button"
							variant="secondary"
							size="md"
							rounded="full"
							onClick={handleClose}
							disabled={isUpdating}
						>
							Cancel
						</Button>
						<Button type="submit" variant="primary" size="md" rounded="full" disabled={isUpdating}>
							{isUpdating ? "Updating..." : "Update Media"}
						</Button>
					</div>
				</form>
			</div>
		</div>
	);
};

export default EditMediaModal;
