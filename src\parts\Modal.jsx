import React, { useEffect } from "react";
import { IoClose } from "react-icons/io5";

const Modal = ({ isOpen, toggleModal, title, children, size = "md", showCloseButton = true }) => {
	// Define size classes
	const sizeClasses = {
		sm: "max-w-lg",
		md: "max-w-xl",
		lg: "max-w-2xl",
		xl: "max-w-4xl",
		full: "max-w-full w-full",
	};

	// Prevent body scrolling when modal is open
	useEffect(() => {
		if (isOpen) {
			document.body.style.overflow = "hidden";
		} else {
			document.body.style.overflow = "auto";
		}

		return () => {
			document.body.style.overflow = "auto";
		};
	}, [isOpen]);

	if (!isOpen) return null;

	return (
		<div className="fixed inset-0 z-50 overflow-y-auto">
			{/* Backdrop */}
			<div
				className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
				onClick={toggleModal}
			/>

			{/* Modal */}
			<div className="flex items-center justify-center min-h-screen p-4">
				<div
					className={`relative bg-white rounded-lg shadow-xl ${sizeClasses[size]} w-full mx-auto`}
					onClick={(e) => e.stopPropagation()}
				>
					{/* Header */}
					{title && (
						<div className="px-4 py-3 border-b border-gray-200 flex justify-between items-center">
							<h3 className="text-lg font-semibold text-gray-900">{title}</h3>
							{showCloseButton && (
								<button
									type="button"
									className="rounded-md bg-white text-gray-400 hover:text-gray-500 focus:outline-none"
									onClick={toggleModal}
								>
									<span className="sr-only">Close</span>
									<IoClose className="h-6 w-6" aria-hidden="true" />
								</button>
							)}
						</div>
					)}

					{/* Content */}
					{children}
				</div>
			</div>
		</div>
	);
};

export default Modal;
