import React from "react";

const Panorama = ({ data = [], properties = { spacing: 5 } }) => {
  const { spacing } = properties;

  return (
    <div className="w-full overflow-y-auto">
      <div
        className="flex flex-col items-center"
        style={{ gap: `${spacing}px` }}
      >
        {data.map((item, index) => (
          <div
            key={index}
            className="relative w-full aspect-square flex-shrink-0 overflow-hidden bg-gray-200 shadow"
          >
            <img
              src={item.src}
              alt={item.alt || `Panorama item ${index}`}
              className="absolute inset-0 w-full h-full object-cover object-center"
            />
          </div>
        ))}
      </div>
    </div>
  );
};

export default Panorama;
