import React from "react";
import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { GrDrag } from "react-icons/gr";

export function SortableSlide({ id, index, title }) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`w-20 bg-white rounded-md p-4 flex flex-col items-center shadow-sm border border-[#e5e7eb] ${
        isDragging ? "z-10 shadow-lg" : ""
      }`}
      {...attributes}
    >
      <span className="text-2xl font-bold text-primary mb-2">{index + 1}</span>
      <div {...listeners} className="cursor-grab active:cursor-grabbing">
        <GrDrag size={20} className="text-gray-400" />
      </div>
    </div>
  );
}
