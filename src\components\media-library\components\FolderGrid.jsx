import React from "react";
import { MdFolder } from "react-icons/md";
import { useSelectionManager } from "../hooks/useSelectionManager";
import { useFolderOperations } from "../hooks/useFolderOperations";

/**
 * Component for displaying folders in a grid layout
 */
const FolderGrid = ({ 
	folders = [], 
	onFolderEnter, 
	onFolderSelect,
	showActions = true 
}) => {
	const { isFolderSelected } = useSelectionManager();
	const { handleFolderEnter } = useFolderOperations();
	
	const handleFolderClick = (folder) => {
		if (onFolderSelect) {
			onFolderSelect(folder);
		}
	};
	
	const handleFolderDoubleClick = (folder) => {
		if (onFolderEnter) {
			onFolderEnter(folder);
		} else {
			handleFolderEnter(folder);
		}
	};
	
	if (!folders || folders.length === 0) {
		return (
			<div className="text-center py-8 text-gray-500">
				<MdFolder className="mx-auto text-4xl mb-2 opacity-50" />
				<p>No folders found</p>
			</div>
		);
	}
	
	return (
		<div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4">
			{folders.map((folder) => {
				const isSelected = isFolderSelected(folder._id || folder.id);
				
				return (
					<div
						key={folder._id || folder.id}
						className={`group relative cursor-pointer transition-all duration-200 ${
							isSelected
								? "ring-2 ring-blue-500 ring-offset-2"
								: "hover:ring-1 hover:ring-gray-300 hover:ring-offset-1"
						}`}
						onClick={() => handleFolderClick(folder)}
						onDoubleClick={() => handleFolderDoubleClick(folder)}
					>
						{/* Folder Icon */}
						<div className="flex flex-col items-center p-3 rounded-lg bg-white shadow-sm hover:shadow-md transition-shadow">
							<MdFolder 
								className={`text-4xl mb-2 transition-colors ${
									isSelected 
										? "text-blue-500" 
										: "text-blue-400 group-hover:text-blue-500"
								}`} 
							/>
							
							{/* Folder Name */}
							<span 
								className={`text-sm text-center font-medium truncate w-full ${
									isSelected ? "text-blue-700" : "text-gray-700"
								}`}
								title={folder.name}
							>
								{folder.name}
							</span>
							
							{/* Folder Path (if different from name) */}
							{folder.path && folder.path !== folder.name && (
								<span 
									className="text-xs text-gray-500 truncate w-full text-center mt-1"
									title={folder.path}
								>
									{folder.path}
								</span>
							)}
						</div>
						
						{/* Selection Indicator */}
						{isSelected && (
							<div className="absolute -top-1 -right-1 w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center">
								<svg 
									className="w-3 h-3 text-white" 
									fill="currentColor" 
									viewBox="0 0 20 20"
								>
									<path 
										fillRule="evenodd" 
										d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" 
										clipRule="evenodd" 
									/>
								</svg>
							</div>
						)}
						
						{/* Hover Actions */}
						{showActions && (
							<div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-10 transition-all duration-200 rounded-lg flex items-center justify-center opacity-0 group-hover:opacity-100">
								<div className="flex space-x-2">
									<button
										className="p-2 bg-white rounded-full shadow-md hover:shadow-lg transition-shadow"
										onClick={(e) => {
											e.stopPropagation();
											handleFolderDoubleClick(folder);
										}}
										title="Open folder"
									>
										<svg className="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
											<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
										</svg>
									</button>
								</div>
							</div>
						)}
					</div>
				);
			})}
		</div>
	);
};

export default FolderGrid;
