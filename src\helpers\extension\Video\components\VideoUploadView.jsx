import { Editor, NodeViewWrapper } from "@tiptap/react";
import { useCallback } from "react";

import { VideoUploader } from "./VideoUploader";

export const VideoUploadView = ({ getPos, editor }) => {
  const onUpload = useCallback(
    (url) => {
      if (url) {
        editor
          .chain()
          .setVideoBlock({ src: url })
          .deleteRange({ from: getPos(), to: getPos() })
          .focus()
          .run();
      }
    },
    [getPos, editor]
  );

  return (
    <NodeViewWrapper>
      <div className="p-0 m-0" data-drag-handle>
        <VideoUploader onUpload={onUpload} />
      </div>
    </NodeViewWrapper>
  );
};

export default VideoUploadView;
