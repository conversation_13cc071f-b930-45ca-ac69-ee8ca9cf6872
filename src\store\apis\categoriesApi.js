import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import { baseQuery } from "./baseConfig";
import { apiEndpoints } from "../../utils/constants";

// api endpoints related to the categories
export const categoriesApi = createApi({
  reducerPath: "categoriesApi",
  baseQuery: baseQuery,
  endpoints: (builder) => ({
    getCategories: builder.query({
      query: ({ search, limit, offset, status }) => ({
        url: apiEndpoints.getCategoriesList,
        params: {
          search,
          limit,
          offset,
        },
      }),
      transformResponse: (response) => ({
        count: response.totalCounts,
        data: response.data,
      }),
      // Only have one cache entry because the arg always maps to one string
      serializeQueryArgs: ({ endpointName, queryArgs }) => {
        // Include search in the cache key so different searches have different caches
        return `${endpointName}-${queryArgs.search}`;
      },
      merge: (currentCache, newItems, { arg: { offset } }) => {
        // If offset is 0, it means either:
        // 1. This is the first request
        // 2. Search term has changed (since you'll reset offset to 0)
        // In either case, we want to replace the cache
        if (offset === 0) {
          return newItems;
        }
        // Otherwise, merge the new items with existing cache
        currentCache.data.push(...newItems.data);
        return currentCache;
      },
      forceRefetch({ currentArg, previousArg }) {
        // Refetch if any of the arguments change
        return (
          currentArg.search !== previousArg?.search ||
          currentArg.offset !== previousArg?.offset ||
          currentArg.limit !== previousArg?.limit
        );
      },
    }),
    getAllSingleCategory: builder.query({
      query: ({ search, limit, offset, categories, status }) => ({
        url: apiEndpoints.getSingleCategoriAllList,
        params: {
          filter: { category: categories, search },
          limit,
          offset,
          status,
        },
      }),
      // transformResponse: (response) => ({
      //   count: response.count,
      //   data: response.data,
      // }),
    }),

    updateSubCatArticle: builder.mutation({
      query: ({ id, data }) => ({
        url: `${apiEndpoints.updatedSubCategoryArticle}/${id}`,
        body: data,
        method: "PUT",
      }),
      // transformResponse: (response) => response.data,
    }),

    getSubCategoriesList: builder.query({
      query: ({ search, limit, offset, categories, status }) => ({
        url: apiEndpoints.getSubCategoryList,
        params: {
          category: categories,
          limit,
          search,
          offset,
          status,
        },
      }),
      serializeQueryArgs: ({ endpointName }) => {
        return endpointName;
      },
      // Always merge incoming data to the cache entry
      merge: (currentCache, newItems) => {
        currentCache.data.push(...newItems.data);
      },
      // Refetch when the page arg changes
      forceRefetch({ currentArg, previousArg }) {
        return currentArg !== previousArg;
      },
      // transformResponse: (response) => ({
      //   count: response.count,
      //   data: response.data,
      // }),
    }),
  }),
});

// Export hooks for usage in functional components, which are
// auto-generated based on the defined endpoints
export const {
  useGetCategoriesQuery,
  useGetAllSingleCategoryQuery,
  useGetSubCategoriesListQuery,
  useUpdateSubCatArticleMutation,
} = categoriesApi;
