import { useEditorState } from "@tiptap/react";
import React, { useEffect, useState, useMemo, useCallback } from "react";
import { IoCloseOutline } from "react-icons/io5";
import Button from "../../parts/Button";

const LinkEditor = ({ editor, setShowLink }) => {
  const { link, target, rel } = useEditorState({
    editor,
    selector: (ctx) => {
      const attrs = ctx.editor.getAttributes("link");
      return { link: attrs.href, target: attrs.target, rel: attrs.rel };
    },
  });

  if (!editor) {
    return null;
  }

  const relArray = rel ? rel.split(", ") : [];
  const initialNoReferrer = relArray.includes("noreferrer");
  const initialNoFollow = relArray.includes("nofollow");
  const initialSponsored = relArray.includes("sponsored");

  const [anchorLink, setAnchorLink] = useState({
    noreferrer: true,
    nofollow: true,
    sponsored: true,
    openInNewTab: true,
    url: link || "",
  });

  useEffect(() => {
    setAnchorLink({
      noreferrer: initialNoReferrer, // Use the actual value
      nofollow: initialNoFollow, // Use the actual value
      sponsored: initialSponsored, // Use the actual value
      openInNewTab: !!target, // Convert target to boolean
      url: link || "", // Fallback to empty string if link is undefined
    });
  }, [initialNoReferrer, initialSponsored, initialNoFollow, link, target]);

  const isValidUrl = useMemo(
    () => /^(\S+):(\/\/)?\S+$/.test(anchorLink.url),
    [anchorLink.url]
  );

  const onSetLink = useCallback(
    (href, target, noreferrer, nofollow, sponsored) => {
      let rel = [];
      if (noreferrer) rel.push("noreferrer");
      if (nofollow) rel.push("nofollow");
      if (sponsored) rel.push("sponsored");

      editor
        .chain()
        .focus()
        .extendMarkRange("link")
        .setLink({
          href: href,
          target: target ? "_blank" : "",
          rel: rel.length > 0 ? rel.join(", ") : "",
        })
        .run();
      setShowLink(false);
    },
    [editor, setShowLink]
  );

  const handleChange = ({ name, value }) => {
    setAnchorLink({ ...anchorLink, [name]: value });
  };

  const handleSubmit = useCallback(
    (e) => {
      e.preventDefault();
      if (isValidUrl) {
        onSetLink(
          anchorLink.url,
          anchorLink.openInNewTab,
          anchorLink.noreferrer,
          anchorLink.nofollow,
          anchorLink.sponsored
        );
      }
    },
    [anchorLink, onSetLink]
  );

  return (
    <div className="w-96">
      <div className="link-menu">
        <div className="link-menu-head">
          <div className="link-menu-title">What do you want to link to?</div>
          <button
            className="btn-menulink-close"
            onClick={() => setShowLink(false)}
          >
            <IoCloseOutline />
          </button>
        </div>
        <div className="link-menu-main">
          <div className="input-list">
            <label>Link</label>
            <input
              type="text"
              className="border text-sm border-gray-300 rounded-md px-4 py-2 w-full focus:outline-none focus:border-blue-500"
              placeholder="Enter or paste a link"
              value={anchorLink.url ?? ""}
              onChange={(e) =>
                handleChange({ name: "url", value: e.target.value })
              }
            />
          </div>
          <div className=" flex flex-col gap-y-4">
            <label className="flex items-center gap-2">
              <input
                type="checkbox"
                name="type"
                className="w-4 h-4"
                value={anchorLink.openInNewTab}
                checked={anchorLink.openInNewTab}
                onChange={(e) =>
                  handleChange({
                    name: "openInNewTab",
                    value: e.target.checked,
                  })
                }
              />
              Link opens in a new tab
            </label>
            <label className="flex items-center gap-2">
              <input
                type="checkbox"
                name="type"
                className="w-4 h-4"
                value="_blank"
                checked={anchorLink.noreferrer}
                onChange={(e) =>
                  handleChange({ name: "noreferrer", value: e.target.checked })
                }
              />
              noreferrer (recommended)
            </label>
            <label className="flex items-center gap-2">
              <input
                type="checkbox"
                name="type"
                className="w-4 h-4"
                value="_blank"
                checked={anchorLink.nofollow}
                onChange={(e) =>
                  handleChange({ name: "nofollow", value: e.target.checked })
                }
              />
              nofollow
            </label>
            <label className="flex items-center gap-2">
              <input
                type="checkbox"
                name="type"
                className="w-4 h-4"
                value="_blank"
                checked={anchorLink.sponsored}
                onChange={(e) =>
                  handleChange({ name: "sponsored", value: e.target.checked })
                }
              />
              sponsored
            </label>
          </div>
        </div>
        <div className="link-menu-footer">
          <Button
            rounded="full"
            variant="secondary"
            customClasses="py-[7px]"
            onClick={() => setShowLink(false)}
          >
            Cancel
          </Button>
          <Button
            customClasses={"disabled:bg-gray-300"}
            rounded="full"
            disabled={!isValidUrl}
            onClick={handleSubmit}
          >
            Save
          </Button>
        </div>
      </div>
    </div>
  );
};

export default LinkEditor;
