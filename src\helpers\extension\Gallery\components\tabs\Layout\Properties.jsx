import React from "react";

const Properties = ({ editor, properties, layout }) => {
  const propertiesData = {
    grid: [
      {
        label: "Thumbnail resize",
        description:
          "<PERSON><PERSON> <PERSON><PERSON> to select what your visitors see, or choose <PERSON><PERSON> to use the whole photo as the thumbnail.",
        type: "button",
        fields: [
          {
            icon: "",
            tile: "Crop",
            value: "cover",
          },
          {
            icon: "",
            tile: "Fit",
            value: "contain",
          },
        ],
      },
      {
        label: "Image ratio",
        description: "",
        type: "thumbnail",
        fields: [
          {
            icon: "",
            tile: "16:9",
            value: "16/9",
          },
          {
            icon: "",
            tile: "4:3",
            value: "4/3",
          },
          {
            icon: "",
            tile: "1:1",
            value: "1/1",
          },
          {
            icon: "",
            tile: "3:4",
            value: "3/4",
          },
          {
            icon: "",
            tile: "9:16",
            value: "9/16",
          },
        ],
      },
      {
        label: "Images per row",
        description: "",
        type: "slider",
        max: 5,
      },
    ],
  };
  return (
    <div className="px-4 pt-3 pb-6">
      <h2 className="font-bold text-base">Properties</h2>
      <div className="flex flex-col gap-y-2 pt-5">
        <div className="text-sm">Caption</div>
      </div>
    </div>
  );
};

export default Properties;
