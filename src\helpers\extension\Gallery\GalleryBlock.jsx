import { ReactNode<PERSON>iew<PERSON><PERSON>er } from "@tiptap/react";
import { mergeAttributes, Node } from "@tiptap/core";
import GalleryView from "./components/GalleryView";

export const GalleryBlock = Node.create({
  name: "galleryBlock",
  group: "block",
  draggable: true,
  isolating: true,
  selectable: true,

  addAttributes() {
    return {
      data: {
        default: [],
        parseHTML: (element) => {
          try {
            const json = element.getAttribute("data-gallery-items");
            return json ? JSON.parse(json) : [];
          } catch {
            return [];
          }
        },
        renderHTML: (attributes) => ({
          "data-gallery-items": JSON.stringify(attributes.data || []),
        }),
      },
      layout: {
        default: "grid",
        parseHTML: (element) => element.getAttribute("data-layout"),
        renderHTML: (attributes) => ({
          "data-layout": attributes.layout,
        }),
      },
      properties: {
        default: {
          grid: {
            resize: "cover",
            ratio: "1/1",
            column: 3,
          },
          masonry: {
            orientation: "horizontal",
            height: 300,
            spacing: 5,
          },
          collage: {
            direction: "vertical",
            orientation: "vertical",
            height: 300,
            spacing: 5,
          },
          thumbnails: {
            direction: "bottom",
            spacing: 5,
          },
          panorama: {
            spacing: 5,
          },
          columns: {
            spacing: 5,
          },
          sliders: {
            spacing: 5,
            resize: "crop",
            ratio: "16/9",
          },
        },
        parseHTML: (element) => element.getAttribute("data-properties"),
        renderHTML: (attributes) => ({
          "data-properties": attributes.properties,
        }),
      },
      width: {
        default: "100%",
        parseHTML: (element) => element.getAttribute("data-width"),
        renderHTML: (attributes) => ({
          "data-width": attributes.width,
        }),
      },
      alignment: {
        default: "center",
        parseHTML: (element) => element.getAttribute("data-alignment"),
        renderHTML: (attributes) => ({
          "data-alignment": attributes.alignment,
        }),
      },
    };
  },

  parseHTML() {
    return [
      {
        tag: "div[data-gallery-items]",
      },
    ];
  },

  renderHTML({ HTMLAttributes }) {
    return [
      "div",
      mergeAttributes(this.options.HTMLAttributes, HTMLAttributes),
    ];
  },

  addCommands() {
    return {
      setGalleryBlock:
        (attrs) =>
        ({ commands }) =>
          commands.insertContent({
            type: this.name,
            attrs,
          }),

      setGalleryBlockItems:
        (items) =>
        ({ commands }) =>
          commands.updateAttributes(this.name, { data: items }),

      setGalleryBlockLayout:
        (layout) =>
        ({ commands }) =>
          commands.updateAttributes(this.name, { layout }),

      setGalleryBlockProperties:
        (properties) =>
        ({ commands }) =>
          commands.updateAttributes(this.name, { properties }),

      setGalleryBlockSize:
        (width) =>
        ({ commands }) =>
          commands.updateAttributes(this.name, {
            width:
              width === 0 ? "100vw" : `${Math.max(0, Math.min(100, width))}%`,
          }),

      setGalleryBlockAlignment:
        (alignment) =>
        ({ commands }) =>
          commands.updateAttributes(this.name, { alignment }),
    };
  },

  addNodeView() {
    return ReactNodeViewRenderer(GalleryView);
  },
});

export default GalleryBlock;
