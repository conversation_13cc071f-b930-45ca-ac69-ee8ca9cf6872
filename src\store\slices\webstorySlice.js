import { createSlice } from "@reduxjs/toolkit";
import {
	filterStoryStatusState,
	incrementOffsetState,
	resetFiltersState,
	setCanonicalState,
	setFetchedDataState,
	setHighlightedSubCategoryState,
	setImageState,
	setIndexingState,
	setIntialFiltersStateData,
	setKeyWordsState,
	setMetaDataState,
	setOgContentState,
	setSubCategoryState,
	setXContentState,
	storiesInputFilterState,
	toggleFlagState,
} from "./sharedReducers";
import { generateSlugStories } from "../../utils/helperFunctions";

const initialState = {
	_id: null,
	data: [],
	errors: {},
	filter: {
		status: null,
		search: "",
	},
	limit: 20,
	offset: 0,
	hasMore: true,
	editCoverImg: false,
	croppedImg: [],
	storiesState: {
		title: "",
		excerpt: "",
		publishDate: null,
		subcategory: "",
		contributors: [],
		section: [],
		writer: [],
		meta: {
			title: null,
			description: null,
			keywords: [],
			primaryKeywords: [],
			slug: null,
			author: null,
			canonical: null,
			robots: "index,follow",
			_id: null,
		},
		ogImg: [],
		twitterImg: [],
		og: {
			title: null,
			description: null,
		},
		twitter: {
			title: null,
			description: null,
			card: "large",
		},
	},
	status: 3,
	coverImg: [],
};

export const webStory = createSlice({
	name: "webStory",
	initialState,
	reducers: {
		setCoverImgTransformData: (state, { payload }) => {
			state.storiesState.coverImgFocalPosition = payload;
		},
		setEditCoverImg: (state, { payload }) => {
			state.editCoverImg = payload;
		},
		setWebStoryError: (state, { payload }) => {
			state.errors.subcategory = null;
		},
		setErrors: (state, action) => {
			state.errors = action.payload; // Set validation errors
		},
		clearErrors: (state) => {
			state.errors = {};
		},

		webStoryFilterStoryStatus: (state, { payload }) => filterStoryStatusState(state, payload),

		webStoriesInputFilter: (state, { payload }) => storiesInputFilterState(state, payload),
		incrementOffset: (state) => incrementOffsetState(state),
		resetFilters: (state, { payload }) => resetFiltersState(state, payload),
		// for handling the tag toggle in the filter
		setWebStorySubCategory: (state, { payload }) => setSubCategoryState(state, payload),
		setHighlightedWebStorySubCategory: (state, { payload }) =>
			setHighlightedSubCategoryState(state, payload),
		setFetchedData: (state, { payload }) => setFetchedDataState(state, payload),

		// set meta data here
		setWebStoryMetaData: (state, { payload }) => setMetaDataState(state, payload),

		// set keywords here
		setWebStoryKeyWords: (state, { payload }) => setKeyWordsState(state, payload),

		// set the canonical here
		setWebStoryCanonical: (state, { payload }) => setCanonicalState(state, payload),

		// set indexing
		setWebStoryIndexing: (state, { payload }) => setIndexingState(state, payload),
		//set twitter and og image
		setWebStoryImage: (state, { payload }) => setImageState(state, payload),

		// set og content
		setWebStoryOgContent: (state, { payload }) => setOgContentState(state, payload),
		// set x content
		setWebStoryXContent: (state, { payload }) => setXContentState(state, payload),

		//set toggle state
		toggleWebStoryFlag: (state, { payload }) => toggleFlagState(state, payload),

		//set excerpt here
		setInputData: (state, { payload }) => {
			const { name, value } = payload;
			state.storiesState[name] = value;
		},
		// update thumbnail here
		updateThumbnail: (state, action) => {
			state.coverImg = action.payload;
			state.storiesState.ogImg = [action.payload];
			state.storiesState.twitterImg = [action.payload];
		},

		setCoverImg: (state, { payload }) => {
			state.coverImg = payload;
		},

		//set youtube webStory here
		// setYoutubeVideo: (state, action) => {
		//   const { youtubeUrl, thumbnailUrl } = action.payload;
		//   state.src = youtubeUrl;
		//   state.youtubeUrl = youtubeUrl;
		//   state.isYoutube = true;
		//   state.storiesState.ogImg = thumbnailUrl;
		//   state.storiesState.twitterImg = thumbnailUrl;
		//   state.coverImg = thumbnailUrl;
		//   state.isCustomThumbnail = false;
		// },

		// resetYoutubeVideoData: (state, { payload }) => {
		//   state.src = null;
		//   state.isYoutube = false;
		//   state.youtubeUrl = null;
		//   state.storiesState.ogImg = [];
		//   state.storiesState.twitterImg = [];
		//   state.coverImg = null;
		//   state.isCustomThumbnail = false;
		//   state.storiesState.twitterImg = [];
		//   state.storiesState.ogImg = [];
		// },

		// resetLocalVideoData: (state, { payload }) => {
		//   state.src = null;
		//   state.isYoutube = false;
		//   state.coverImg = null;
		//   state.youtubeUrl = null;
		//   state.isCustomThumbnail = false;
		//   state.storiesState.twitterImg = [];
		//   state.storiesState.ogImg = [];
		// },

		// //set local webStory here
		// setLocalVideo: (state, action) => {
		//   const { videoFile, thumbnailFile } = action.payload;
		//   console.log(thumbnailFile, " thumbnailFilef forthe local webStory");
		//   state.src = videoFile;
		//   state.isYoutube = false;
		//   state.coverImg = thumbnailFile;
		//   state.isCustomThumbnail = false;
		//   state.storiesState.ogImg = [thumbnailFile];
		//   state.storiesState.twitterImg = [thumbnailFile];
		// },

		// setYoutubeStatus: (state, { payload }) => {
		//   state.isYoutube = payload;
		// },

		// // update thumbnail here
		// updateThumbnail: (state, action) => {
		//   state.coverImg = action.payload;
		//   console.log(action.payload, " thumbnail");
		//   state.storiesState.ogImg = [action.payload];
		//   state.storiesState.twitterImg = [action.payload];
		//   state.isCustomThumbnail = true;
		// },

		// reset webStory upload
		resetWebStoryUpload: (state) => {
			return initialState;
		},
		setInititalFilterData: (state, { payload }) => setIntialFiltersStateData(state, payload),

		setwebStoriesData: (state, { payload }) => {
			function normalizePayload(payload) {
				// Check if the payload is an array
				if (Array.isArray(payload)) {
					return payload[0]; // Already an array, return as is
				}

				// If it's an object, wrap it in an array
				if (typeof payload === "object" && payload !== null) {
					return payload;
				}

				// If it's neither, return an empty array or handle the error as needed
				return {};
			}

			const finalData = normalizePayload(payload);
			const {
				coverImg,
				status,
				type,
				writer,
				contributor,
				meta,
				section,
				title,
				slug,
				author,
				category,
				subcategory,
				webStorySlides,
				publishDate,
			} = finalData;

			// Assign root-level properties
			state.coverImg = coverImg;
			state.status = status;
			state.croppedImg = payload?.croppedImg ? payload.croppedImg : [];

			// Update `storiesState` properties
			state.storiesState = {
				...state.storiesState,
				title,
				subcategory,
				writer: writer || [],
				contributors: contributor || [],
				section: section || [],
				webStorySlides: webStorySlides || [],
				publishDate: publishDate || [],
				meta: {
					...state.storiesState.meta,
					title: meta?.title || state.storiesState.meta.title,
					description: meta?.description || state.storiesState.meta.description,
					keywords: meta?.keywords || state.storiesState.meta.keywords,
					primaryKeywords: meta?.primaryKeywords || state.storiesState.meta.primaryKeywords,
					slug: generateSlugStories(slug) || state.storiesState.meta.slug,
					canonical: meta?.canonical || state.storiesState.meta.canonical,
					robots: meta?.robots || state.storiesState.meta.robots,
				},
				og: {
					...state.storiesState.og,
					title: meta?.og?.title,
					description: meta?.og?.description,
				},
				twitter: {
					...state.storiesState.twitter,
					title: meta?.twitter?.title,
					description: meta?.twitter?.description,
					card: meta?.twitter?.card === "summary_large_image" ? "large" : "small",
				},
				ogImg: meta?.og?.image || state.storiesState.ogImg,
				twitterImg: meta?.twitter?.image || state.storiesState.twitterImg,
			};
		},
		resetState: () => initialState,
		setStatus: (state, { payload }) => {
			state.status = payload;
		},
		setCroppedImage: (state, { payload }) => {
			state.croppedImg = payload;
		},
		setScheduleTime: (state, { payload }) => {
			state.status = payload.status;
			state.storiesState.publishDate = payload.publishDate;
		},
		setWebStorySlides: (state, { payload }) => {
			state.storiesState.webStorySlides = payload;
		},
		resetRestState: (state) => {
			return {
				filter: state.filter,
				limit: state.limit,
				offset: state.offset,
				status: state.status,
				...initialState,
			};
		},
		setWebPublishedData: (state, { payload }) => {
			state.storiesState.publishDate = payload;
		},
	},
});
// Action creators are generated for each case reducer function
export const {
	setWebPublishedData,
	setWebStoryError,
	clearErrors,
	incrementOffset,
	resetFilters,
	resetState,
	resetWebStoryUpload,
	setErrors,
	setFetchedData,
	setHighlightedWebStorySubCategory,
	setInititalFilterData,
	setInputData,
	setStatus,
	setWebStoryCanonical,
	setWebStoryImage,
	setWebStoryIndexing,
	setWebStoryKeyWords,
	setWebStoryMetaData,
	setWebStoryOgContent,
	setWebStorySubCategory,
	setWebStoryXContent,
	setwebStoriesData,
	toggleWebStoryFlag,
	webStoriesInputFilter,
	webStoryFilterStoryStatus,
	updateThumbnail,
	setCoverImg,
	setEditCoverImg,
	setCoverImgTransformData,
	setCroppedImage,
	setScheduleTime,
	setWebStorySlides,
	resetRestState,
} = webStory.actions;

export default webStory.reducer;
