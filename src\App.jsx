import { createBrowser<PERSON><PERSON>er, Navigate, RouterProvider } from "react-router-dom";
import ErrorPage from "./error-page";
import Login from "./routes/Login";
import Stories from "./routes/Stories";
import Dashboard from "./routes/Dashboard";
import Home from "./routes/Home";
import Categories from "./routes/Categories";
import UnderDevelopment from "./routes/UnderDevelopment";
import Signin from "./routes/Signin";
import { ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { useSelector } from "react-redux";
import TextStories from "./routes/TextStories";
import VideoStories from "./routes/VideoStories";
import AddEditVideoStory from "./routes/AddEditVideoStory";
import SubCategory from "./routes/SubCategory";
import Authors from "./routes/Authors";
import AddEditAuthor from "./routes/AddEditAuthor";
import TagsAddEdit from "./routes/TagsAddEdit";
import Tags from "./routes/Tags";
import FlaggedStories from "./routes/FlaggedStories";
import FlaggedSubStory from "./routes/FlaggedSubStory";
import AddEditWebStory from "./routes/AddEditWebStory";
import WebStories from "./routes/Webstories";
import TrashStories from "./routes/TrashStories";
import EmailContacts from "./routes/EmailContacts";
import EmailGroups from "./routes/EmailGroups";
import EmailCampaign from "./routes/EmailCampaign";
import EmailAnalytics from "./routes/EmailAnalytics";
import AddEditEmailContact from "./routes/AddEditEmailContact";
import MediaLibrary from "./components/media-library/MediaLibrary";
import Settings from "./routes/Settings";

function App() {
	const { user } = useSelector((state) => state.user);
	const { isOpen } = useSelector((state) => state.mediaLibrary);
	const isLoggedIn = user ? true : false;
	const router = createBrowserRouter([
		{
			path: "/",
			element: isLoggedIn ? <Navigate to="/admin/home" replace /> : <Signin />,
			errorElement: <ErrorPage />,
		},
		{
			path: "/signin",
			element: <Signin />,
		},
		{
			path: "/admin",
			element: isLoggedIn ? <Dashboard /> : <Navigate to="/" replace />,
			children: [
				{
					path: "under-development",
					element: <UnderDevelopment />,
				},
				{ path: "home", element: <Home />, index: true },
				{
					path: "stories/create",
					element: <Stories method="POST" />,
				},
				{
					path: "stories/edit/:storyId",
					element: <Stories method="PUT" />,
				},
				{
					path: "text-stories",
					element: <TextStories />,
				},
				{
					path: "trash-stories",
					element: <TrashStories />,
				},
				{
					path: "video-stories",
					element: <VideoStories />,
				},
				{
					path: "shorts",
					element: <VideoStories isShorts={true} />,
				},
				{
					path: "add-video-story",
					element: <AddEditVideoStory method="POST" />,
				},
				{
					path: "add-shorts-story",
					element: <AddEditVideoStory method="POST" isShorts={true} />,
				},
				{
					path: "edit-video-story/:id",
					element: <AddEditVideoStory method="PUT" />,
				},
				{
					path: "edit-shorts-story/:id",
					element: <AddEditVideoStory method="PUT" isShorts={true} />,
				},
				{
					path: "categories",
					element: <Categories />,
				},
				{
					path: "subcategory/:name",
					element: <SubCategory />,
				},
				{
					path: "authors",
					element: <Authors />,
				},
				{
					path: "authors/create",
					element: <AddEditAuthor method={"POST"} />,
				},
				{
					path: "authors/edit/:id",
					element: <AddEditAuthor method={"PUT"} />,
				},
				{
					path: "tags",
					element: <Tags />,
				},
				{
					path: "tags/create",
					element: <TagsAddEdit method={"POST"} />,
				},
				{
					path: "tags/edit/:id",
					element: <TagsAddEdit method={"PUT"} />,
				},
				{
					path: "flagged-stories",
					element: <FlaggedStories />,
				},
				{
					path: "flagged-sub-stories/:name",
					element: <FlaggedSubStory />,
				},
				{
					path: "web-stories/create",
					element: <AddEditWebStory method={"POST"} />,
				},
				{
					path: "edit-web-story/:id",
					element: <AddEditWebStory method={"PUT"} />,
				},
				{
					path: "web-stories",
					element: <WebStories />,
				},
				{
					path: "email/contacts",
					element: <EmailContacts />,
				},
				{
					path: "email/contacts/create",
					element: <AddEditEmailContact />,
				},
				{
					path: "email/contacts/edit/:id",
					element: <AddEditEmailContact />,
				},
				{
					path: "email/groups",
					element: <EmailGroups />,
				},
				{
					path: "email/campaign",
					element: <EmailCampaign />,
				},
				{
					path: "email/analytics",
					element: <EmailAnalytics />,
				},
				{
					path: "settings",
					element: <Settings />,
				},
			],
		},
	]);
	return (
		<>
			<RouterProvider router={router} />
			{isOpen && <MediaLibrary />}
			<ToastContainer />
		</>
	);
}

export default App;
