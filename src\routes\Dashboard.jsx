import React from "react";
import { BiHome } from "react-icons/bi";
import {
  Link,
  Outlet,
  useLocation,
  useNavigate,
  useParams,
  useRoutes,
  useSearchParams,
} from "react-router-dom";
import Sidebar from "../parts/Sidebar";
import ConfirmationModal from "../parts/ConfirmationModal";
import useConfirmationModal from "../utils/useConfirmationModal";
import { useDispatch } from "react-redux";
import { logout, userSlice } from "../store/slices/userSlice";
import { toast } from "react-toastify";
import { storiesApi } from "../store/apis/storiesApi";
import { videoStoryApi } from "../store/apis/videoStoryApi";
import { webStoryApi } from "../store/apis/webStoryApi";
import { usersApi } from "../store/apis/userApi";
import { categoriesApi } from "../store/apis/categoriesApi";
import { authorsApi } from "../store/apis/authorsApi";
import { tagsApi } from "../store/apis/tagsApi";
import { flaggedStoriesApi } from "../store/apis/flaggedStoriesApi";
import { store } from "../store/store";
import { resetState } from "../store/slices/storiesSlice";
import { resetState as authorResetState } from "../store/slices/authorsSlice";
import { resetCategoryState } from "../store/slices/categoriesSlice";
import { resetEditorState } from "../store/slices/editorSlice";
import { resetFlaggedStorieState } from "../store/slices/flaggedStoriesSlice";
import { resetTableState } from "../store/slices/tableSlice";
import { resetState as resetTagState } from "../store/slices/tagsSlice";
import { resetState as resetVideoStoryState } from "../store/slices/videoStorySlice";
import { resetState as resetWebStoryState } from "../store/slices/webstorySlice";

const Dashboard = () => {
  const [params] = useSearchParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { isModalOpen, rowIdToDelete, openModal, closeModal } =
    useConfirmationModal();

  const openModalFunction = () => openModal();

  const handleDelete = () => {
    dispatch(logout());
    dispatch(authorResetState());
    dispatch(resetCategoryState());
    dispatch(resetEditorState());
    dispatch(resetFlaggedStorieState());
    dispatch(resetTableState());
    dispatch(resetTagState());
    dispatch(resetVideoStoryState());
    dispatch(resetWebStoryState());
    dispatch(storiesApi.util.resetApiState());
    dispatch(videoStoryApi.util.resetApiState());
    dispatch(webStoryApi.util.resetApiState());
    dispatch(usersApi.util.resetApiState());
    dispatch(categoriesApi.util.resetApiState());
    dispatch(authorsApi.util.resetApiState());
    dispatch(tagsApi.util.resetApiState());
    dispatch(flaggedStoriesApi.util.resetApiState());
    navigate("/signin");
    toast.success("Logged out successfully!");
  };
  return (
    <div>
      <div className="flex flex-col lg:flex-row relative min-h-screen">
        {params.get("location") ? null : (
          <Sidebar openModalFunction={openModalFunction} />
        )}
        <div className="w-full overflow-y-auto scrollbar bg-[#eceff3] ">
          <Outlet />
          <ConfirmationModal
            isOpen={isModalOpen}
            isLoading={false}
            confirmLogout={true}
            toggleModal={closeModal}
            message="Are you sure you want to logout?"
            onConfirm={handleDelete}
          />
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
