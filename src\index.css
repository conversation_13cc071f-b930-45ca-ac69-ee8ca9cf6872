@tailwind base;
@tailwind components;
@tailwind utilities;

@font-face {
	font-family: "public_sans_regular";
	src: url("/fonts/PublicSans-Regular.ttf");
}

@font-face {
	font-family: "public_sans_semibold";
	src: url("/fonts/PublicSans-SemiBold.ttf");
}
@font-face {
	font-family: "public_sans_bold";
	src: url("/fonts/PublicSans-Bold.ttf");
}

:root {
	font-family: "public_sans_regular";
	/* font-size: 14px; */
	color: "#000624";
}

/* h1{
  font-size: 28px;
  font-weight: 700;
} */

/* h3{
  font-size: 16px;
  font-weight: 700;
  line-height: 24px;
} */

.gradient {
	background: rgb(255, 170, 170);
	background: linear-gradient(
		144deg,
		rgba(255, 170, 170, 1) 0%,
		rgba(254, 254, 254, 1) 48%,
		rgba(244, 204, 255, 1) 100%
	);
}

.calculateHeight {
	min-height: auto;
	max-height: calc(100vh - 148px);
}
.table-container {
	max-height: 600px; /* Adjust height as needed */
	overflow-y: auto;
	border: 1px solid #e1e1e1;
}

.fixed-header {
	position: sticky;
	top: 0;
	background-color: #c3e5ff;
	z-index: 1;
}

.scrollable-rows {
	overflow-y: scroll;
}

/* Hide scrollbar for Chrome, Safari, and Opera */
.scrollbar::-webkit-scrollbar {
	display: none;
}

/* Hide scrollbar for IE, Edge, and Firefox */
.scrollbar {
	-ms-overflow-style: none; /* IE and Edge */
	scrollbar-width: none; /* Firefox */
}

.homeGradient {
	background: linear-gradient(
		250.38deg,
		rgb(230, 244, 255) 2.39%,
		rgb(105, 177, 255) 34.42%,
		rgb(22, 119, 255) 60.95%,
		rgb(9, 88, 217) 84.83%,
		rgb(0, 44, 140) 104.37%
	);
}

.calculateHeight2 {
	height: calc(100vh - 4rem);
}

td {
	padding: 10px 5px;
}

.table tbody tr td:nth-child(2) {
	max-width: 25rem;
}

.table tbody tr td:nth-child(4) {
	max-width: 9rem;
}
.ql-toolbar.ql-snow,
.ql-container.ql-snow {
	border: none !important;
}

.categories tbody tr td:nth-child(2) {
	display: flex;
	align-items: center;
	height: 100%;
}

.categories tbody tr td:nth-child(3):hover {
	color: #bbbfc0 !important;
}

.categories thead {
	top: 3.25rem !important;
}

.table thead {
	top: 4.2rem !important;
}
.webstory_description .ql-toolbar.ql-snow .ql-formats {
	margin-right: 7px;
}

.imagedrop img {
	height: 100%;
	object-fit: cover;
}

.advanced-cropper-background-image {
	border-radius: 8px !important;
}

/* For Microsoft Edge */
input[type="password"]::-ms-reveal {
	display: none;
}

/* For Chromium-based Edge and other Chromium browsers */
input[type="password"]::-webkit-credentials-auto-fill-button {
	visibility: hidden;
	display: none !important;
	pointer-events: none;
	position: absolute;
	right: 0;
}

.fileInfoAccordion button span {
	font-size: 13px !important;
}

#seoDetail > div {
	padding-left: 0 !important;
	padding-right: 0 !important;
}
