import React, { useState } from "react";
import ReactQuill from "react-quill";
import "react-quill/dist/quill.snow.css";
import { RxCross2 } from "react-icons/rx";
import Button from "../../parts/Button";
import { Input } from "../../parts/FormComponents";
import ImageDrop from "../stories/ImageDrop";

const Modal = ({
  isOpen,
  onClose,
  slideData,
  onUpdateSlideData,
  handleSaveData,
}) => {
  const [errors, setErrors] = useState({});
  const [newContributor, setNewContributor] = useState("");

  if (!isOpen) return null;

  const handleDataChange = (name, value) => {
    onUpdateSlideData((prev) => ({
      ...prev,
      [name]: value,
    }));

    // Clear errors for the field being updated
    if (errors[name]) {
      setErrors((prevErrors) => {
        const updatedErrors = { ...prevErrors };
        delete updatedErrors[name];
        return updatedErrors;
      });
    }
  };

  const handleContributorAdd = (newContributor) => {
    if (newContributor.trim()) {
      onUpdateSlideData((prev) => ({
        ...prev,
        contributor: [...prev.contributor, newContributor.trim()],
      }));
      setNewContributor("");
    }
  };

  const handleRemoveContributor = (contributorToRemove) => {
    onUpdateSlideData((prev) => ({
      ...prev,
      contributor: prev.contributor.filter((c) => c !== contributorToRemove),
    }));
  };

  const validateStory = () => {
    const newErrors = {};
    if (!slideData.title?.trim()) newErrors.title = "Title is required";
    if (!slideData.altName?.trim()) newErrors.altName = "Alt Name is required";
    if (!slideData.content?.trim()) newErrors.content = "Content is required";
    if (!slideData.coverImg) newErrors.coverImg = "Cover image is required";
    return newErrors;
  };

  const handleSave = () => {
    const validationErrors = validateStory();
    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
    } else {
      handleSaveData();
      setErrors({}); // Clear errors after successful save
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg w-[60%] p-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold">Edit Slide</h2>
          <button onClick={onClose} className="text-gray-500">
            <RxCross2 size={24} />
          </button>
        </div>

        <div className="flex gap-x-3 w-full">
          <div className="w-[30%] pl-5 py-5 imagedrop">
            <ImageDrop
              selectedFiles={slideData.coverImg}
              setSelectedFiles={(file) => handleDataChange("coverImg", file)}
              label={false}
              customHeight="370px"
              customHeight1="370px"
              customClasses="relative !h-[370px]"
            />
            {errors.coverImg && (
              <p className="text-red-500 text-sm mt-1">{errors.coverImg}</p>
            )}
          </div>

          <div className="px-5 py-4 flex flex-col gap-y-4 w-[60%]">
            <div>
              <Input
                label="Slide Title"
                value={slideData.title}
                onDebouncedChange={(value) => handleDataChange("title", value)}
                placeholder="Add a slide title"
              />
              {errors.title && (
                <p className="text-red-500 text-sm mt-1">{errors.title}</p>
              )}
            </div>
            <div>
              <Input
                label="Alt Name"
                value={slideData.altName}
                onDebouncedChange={(value) =>
                  handleDataChange("altName", value)
                }
                placeholder="Add alt text"
              />
              {errors.altName && (
                <p className="text-red-500 text-sm mt-1">{errors.altName}</p>
              )}
            </div>
            <div className="flex flex-col">
              <Input
                label="Photo Credit"
                value={newContributor}
                onDebouncedChange={setNewContributor}
                onEnter={(value) => handleContributorAdd(value)}
                placeholder="Add contributors"
              />
              <div className="flex flex-wrap gap-2 mt-2">
                {slideData.contributor.map((contributor, index) => (
                  <span
                    key={index}
                    className="flex items-center gap-2 px-3 py-1 rounded-full bg-primary text-white"
                    onClick={() => handleRemoveContributor(contributor)}
                  >
                    {contributor}
                    <RxCross2 />
                  </span>
                ))}
              </div>
            </div>

            <div className="webstory_description">
              <label className="text-sm text-fadeGray mb-4">Description</label>
              <ReactQuill
                theme="snow"
                className="!rounded-md border border-[#d1d5db] outline-none"
                value={slideData.content}
                onChange={(value) => handleDataChange("content", value)}
              />
              {errors.content && (
                <p className="text-red-500 text-sm mt-1">{errors.content}</p>
              )}
            </div>
          </div>
        </div>

        <div className="flex justify-end mt-4">
          <Button onClick={onClose} rounded="full" customClasses="mr-4">
            Cancel
          </Button>
          <Button
            onClick={handleSave}
            rounded="full"
            customClasses="bg-primary"
          >
            Save
          </Button>
        </div>
      </div>
    </div>
  );
};

export default Modal;
