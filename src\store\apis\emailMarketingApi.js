import { createApi } from "@reduxjs/toolkit/query/react";
import { baseQuery } from "./baseConfig";

// Define API endpoints for email marketing
export const emailMarketingApi = createApi({
  reducerPath: "emailMarketingApi",
  baseQuery: baseQuery,
  tagTypes: [
    "EmailContacts",
    "EmailGroups",
    "EmailTemplates",
    "EmailCampaigns",
    "EmailAnalytics",
  ],
  endpoints: (builder) => ({
    // Contacts endpoints
    getEmailContacts: builder.query({
      query: ({ search = "", limit = 20, offset = 0, status = null }) => ({
        url: "/api/news-letter/email-contact/all",
        params: { search, limit, offset, status },
      }),
      providesTags: ["EmailContacts"],
      transformResponse: (response) => ({
        count: response.totalCounts,
        data: response.data,
      }),
    }),

    createEmailContact: builder.mutation({
      query: (contactData) => ({
        url: "/api/news-letter/email-contact/save",
        method: "POST",
        body: contactData,
      }),
      invalidatesTags: ["EmailContacts"],
    }),

    updateEmailContact: builder.mutation({
      query: ({ id, contactData }) => ({
        url: `/api/news-letter/email-contact/update/${id}`,
        method: "PUT",
        body: contactData,
      }),
      invalidatesTags: ["EmailContacts"],
    }),

    deleteEmailContact: builder.mutation({
      query: (id) => ({
        url: `/api/news-letter/email-contact/delete/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: ["EmailContacts"],
    }),

    // Groups endpoints
    getEmailGroups: builder.query({
      query: () => "/api/news-letter/contact-group/all",
      providesTags: ["EmailGroups"],
    }),

    createEmailGroup: builder.mutation({
      query: (groupData) => ({
        url: "/api/news-letter/contact-group/save",
        method: "POST",
        body: groupData,
      }),
      invalidatesTags: ["EmailGroups"],
    }),

    updateEmailGroup: builder.mutation({
      query: ({ id, groupData }) => ({
        url: `/api/news-letter/contact-group/update/${id}`,
        method: "PUT",
        body: groupData,
      }),
      invalidatesTags: ["EmailGroups"],
    }),

    deleteEmailGroup: builder.mutation({
      query: (id) => ({
        url: `/api/news-letter/groups/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: ["EmailGroups"],
    }),

    addContactsToGroup: builder.mutation({
      query: ({ groupId, contactIds }) => ({
        url: `/api/news-letter/groups/${groupId}/contacts`,
        method: "POST",
        body: { contactIds },
      }),
      invalidatesTags: ["EmailGroups", "EmailContacts"],
    }),

    removeContactsFromGroup: builder.mutation({
      query: ({ groupId, contactIds }) => ({
        url: `/api/news-letter/groups/${groupId}/contacts`,
        method: "DELETE",
        body: { contactIds },
      }),
      invalidatesTags: ["EmailGroups", "EmailContacts"],
    }),

    // Templates endpoints
    getEmailTemplates: builder.query({
      query: () => "/api/news-letter/templates",
      providesTags: ["EmailTemplates"],
    }),

    getEmailTemplate: builder.query({
      query: (id) => `/api/news-letter/templates/${id}`,
      providesTags: (result, error, id) => [{ type: "EmailTemplates", id }],
    }),

    // Campaign endpoints
    sendEmailCampaign: builder.mutation({
      query: (campaignData) => ({
        url: "/api/news-letter/campaigns",
        method: "POST",
        body: campaignData,
      }),
      invalidatesTags: ["EmailCampaigns"],
    }),

    scheduleEmailCampaign: builder.mutation({
      query: (campaignData) => ({
        url: "/api/news-letter/campaigns/schedule",
        method: "POST",
        body: campaignData,
      }),
      invalidatesTags: ["EmailCampaigns"],
    }),

    getCampaigns: builder.query({
      query: ({ limit = 20, offset = 0, status = null }) => ({
        url: "/api/news-letter/campaigns",
        params: { limit, offset, status },
      }),
      providesTags: ["EmailCampaigns"],
    }),

    getCampaignDetails: builder.query({
      query: (id) => `/api/news-letter/campaigns/${id}`,
      providesTags: (result, error, id) => [{ type: "EmailCampaigns", id }],
    }),

    // Analytics endpoints
    getCampaignAnalytics: builder.query({
      query: (campaignId) =>
        `/api/news-letter/analytics/campaign/${campaignId}`,
      providesTags: ["EmailAnalytics"],
    }),

    getOverallAnalytics: builder.query({
      query: ({ startDate, endDate }) => ({
        url: "/api/news-letter/analytics/overall",
        params: { startDate, endDate },
      }),
      providesTags: ["EmailAnalytics"],
    }),
  }),
});

// Export hooks for usage in functional components
export const {
  // Contacts hooks
  useGetEmailContactsQuery,
  useCreateEmailContactMutation,
  useUpdateEmailContactMutation,
  useDeleteEmailContactMutation,

  // Groups hooks
  useGetEmailGroupsQuery,
  useCreateEmailGroupMutation,
  useUpdateEmailGroupMutation,
  useDeleteEmailGroupMutation,
  useAddContactsToGroupMutation,
  useRemoveContactsFromGroupMutation,

  // Templates hooks
  useGetEmailTemplatesQuery,
  useGetEmailTemplateQuery,

  // Campaign hooks
  useSendEmailCampaignMutation,
  useScheduleEmailCampaignMutation,
  useGetCampaignsQuery,
  useGetCampaignDetailsQuery,

  // Analytics hooks
  useGetCampaignAnalyticsQuery,
  useGetOverallAnalyticsQuery,
} = emailMarketingApi;
