import React, { useState, useRef } from "react";
import {
  CropperPreview,
  FixedCropper,
  ImageRestriction,
  useCropperImage,
} from "react-advanced-cropper";
import {
  getAbsoluteZoom,
  getZoomFactor,
} from "advanced-cropper/extensions/absolute-zoom";
import "react-advanced-cropper/dist/style.css";
import Button from "../../parts/Button";
import {
  setCroppedImage,
  setEditCoverImg,
} from "../../store/slices/storiesSlice";

import {
  setCroppedImage as setWebCroppedImage,
  setEditCoverImg as setWebEditCoverImg,
} from "../../store/slices/webstorySlice";
import { useDispatch } from "react-redux";
import { base64ToBlob, base64ToFile } from "../../utils/helperFunctions";

const ImageCropper = ({ image, type }) => {
  const [coordinates, setCoordinates] = useState({
    left: 150,
    top: 100,
    width: 650,
    height: 100,
  });
  const [zoom, setZoom] = useState(0); // State to track the current zoom level
  const dispatch = useDispatch();
  const previewRef = useRef(null);
  const cropperRef = useRef(null);
  // const dd = useCropperImage({ src: image });

  const onChange = (cropper) => {
    setCoordinates(cropper.getCoordinates());
  };

  const onUpdate = (cropper) => {
    previewRef.current?.update(cropper);
  };

  const handleZoomChange = (value) => {
    const cropper = cropperRef.current;
    if (cropper) {
      const state = cropper.getState();
      const settings = cropper.getSettings();
      const zoomFactor = getZoomFactor(state, settings, value);
      cropper.zoomImage(zoomFactor, { transitions: true });
      setZoom(value); // Update the zoom state
    }
  };

  const handleImgCrop = () => {
    const blobImg = base64ToFile(
      cropperRef.current.getCanvas()?.toDataURL(),
      "image.jpg"
    );
    if (type === "stories") {
      dispatch(setCroppedImage([blobImg]));
      dispatch(setEditCoverImg(false));
    } else if (type === "webStory") {
      dispatch(setWebCroppedImage([blobImg]));
      dispatch(setWebEditCoverImg(false));
    }
  };

  return (
    <div>
      <div className="w-full flex gap-x-12">
        <CropperPreview ref={previewRef} className="preview" />
        <div className="w-3/4 h-[25rem]">
          <FixedCropper
            ref={cropperRef}
            backgroundClassName="bg-white"
            src={image}
            onInteractionEnd={onChange}
            onUpdate={onUpdate}
            stencilSize={{
              width: 300,
              height: 300,
            }}
            defaultPosition={{
              left: coordinates.left,
              top: coordinates.top,
            }}
            defaultSize={{
              width: coordinates.width,
              height: coordinates.height,
            }}
            stencilProps={{
              aspectRatio: 1 / 1,
              grid: false,
              handlers: false,
              lines: false,
              movable: false,
              resizable: false,
            }}
            crossOrigin="anonymous"
            imageRestriction={ImageRestriction.stencil}
          />
          <div className="flex items-center gap-x-4 mt-4 w-full ">
            <label htmlFor="zoom-slider">Zoom:</label>
            <input
              id="zoom-slider"
              type="range"
              min={0}
              max={1}
              step={0.01}
              className="w-1/2"
              value={zoom}
              onChange={(e) => handleZoomChange(Number(e.target.value))}
            />
          </div>
        </div>
      </div>
      {/* Zoom Slider */}

      <div className="flex items-center gap-x-4 justify-end w-full mt-10">
        <Button rounded="full" onClick={handleImgCrop}>
          Crop
        </Button>
        <Button
          rounded="full"
          variant="outline"
          onClick={() => {
            if (type === "stories") {
              dispatch(setEditCoverImg(false));
            } else if (type === "webStory") {
              dispatch(setWebEditCoverImg(false));
            }
          }}
        >
          Cancel
        </Button>
      </div>
      <div id="preview-container"></div>
    </div>
  );
};

export default ImageCropper;
