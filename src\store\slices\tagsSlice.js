import { createSlice } from "@reduxjs/toolkit";
import {
  incrementOffsetState,
  resetFiltersState,
  setCanonicalState,
  setImageState,
  setIndexingState,
  setKeyWordsState,
  setMetaDataState,
  setMetaDataTemplateData,
  setOgContentState,
  setXContentState,
  storiesInputFilterState,
} from "./sharedReducers";
import {
  formatTagTitle,
  generateSlugStories,
} from "../../utils/helperFunctions";

const initialState = {
  name: "",
  _id: null,
  description: "",
  errors: {},
  filter: { search: "" },
  limit: 20,
  offset: 0,

  storiesState: {
    meta: {
      title: null,
      description: null,
      keywords: [],
      slug: null,
      Tag: null,
      canonical: null,
      robots: "index,follow",
      _id: null,
    },
    ogImg: [],
    imgsrc: [],
    twitterImg: [],
    og: {
      title: null,
      description: null,
    },
    twitter: {
      title: null,
      description: null,
      card: "large",
    },
  },
};

export const tagsSlice = createSlice({
  name: "tags",
  initialState,
  reducers: {
    setTagsInputFilter: (state, { payload }) =>
      storiesInputFilterState(state, payload),
    resetFilters: (state, { payload }) => resetFiltersState(state),
    incrementOffset: (state) => incrementOffsetState(state),

    // set meta data here
    setTagMetaData: (state, { payload }) => setMetaDataState(state, payload),

    // set keywords here
    setTagKeyWords: (state, { payload }) => setKeyWordsState(state, payload),

    // set the canonical here
    setTagCanonical: (state, { payload }) => setCanonicalState(state, payload),

    // set indexing
    setTagIndexing: (state, { payload }) => setIndexingState(state, payload),
    //set twitter and og image
    setTagImage: (state, { payload }) => setImageState(state, payload),

    // set og content
    setTagOgContent: (state, { payload }) => setOgContentState(state, payload),
    // set x content
    setTagXContent: (state, { payload }) => setXContentState(state, payload),
    setInputData: (state, { payload }) => {
      const { name, value } = payload;
      state.storiesState[name] = value;
    },

    setTagProfile: (state, { payload }) => {
      const { name, value } = payload;
      state[name] = value;
    },

    setErrors: (state, action) => {
      state.errors = action.payload; // Set validation errors
    },
    clearErrors: (state) => {
      state.errors = {};
    },
    setTagsData: (state, { payload }) => {
      const { _id, name, description = "", slug, meta } = payload;
      if (payload) {
        state._id = _id;
        state.name = name;
        state.description = meta?.description;
        state.storiesState.meta = {
          ...state.storiesState.meta,
          title: meta?.title,
          description: meta?.description,
          keywords: meta?.keywords,
          slug: generateSlugStories(slug),
          Tag: meta?.Tag,
          canonical: meta?.canonical,
          robots: meta?.robots,
          _id: meta?._id,
        };

        state.storiesState.twitter = {
          ...state.storiesState.twitter,
          title: meta?.twitter?.title || null,
          description: meta?.twitter?.description || null,
          card:
            meta?.twitter?.card === "summary_large_image" ? "large" : "small",
        };
        state.storiesState.twitterImg = meta.twitter?.image || [];

        state.storiesState.ogImg = meta.og?.image || [];
        state.storiesState.og = {
          ...state.storiesState.og,
          title: meta.title || null,
          description: meta.description || null,
        };
      }
    },
    setTagsMetaTemplateData: (state, { payload }) =>
      setMetaDataTemplateData(state, payload),
    replaceTagMetaData: (state, { payload }) => {
      const { name, value } = payload;
      state.storiesState.meta[name] = value;
    },
    resetState: () => initialState,
  },
});
// Action creators are generated for each case reducer function
export const {
  replaceTagMetaData,
  setTagsMetaTemplateData,
  setTagMetaData,
  setTagCanonical,
  setTagImage,
  setTagIndexing,
  setTagKeyWords,
  setTagOgContent,
  setTagXContent,
  setTagProfile,
  setErrors,
  clearErrors,
  setInputData,
  setTagsData,
  resetState,
  incrementOffset,
  resetFilters,
  setTagsInputFilter,
} = tagsSlice.actions;

export default tagsSlice.reducer;
