import React, { useState, useRef, useEffect } from "react";
import <PERSON><PERSON><PERSON> from "cropperjs";
import "cropperjs/dist/cropper.css";
import { FiUpload, FiZoomIn, FiZoomOut, FiSave } from "react-icons/fi";

const ProfileImagePicker = ({
  existingImageUrl = null,
  savedCoordinates = null,
  onSave = () => {},
}) => {
  const [image, setImage] = useState(existingImageUrl);
  const [cropData, setCropData] = useState(null);
  const imageRef = useRef(null);
  const cropperRef = useRef(null);
  const fileInputRef = useRef(null);

  useEffect(() => {
    if (image && imageRef.current) {
      const initCropper = () => {
        if (cropperRef.current) {
          cropperRef.current.destroy();
        }

        cropperRef.current = new Cropper(imageRef.current, {
          aspectRatio: 1,
          viewMode: 1,
          dragMode: "move",
          autoCropArea: 1,
          crop(event) {
            const data = event.detail;
            setCropData({
              x: Math.round(data.x),
              y: Math.round(data.y),
              scaleX: Math.round(data.scaleX * 100) / 100,
              scaleY: Math.round(data.scaleY * 100) / 100,
            });
          },
          ready() {
            if (savedCoordinates) {
              cropperRef.current.setData({
                x: savedCoordinates.x,
                y: savedCoordinates.y,
              });
              cropperRef.current.scale(
                savedCoordinates.scaleX,
                savedCoordinates.scaleY
              );
            }
          },
        });
      };

      initCropper();
    }

    return () => {
      if (cropperRef.current) {
        cropperRef.current.destroy();
      }
    };
  }, [image, savedCoordinates]);

  const handleFileChange = (e) => {
    e.preventDefault();
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = () => {
        setImage(reader.result);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleZoom = (zoomIn = true) => {
    if (cropperRef.current) {
      const value = zoomIn ? 0.1 : -0.1;
      cropperRef.current.zoom(value);
    }
  };

  const handleSave = () => {
    if (cropperRef.current) {
      const coordinates = {
        x: cropData.x,
        y: cropData.y,
        scaleX: cropData.scaleX,
        scaleY: cropData.scaleY,
      };

      const croppedCanvas = cropperRef.current.getCroppedCanvas({
        width: 300,
        height: 300,
      });

      const croppedImageUrl = croppedCanvas.toDataURL("image/jpeg", 0.8);

      onSave({
        coordinates,
        croppedImage: croppedImageUrl,
        originalImage: image,
      });
    }
  };

  const triggerFileInput = () => {
    fileInputRef.current?.click();
  };

  return (
    <div className="w-full max-w-md mx-auto p-4 space-y-4">
      {!image ? (
        <div className="flex items-center justify-center w-full">
          <label className="flex flex-col items-center justify-center w-full h-64 border-2 border-dashed border-gray-300 rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100">
            <div className="flex flex-col items-center justify-center pt-5 pb-6">
              <FiUpload className="w-8 h-8 mb-4 text-gray-500" />
              <p className="mb-2 text-sm text-gray-500">
                <span className="font-semibold">Click to upload</span> or drag
                and drop
              </p>
            </div>
            <input
              ref={fileInputRef}
              type="file"
              className="hidden"
              accept="image/*"
              onChange={handleFileChange}
            />
          </label>
        </div>
      ) : (
        <div className="space-y-4">
          <div className="relative h-64 overflow-hidden border rounded-lg">
            <img
              ref={imageRef}
              src={image}
              alt="Profile"
              className="max-w-full h-auto"
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <button
                onClick={() => handleZoom(false)}
                className="p-2 rounded-full hover:bg-gray-100"
                title="Zoom Out"
              >
                <FiZoomOut className="w-5 h-5" />
              </button>
              <button
                onClick={() => handleZoom(true)}
                className="p-2 rounded-full hover:bg-gray-100"
                title="Zoom In"
              >
                <FiZoomIn className="w-5 h-5" />
              </button>
            </div>

            <div className="flex items-center space-x-2">
              <button
                onClick={triggerFileInput}
                className="flex items-center px-3 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200"
              >
                <FiUpload className="w-4 h-4 mr-2" />
                Change Image
              </button>
              <button
                onClick={handleSave}
                className="flex items-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600"
              >
                <FiSave className="w-4 h-4 mr-2" />
                Save
              </button>
            </div>
          </div>

          {cropData && (
            <div className="p-4 bg-gray-50 rounded-lg">
              <h3 className="font-medium mb-2">Crop Coordinates:</h3>
              <pre className="text-sm overflow-x-auto">
                {JSON.stringify(cropData, null, 2)}
              </pre>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default ProfileImagePicker;
