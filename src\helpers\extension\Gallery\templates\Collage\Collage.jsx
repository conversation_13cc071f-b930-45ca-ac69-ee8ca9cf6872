import React from "react";
import PropTypes from "prop-types";

const Collage = ({
  data = [],
  properties = {
    direction: "vertical", // scroll direction
    orientation: "vertical", // image shape
    height: 300,
    spacing: 5,
  },
}) => {
  const { direction, orientation, height, spacing } = properties;

  const isScrollHorizontal = direction === "horizontal";
  const isPortrait = orientation === "vertical";

  const aspectRatio = isPortrait ? "2 / 3" : "16 / 9";

  return (
    <div
      className={`w-full ${
        isScrollHorizontal ? "flex overflow-x-auto" : "grid"
      }`}
      style={{
        gap: `${spacing}px`,
        gridTemplateColumns: !isScrollHorizontal
          ? "repeat(auto-fill, minmax(200px, 1fr))"
          : undefined,
      }}
    >
      {data.map((item, index) => (
        <div
          key={`collage-item-${index}`}
          className={`relative ${
            isScrollHorizontal ? "flex-shrink-0" : "w-full"
          } overflow-hidden`}
          style={{
            width: isScrollHorizontal
              ? isPortrait
                ? height // vertical portrait = column width
                : "auto"
              : "100%",
            height: isScrollHorizontal
              ? isPortrait
                ? "auto"
                : height // horizontal landscape = row height
              : "auto",
            aspectRatio,
          }}
        >
          <img
            src={item?.src || ""}
            alt={item?.alt || `Collage Image ${index}`}
            className="absolute inset-0 w-full h-full object-cover"
            onError={(e) => (e.target.style.display = "none")}
          />
        </div>
      ))}
    </div>
  );
};

Collage.propTypes = {
  data: PropTypes.arrayOf(
    PropTypes.shape({
      src: PropTypes.string.isRequired,
      alt: PropTypes.string,
    })
  ),
  properties: PropTypes.shape({
    direction: PropTypes.oneOf(["vertical", "horizontal"]),
    orientation: PropTypes.oneOf(["vertical", "horizontal"]),
    height: PropTypes.number,
    spacing: PropTypes.number,
  }),
};

export default Collage;
