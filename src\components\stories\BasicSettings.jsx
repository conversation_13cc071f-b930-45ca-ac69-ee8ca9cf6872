import React, { useC<PERSON>back, useEffect, useRef, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  setContributors,
  setCoverImg,
  setCroppedImage,
  setEditCoverImg,
  setImage,
  setIsPromotional,
  setOgContent,
  setSettingsData,
  setWriter,
  setXContent,
} from "../../store/slices/storiesSlice";
import { Input } from "../../parts/FormComponents";
import AsyncSelect from "react-select/async";
import { RxCross2 } from "react-icons/rx";
import { CustomOption } from "../../utils/TransformData";
import {
  useGetAuthorsListQuery,
  useLazyGetAuthorsListQuery,
} from "../../store/apis/authorsApi";
import { BiX } from "react-icons/bi";
import { useLocation } from "react-router-dom";
import { folderPath } from "../../utils/constants";
import ImageMediaDrop from "./ImageMediaDrop";
import ToggleSwitch from "../../parts/ToggleSwitch";

const BasicSettings = () => {
  const dispatch = useDispatch();
  const { pathname } = useLocation();
  const debounceTimerRef = useRef(null);
  const [savedCoordinates, setSavedCoordinates] = useState(null);
  const {
    storiesState,
    croppedImg,
    storiesState: {
      writer,
      contributors: intialContributors,
      coverImgFocalPosition: { scale, top, left },
    },
  } = useSelector((state) => state.stories);
  const [selectedFiles, setSelectedFiles] = useState(storiesState.coverImg);
  const [contributors, setContributorsState] = useState(
    intialContributors || []
  );
  const [newContributor, setNewContributor] = useState(""); // Track current contributor input
  const [authorsState, setAuthorsState] = useState(
    writer.map((author) => ({
      label: `${author.firstname} ${author?.lastname || ""}`,
      value: author._id,
    }))
  );

  const [triggerSearch] = useLazyGetAuthorsListQuery();

  // Debounce function for search input
  const debounce = useCallback((func, delay) => {
    return function (...args) {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
      debounceTimerRef.current = setTimeout(() => func(...args), delay);
    };
  }, []);

  const loadOptions = async (inputValue, callback) => {
    try {
      const response = await triggerSearch({ search: inputValue }).unwrap();
      const options = response.data.map((author) => ({
        label: author.name,
        value: author._id,
      }));
      callback(options);
    } catch (error) {
      callback([]);
    }
  };

  const handleAuthorSelectChange = (selected) => {
    console.log("Hello");

    setAuthorsState(selected);
    const payloadData = (selected || []).map((author) => author.value);
    dispatch(setWriter(payloadData));
  };

  // Handle contributor input change
  const handleContributorInputChange = (value) => {
    setNewContributor(value); // Update the new contributor input field
  };

  // Handling contributor addition (only when Enter is pressed)
  const handleAddContributor = () => {
    const value = newContributor.trim();
    if (value && !contributors.includes(value)) {
      const updatedContributors = [...contributors, value];
      setContributorsState(updatedContributors);
      dispatch(setContributors(updatedContributors)); // Update Redux state
      setNewContributor(""); // Reset input after adding
    }
  };

  // Handling contributor removal
  const handleRemoveContributor = (value) => {
    const updatedContributors = contributors.filter((chip) => chip !== value);
    setContributorsState(updatedContributors);
    dispatch(setContributors(updatedContributors)); // Update Redux state
  };

  const handleDataChange = ({ name, value }) => {
    dispatch(setSettingsData({ name, value }));
  };

  useEffect(() => {
    dispatch(setCoverImg(selectedFiles));
    dispatch(setImage({ name: "ogImg", value: selectedFiles }));
    dispatch(setImage({ name: "twitterImg", value: selectedFiles }));
  }, [selectedFiles]);
  useEffect(() => {
    const loadInitialAuthors = async () => {
      if (writer && writer.length > 0) {
        try {
          // Optional: avoid refetching if already set
          if (authorsState.length > 0) return;

          const response = await triggerSearch({ search: "" }).unwrap(); // fetch all available authors
          const options = response.data.map((author) => ({
            label: author.name,
            value: author._id,
          }));

          // Filter those that match writer IDs
          const selectedAuthors = options.filter((opt) =>
            writer.includes(opt.value)
          );
          setAuthorsState(selectedAuthors);
        } catch (error) {
          console.error("Failed to load selected authors:", error);
        }
      }
    };

    loadInitialAuthors();
  }, [writer]);

  const handleSave = (data) => {
    console.log(data, " save data");
  };

  const getPreviewStyle = () => {
    const ratio = 96 / 400;
    return {
      transform: `scale(${scale}) translate(${left * ratio}px, ${
        top * ratio
      }px)`,
      width: "100%",
      height: "100%",
      transition: "all 0.1s",
      objectFit: "contain",
    };
  };
  return (
    <>
      <ImageMediaDrop
        selectedFiles={selectedFiles}
        setSelectedFiles={setSelectedFiles}
        isEdit={true}
        onSave={() => dispatch(setEditCoverImg(true))}
        onEditClick={() => dispatch(setEditCoverImg(true))}
        customImgClass={getPreviewStyle()}
        customClasses={
          "w-11/12 mx-auto h-[180px] overflow-hidden bg-gray-100 relative "
        }
        folderPath={folderPath.stories}
      />
      <div className="mt-4 ">
        <div className="flex relative flex-col gap-y-4">
          {croppedImg && croppedImg.length > 0 ? (
            <>
              <label
                htmlFor="cropImg"
                className="text-sm text-gray-600 flex items-center"
              >
                Cropped Image Preview
              </label>
              <BiX
                onClick={() => dispatch(setCroppedImage(null))}
                className="absolute right-16 top-5 bg-lightBlue rounded-full  text-2xl text-primaryLight hover:text-dangerHover cursor-pointer transition-all duration-500"
              />
              <img
                src={
                  typeof croppedImg === "string"
                    ? croppedImg
                    : URL.createObjectURL(croppedImg[0])
                }
                alt="Cropped Image"
                className="h-48 w-48 mx-auto"
              />
            </>
          ) : null}
        </div>
      </div>

      <div>
        <div className="flex flex-col gap-y-4 mt-4">
          <Input
            label="Image Caption"
            name="caption"
            id="caption"
            value={storiesState.caption}
            placeholder="Add an image caption here"
            onDebouncedChange={(value) =>
              handleDataChange({ value, name: "caption" })
            }
            type="text"
          />
          <Input
            label="Image courtesy"
            name="courtesy"
            id="courtesy"
            value={storiesState.courtesy}
            placeholder="Add an image courtesy here"
            onDebouncedChange={(value) =>
              handleDataChange({ value, name: "courtesy" })
            }
            type="text"
          />
          <Input
            label="Alt Name"
            name="altName"
            value={storiesState.altName}
            id="altName"
            onDebouncedChange={(value) =>
              handleDataChange({ value, name: "altName" })
            }
            placeholder="e.g. A cat sleeping on a white blanket"
            type="text"
          />
          <Input
            label="Timestamp"
            name="publishDate"
            value={storiesState.publishDate}
            onDebouncedChange={(value) =>
              handleDataChange({ value, name: "publishDate" })
            }
            id="publishDate"
            placeholder="Add a read time"
            type="number"
          />
          <div>
            <div className="text-sm text-fadeGray mb-3">Authors</div>
            {/* <Select
              // defaultValue={[colourOptions[2], colourOptions[3]]}
              isMulti
              value={authorsState}
              name="authors"
              options={
                data
                  ? data.data.map((author) => ({
                      value: author._id,
                      label: author.name,
                      image: author.imgsrc,
                    }))
                  : []
              }
              className="basic-multi-select focus:outline-none active:outline-none focus:border-primary focus:!border"
              classNamePrefix="select"
              onChange={handleAuthorSelectChange}
              components={{
                Option: CustomOption,
              }}
            /> */}
            <AsyncSelect
              isMulti
              cacheOptions
              defaultOptions
              isClearable
              value={authorsState}
              loadOptions={debounce(loadOptions, 300)}
              onChange={handleAuthorSelectChange}
              className="basic-multi-select focus:outline-none active:outline-none focus:border-primary focus:!border"
              classNamePrefix="select"
              placeholder="Select an author..."
              components={{ Option: CustomOption }}
            />
          </div>

          {/* Input for adding contributors */}
          <Input
            label="Contributors"
            name="contributors"
            id="contributors"
            placeholder="Add contributors"
            type="text"
            value={newContributor}
            onDebouncedChange={(value) => handleContributorInputChange(value)} // Use debounced change for contributor input
            onEnter={handleAddContributor} // Add contributor when Enter is pressed
          />

          {/* Chips for contributors */}
          <div className="flex flex-wrap gap-2">
            {contributors.map((contributor, index) => (
              <span
                key={index}
                className="flex items-center gap-2 px-3 py-1 rounded-full hover:bg-blue-700 bg-primary text-white font-medium text-sm border cursor-pointer"
                onClick={() => handleRemoveContributor(contributor)} // Remove chip on click
              >
                {contributor}
                <RxCross2 className="text-[16px]" />
              </span>
            ))}
          </div>

          <div className="-mt-3">
            <Input
              label="Excerpt"
              name="excerpt"
              id="excerpt"
              rows={3}
              value={storiesState.excerpt}
              extraArg="140 characters"
              placeholder="e.g. This quick and easy pasta dish is great for busy weeknight family meals"
              isTextarea={true}
              onDebouncedChange={(value) => {
                handleDataChange({ value, name: "excerpt" });
                if (pathname === "/admin/stories/create") {
                  dispatch(setOgContent({ value, name: "description" }));
                  dispatch(setXContent({ value, name: "description" }));
                }
              }}
            />
          </div>
          <Input
            label="Content Duplication Note"
            name="duplicationNote"
            value={storiesState.duplicationNote}
            onDebouncedChange={(value) =>
              handleDataChange({ value, name: "duplicationNote" })
            }
            id="duplicationNote"
            placeholder="e.g. This article was originally published on XYZ"
          />
          <div className="flex items-center gap-x-2">
            <ToggleSwitch
              checked={storiesState.isPromotional}
              onChange={() =>
                dispatch(setIsPromotional(!storiesState.isPromotional))
              }
            />
            <div>Is Advertorial</div>
          </div>
        </div>
      </div>
    </>
  );
};

export default BasicSettings;
