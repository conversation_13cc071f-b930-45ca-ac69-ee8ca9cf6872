import React, { useCallback, useEffect, useState } from "react";
import { DebouncedInput } from "../../parts/FormComponents";
import { useDispatch, useSelector } from "react-redux";
import Button from "../../parts/Button";
import { RxCross2 } from "react-icons/rx";
import {
	resetFilters,
	resetSubCategoryFilters,
	setFetchedData,
	setSubCategoryFetchedData,
	setSubCategoryFilter,
	setSubCategoryInputFilter,
	setSubCategoryOffset,
} from "../../store/slices/categoriesSlice";
import { toast } from "react-toastify";
import { useNavigate } from "react-router-dom";
import useInfiniteScrollData from "../../utils/useInfiniteScrollData";
import { apiEndpoints } from "../../utils/constants";
import { usePostStoriesMutation } from "../../store/apis/storiesApi";
import { useInView } from "react-intersection-observer";
import { <PERSON><PERSON><PERSON>paperThin, PiVideoThin } from "react-icons/pi";
import { BiPlus } from "react-icons/bi";
import { useUpdateSubCatArticleMutation } from "../../store/apis/categoriesApi";
import Loader, { AnimatedButtonLoader } from "../../parts/Loader";
import { setFetchedData as setFlaggedFetchedData } from "../../store/slices/flaggedStoriesSlice";

const AddArticle = ({
	setShowSideMenu,
	subcategoryIds,
	level = null,
	type = null,
	id,
	refetch,
	setRefetch,
}) => {
	const dispatch = useDispatch();
	const navigate = useNavigate();
	const [activeButtonId, setActiveButtonId] = useState(null);
	const { subCategoriesData, subCategoryFilter, subCategoriesHasMore, subCategoryOffset } =
		useSelector((state) => state.categories);

	const [postStories, { isLoading, isError, error, isFetching }] = usePostStoriesMutation();
	const [updateSubCatArticle, { isLoading: isUpdating }] = useUpdateSubCatArticleMutation();

	const fetchData = useCallback(
		async (forceReset = false) => {
			if (!subCategoriesHasMore && !forceReset) return;

			try {
				const currentOffset = forceReset ? 0 : subCategoryOffset;

				const response = await postStories({
					data: {
						filter: {
							search: subCategoryFilter.search,
							type: parseInt(type, 10),
							level: parseInt(level, 10),
							id: subCategoryFilter.id,
						},
						offset: currentOffset,
						limit: 20,
					},
					url: apiEndpoints.postCatSubArticle,
				}).unwrap();

				const responseData = response.data?.data ?? response.data;
				dispatch(
					setSubCategoryFetchedData({
						data: responseData,
						replace: forceReset || currentOffset === 0,
					})
				);
			} catch (fetchError) {
				console.error("Failed to fetch items", fetchError);
			}
		},
		[
			dispatch,
			subCategoryFilter,
			subCategoryOffset,
			subCategoriesHasMore,
			postStories,
			subCategoriesData,
		]
	);

	// Update the search handler to use the new reset functionality

	useEffect(() => {
		if (subCategoryFilter.id) {
			fetchData(true);
		}
	}, [subCategoryOffset, subCategoryFilter, level, type]);

	useEffect(() => {
		const shouldReset =
			subCategoryFilter.search === null ||
			subCategoryFilter.search ||
			subCategoryFilter.search === "";

		if (shouldReset && subCategoryFilter.id) {
			fetchData(shouldReset);
		}
	}, [subCategoryFilter.search]);

	useEffect(() => {
		if (subcategoryIds) {
			dispatch(
				setSubCategoryInputFilter({
					name: "id",
					value: subcategoryIds.toString(),
				})
			);
		}
	}, [subcategoryIds]);

	if (isError) {
		if (error.status === 401) {
			toast.error("Session Expired", { toastId: "video_story_fetch_error" });
			navigate("/signin");
		}
	}

	const { ref, inView } = useInView({
		threshold: 1.0,
		rootMargin: "20px",
		triggerOnce: false,
	});

	useEffect(() => {
		if (inView && !isFetching && subCategoriesHasMore) {
			dispatch(setSubCategoryOffset());
		}
	}, [inView, isFetching, subCategoriesHasMore, dispatch]);

	const handleClick = (rowId, rowType) => {
		setActiveButtonId(rowId);
		const payload = {
			id: id[0],
			type: parseInt(type ? type : rowType),
			level: parseInt(level),
		};

		updateSubCatArticle({ data: payload, id: rowId })
			.unwrap()
			.then((res) => {
				setActiveButtonId(null);
				if (res.status === "success") {
					console.log(" i am updated");
					toast.success("Story added successfully!");
					fetchData(true);
					setSubCategoryFetchedData({
						data: [],
						replace: false,
					});
					setFlaggedFetchedData({
						data: [],
						replace: false,
					});
					setRefetch(!refetch);
				} else {
					toast.error("Failed to add story.");
				}
			})
			.catch((err) => {
				setActiveButtonId(null);
				toast.error("Failed to add story.");
				setRefetch(!refetch);
				console.log(err);
			});
	};

	return (
		<div className="relative h-full ">
			<div className="w-full flex items-center justify-between border-b p-4">
				<div className="flex gap-x-10 items-center w-1/2">
					{/* <h3>Add Stories</h3> */}
					<DebouncedInput
						customClass="!w-full"
						type="text"
						value={subCategoryFilter.search ?? ""}
						onChange={(value) => {
							dispatch(setSubCategoryInputFilter({ name: "search", value }));
						}}
						placeholder="Search..."
						className="border shadow rounded w-full md:w-auto"
					/>
				</div>
				<RxCross2
					className="text-2xl text-fadeGray hover:cursor-pointer"
					onClick={() => setShowSideMenu(false)}
				/>
			</div>
			<div className="flex flex-col gap-y-4 h-full overflow-y-scroll p-4 scrollbar pb-28">
				{subCategoriesData.map((row, index) => (
					<div
						key={row._id}
						ref={index === subCategoriesData.length - 1 ? ref : null}
						className="flex items-center justify-between w-full"
					>
						<div className="flex items-center gap-x-5 w-full">
							<div>
								{row.type === 0 ? (
									<PiNewspaperThin className="text-xl text-primary" />
								) : (
									<PiVideoThin className="text-xl text-primary" />
								)}
							</div>
							<div className="flex items-center gap-x-2 font-semibold">
								<img src={row.coverImg} alt="" className="w-[80px] h-[60px] object-cover rounded" />
								<div>
									<div className="line-clamp-2 font-normal text-sm">{row.title}</div>
									<div className="text-[#969696] text-xs font-normal flex items-center space-x-2">
										<span>{new Date(row.timestamp).getHours()} hours ago,</span>
										<span>{row.author?.toString().split(" ").join(", ")}</span>
									</div>
								</div>
							</div>
						</div>
						{activeButtonId === row._id ? (
							<div className="inline-flex items-center justify-start px-4">
								<AnimatedButtonLoader />
							</div>
						) : (
							<Button rounded="full" size="sm" onClick={() => handleClick(row._id, row.type)}>
								<BiPlus /> Add
							</Button>
						)}
					</div>
				))}
				{isFetching || (isUpdating && <AnimatedButtonLoader />)}
			</div>
		</div>
	);
};

export default AddArticle;
