import { useCallback, useState } from "react";
import { useDispatch } from "react-redux";
import { toast } from "react-toastify";
import {
	useCreateFolderMutation,
	useDeleteFolderMutation,
	useGetFolderListMutation,
} from "../../../store/apis/mediaLibraryApi";
import {
	setFolders,
	setFolderPagination,
	navigateToFolder,
} from "../../../store/slices/mediaLibrarySlice";

/**
 * Hook for folder-specific operations
 * Handles folder creation, deletion, and navigation
 */
export const useFolderOperations = () => {
	const dispatch = useDispatch();
	const [showCreateFolder, setShowCreateFolder] = useState(false);
	const [newFolderName, setNewFolderName] = useState("");
	
	// API hooks
	const [createFolder, { isLoading: isCreatingFolder }] = useCreateFolderMutation();
	const [deleteFolder, { isLoading: isDeletingFolder }] = useDeleteFolderMutation();
	const [getFolderList] = useGetFolderListMutation();
	
	/**
	 * Create a new folder
	 */
	const handleCreateFolder = useCallback(async (folderData) => {
		try {
			const response = await createFolder(folderData).unwrap();
			
			if (response.status === "success") {
				toast.success("Folder created successfully");
				setNewFolderName("");
				setShowCreateFolder(false);
				
				// Refresh folder list
				await refreshFolders(folderData.parent);
				
				return response.data;
			} else {
				throw new Error(response.message || "Failed to create folder");
			}
		} catch (error) {
			console.error("Error creating folder:", error);
			toast.error(error.message || "Failed to create folder");
			throw error;
		}
	}, [createFolder]);
	
	/**
	 * Delete a folder
	 */
	const handleDeleteFolder = useCallback(async (folderId, parentId = null) => {
		try {
			const response = await deleteFolder(folderId).unwrap();
			
			if (response.status === "success") {
				toast.success("Folder deleted successfully");
				
				// Refresh folder list
				await refreshFolders(parentId);
				
				return true;
			} else {
				throw new Error(response.message || "Failed to delete folder");
			}
		} catch (error) {
			console.error("Error deleting folder:", error);
			toast.error(error.message || "Failed to delete folder");
			throw error;
		}
	}, [deleteFolder]);
	
	/**
	 * Refresh folders list
	 */
	const refreshFolders = useCallback(async (parentId = null) => {
		try {
			const response = await getFolderList({
				parent: parentId || "",
				limit: 20,
				offset: 0,
			}).unwrap();
			
			if (response.status === "success") {
				dispatch(setFolders(response.data || []));
				dispatch(setFolderPagination({
					offset: 20,
					hasMore: (response.data || []).length === 20,
					totalCount: response.totalCounts || 0,
				}));
			}
		} catch (error) {
			console.error("Error refreshing folders:", error);
			toast.error("Failed to refresh folders");
		}
	}, [dispatch, getFolderList]);
	
	/**
	 * Navigate to a folder
	 */
	const navigateToFolderAction = useCallback((folder, isRoot = false) => {
		dispatch(navigateToFolder({ folder, isRoot }));
	}, [dispatch]);
	
	/**
	 * Handle breadcrumb navigation
	 */
	const handleBreadcrumbClick = useCallback((index, folderHierarchy) => {
		if (index === 0) {
			// Navigate to root
			navigateToFolderAction(null, true);
		} else {
			// Navigate to specific folder in hierarchy
			const targetFolder = folderHierarchy[index - 1];
			navigateToFolderAction(targetFolder);
		}
	}, [navigateToFolderAction]);
	
	/**
	 * Handle folder double-click (enter folder)
	 */
	const handleFolderEnter = useCallback((folder) => {
		navigateToFolderAction(folder);
	}, [navigateToFolderAction]);
	
	/**
	 * Validate folder name
	 */
	const validateFolderName = useCallback((name) => {
		if (!name || name.trim().length === 0) {
			return "Folder name is required";
		}
		
		if (name.length > 255) {
			return "Folder name is too long";
		}
		
		// Check for invalid characters
		const invalidChars = /[<>:"/\\|?*]/;
		if (invalidChars.test(name)) {
			return "Folder name contains invalid characters";
		}
		
		return null;
	}, []);
	
	/**
	 * Create folder with validation
	 */
	const createFolderWithValidation = useCallback(async (name, parentId = null, level = 0) => {
		const validationError = validateFolderName(name);
		if (validationError) {
			toast.error(validationError);
			return false;
		}
		
		const folderData = {
			name: name.trim(),
			parent: parentId || "",
			level: level,
			path: parentId ? `${parentId}/${name.trim()}` : name.trim(),
		};
		
		try {
			await handleCreateFolder(folderData);
			return true;
		} catch (error) {
			return false;
		}
	}, [handleCreateFolder, validateFolderName]);
	
	/**
	 * Show create folder modal
	 */
	const showCreateFolderModal = useCallback(() => {
		setShowCreateFolder(true);
		setNewFolderName("");
	}, []);
	
	/**
	 * Hide create folder modal
	 */
	const hideCreateFolderModal = useCallback(() => {
		setShowCreateFolder(false);
		setNewFolderName("");
	}, []);
	
	/**
	 * Handle create folder form submission
	 */
	const handleCreateFolderSubmit = useCallback(async (currentFolder = null) => {
		if (!newFolderName.trim()) {
			toast.error("Please enter a folder name");
			return;
		}
		
		const success = await createFolderWithValidation(
			newFolderName,
			currentFolder?._id,
			currentFolder ? currentFolder.level + 1 : 0
		);
		
		if (success) {
			hideCreateFolderModal();
		}
	}, [newFolderName, createFolderWithValidation, hideCreateFolderModal]);
	
	return {
		// State
		showCreateFolder,
		newFolderName,
		setNewFolderName,
		
		// Loading states
		isCreatingFolder,
		isDeletingFolder,
		
		// Actions
		handleCreateFolder,
		handleDeleteFolder,
		refreshFolders,
		navigateToFolderAction,
		handleBreadcrumbClick,
		handleFolderEnter,
		
		// Validation
		validateFolderName,
		createFolderWithValidation,
		
		// Modal actions
		showCreateFolderModal,
		hideCreateFolderModal,
		handleCreateFolderSubmit,
	};
};
